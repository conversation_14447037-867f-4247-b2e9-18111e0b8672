Component({
  properties: {
    visible: {
      type: Boolean,
      value: false,
      observer: function(newVal) {
        if (newVal) {
          this.showPopup();
        } else {
          this.hidePopup();
        }
      }
    },
    title: {
      type: String,
      value: '标题'
    },
    position: {
      type: String,
      value: 'bottom' // bottom, center, top
    },
    round: {
      type: <PERSON>olean,
      value: true
    },
    closeOnClickOverlay: {
      type: Boolean,
      value: true
    },
    maxHeight: {
      type: String,
      value: '90%'
    },
    closeButtonPosition: {
      type: String,
      value: 'right' // right, left
    },
    showCustomUpload: {
      type: Boolean,
      value: false
    },
    customUploadText: {
      type: String,
      value: '自定义'
    }
  },

  data: {
    popupVisible: false,
    animating: false
  },

  methods: {
    showPopup() {
      // First make the container visible
      this.setData({ popupVisible: true });
      
      // Then after a small delay, add the visible class to trigger animations
      setTimeout(() => {
        this.setData({ animating: true });
      }, 30);
    },
    
    hidePopup() {
      // First remove the visible class to trigger animations
      this.setData({ animating: false });
      
      // Then after animation completes, hide the container
      setTimeout(() => {
        this.setData({ popupVisible: false });
      }, 350); // Match this to the longest transition duration
    },

    onClose() {
      this.hidePopup();
      this.triggerEvent('close');
    },

    onClickOverlay() {
      if (this.data.closeOnClickOverlay) {
        this.onClose();
      }
    },

    stopPropagation() {
      // Prevent event bubbling
    },
    
    // 触发自定义上传事件
    onCustomUpload() {
      this.triggerEvent('customUpload');
    }
  }
}) 