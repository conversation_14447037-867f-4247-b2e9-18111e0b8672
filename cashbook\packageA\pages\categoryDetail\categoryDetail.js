Page({
  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 20, // 默认状态栏高度，单位px
    navBarHeight: 64, // 默认导航栏总高度
    contentMarginTop: 64, // 默认内容区域上边距
    title: '收债',
    time: '2025-5',
    total: null,
    sortType: 'time', // 可选值: 'time', 'amount'
    list: [
      {
        isTransfer: true,
        time: '5月14日',
        istoday: '今天',
        desc: '收债',
        amount: '100',
        fromAccount: '邮',
        toAccount: '邮政',
        interest: '20.00',
        payAccount: '-20.00',
        date: '2023-05-21',
        icon: '/static/icon/check-circle.png',
        title: '收债'
      },
      {
        isTransfer: true,
        time: '5月20日',
        istoday: '今天',
        desc: '收债',
        amount: '100',
        fromAccount: '邮',
        toAccount: '邮政',
        interest: '20.00',
        payAccount: '-20.00',
        date: '2023-05-21',
        icon: '/static/icon/check-circle.png',
        title: '收债',
        remark: '弟',
      },
      {
        isTransfer: true,
        time: '5月21日',
        istoday: '今天',
        amount: '100',
        fromAccount: '邮',
        toAccount: '邮政',
        interest: '20.00',
        payAccount: '-20.00',
        date: '2023-05-21',
        icon: '/static/icon/check-circle.png',
        title: '收债'
      },
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    console.log('分类明细页面加载，参数:', options);
    
    // 获取状态栏高度
    this.getSystemInfo();
    
    // 如果有传递参数，可以在这里处理
    if (options.name) {
      this.setData({
        title: options.name,
        time: options.time,
        total: options.total
      });
    }
    
    // 模拟加载数据
    // 实际使用时，可以从服务器或本地存储加载真实数据
    this.loadBillData();
  },
  
  /**
   * 获取系统信息，设置状态栏高度
   */
  getSystemInfo() {
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });
    
    // 直接使用 CSS 变量的方式将不起作用，因为小程序不支持 documentElement
    // 改为直接在 wxss 中使用 rpx 计算或在页面中动态设置高度
    
    // 计算导航栏的总高度，包括状态栏
    const navBarTotalHeight = systemInfo.statusBarHeight + 44; // 44px是导航栏高度
    
    // 更新页面数据，可以在模板中直接使用
    this.setData({
      navBarHeight: navBarTotalHeight,
      contentMarginTop: navBarTotalHeight
    });
  },
  
  /**
   * 加载账单数据
   */
  loadBillData() {
    // 这里可以添加真实的数据加载逻辑
    // 例如从服务器或本地存储获取数据
    
    // 确保数据中的利息值正确显示
    const list = this.data.list.map(item => {
      // 如果有interest属性，将其设为"利息xx"格式
      if (item.interest) {
        item.interest = `利息${item.interest.toString().replace('利息', '')}`;
      }
      return item;
    });
    
    this.setData({
      list: list
    });
    
    console.log('加载账单数据完成', this.data.list);
  },

  /**
   * 关闭页面
   */
  onClose() {
    wx.navigateBack();
  },

  /**
   * 切换排序方式
   */
  toggleSort() {
    const newSortType = this.data.sortType === 'amount' ? 'time' : 'amount';
    this.setData({
      sortType: newSortType
    });
    
    // 根据排序方式重新加载数据
    this.loadBillData();
  },

  /**
   * 显示详情
   */
  showDetails() {
    // 跳转到详情页或显示弹窗
    wx.navigateTo({
      url: '/packageA/pages/billDetail/billDetail?id=123'
    });
  }
}) 