<view class="book-setting-container">
  <!-- 顶部导航栏 -->
  <!-- <view class="nav-bar">
    <view class="back-icon" bindtap="goBack">
      <image src="/static/icon/back.png" mode="aspectFit"></image>
    </view>
    <view class="page-title">账本设置</view>
  </view> -->
  <!-- 分类管理模块 -->
  <view class="section-container">
    <view class="section-title">
      <view class="title-line" style="background-image: linear-gradient(to bottom, {{titleLineColor || '#a2b486'}}, white);"></view>
      <text>分类管理</text>
    </view>
    <view class="setting-item" bindtap="navigateToCategory">
      <view class="item-left">
        <view class="item-icon circle">
          <image src="http://www.youcai.com/uploads/20250521/dd7091ec2bd62f9cd48ea478986958ad.png" mode="aspectFit"></image>
        </view>
        <view class="item-text">收支分类</view>
      </view>
      <view class="item-right">
        <van-icon name="arrow" color="#dedede" />
      </view>
    </view>
  </view>
  <!-- 账单管理模块 -->
  <view class="section-container">
    <view class="section-title">
      <view class="title-line" style="background-image: linear-gradient(to bottom, {{titleLineColor || '#a2b486'}}, white);"></view>
      <text>账单管理</text>
    </view>
    <view class="setting-item" bindtap="navigateToBillTransfer">
      <view class="item-left">
        <view class="item-icon circle">
          <image src="/static/icon/transfer.png" mode="aspectFit"></image>
        </view>
        <view class="item-text">
          <view class="main-text">账单迁移</view>
          <view class="sub-text">将账单迁移至其他账本</view>
        </view>
      </view>
      <view class="item-right">
        <van-icon name="arrow" color="#dedede" />
      </view>
    </view>
    <view class="setting-item" bindtap="showDeleteConfirmPopup">
      <view class="item-left">
        <view class="item-icon document">
          <image src="/static/icon/delete.png" mode="aspectFit"></image>
        </view>
        <view class="item-text">删除所有账单</view>
      </view>
      <view class="item-right">
        <van-icon name="arrow" color="#dedede" />
      </view>
    </view>
  </view>
  <!-- 账本快捷设置模块 -->
  <view class="section-container">
    <view class="section-title">
      <view class="title-line" style="background-image: linear-gradient(to bottom, {{titleLineColor || '#a2b486'}}, white);"></view>
      <text>账本快捷设置</text>
    </view>
    <view class="setting-item">
      <view class="item-left">
        <view class="item-icon home">
          <image src="/static/icon/home.png" mode="aspectFit"></image>
        </view>
        <view class="item-text">
          <view class="main-text">默认查询账本</view>
          <view class="sub-text">首页、账单、报表页面默认查询账本</view>
        </view>
      </view>
      <view class="item-right">
        <switch checked="{{defaultQueryBook}}" bindchange="toggleDefaultQueryBook" color="#40E0D0" />
      </view>
    </view>
    <!-- 修改默认选择账本部分 -->
    <view class="setting-item">
      <view class="item-left">
        <view class="item-icon select">
          <image src="/static/icon/select.png" mode="aspectFit"></image>
        </view>
        <view class="item-text">
          <view class="main-text">默认选择账本</view>
          <view class="sub-text">记账页面默认选择的账本</view>
          <view class="follow-mode-text">"跟随模式"会随着主页账本切换记账页面的账本</view>
        </view>
      </view>
      <view class="item-right">
        <switch checked="{{defaultSelectedBook}}" bindchange="toggleDefaultSelectedBook" color="#40E0D0" />
      </view>
    </view>
  </view>
  <!-- 账本管理模块 -->
  <view class="section-container">
    <view class="section-title">
      <view class="title-line" style="background-image: linear-gradient(to bottom, {{titleLineColor || '#a2b486'}}, white);"></view>
      <text>账本管理</text>
    </view>
    <view class="setting-item" bindtap="navigateToCoverModify">
      <view class="item-left">
        <view class="item-icon image">
          <image src="/static/icon/cover.png" mode="aspectFit"></image>
        </view>
        <view class="item-text">
          <view class="main-text">账本封面修改</view>
          <view class="sub-text">修改账本封面，首页封面更随账本选择账本的封面封面变动</view>
        </view>
      </view>
      <view class="item-right">
        <van-icon name="arrow" color="#dedede" />
      </view>
    </view>
    <view class="setting-item" bindtap="navigateToBookModify">
      <view class="item-left">
        <view class="item-icon list">
          <image src="/static/icon/book.png" mode="aspectFit"></image>
        </view>
        <view class="item-text">账本修改</view>
      </view>
      <view class="item-right">
        <van-icon name="arrow" color="#dedede" />
      </view>
    </view>
    <!-- 默认账本隐藏功能 -->
    <view class="setting-item">
      <view class="item-left">
        <view class="item-icon trash">
          <van-icon name="delete" size="26rpx" color="#ff5252" />
        </view>
        <view class="item-text">
          <view class="main-text">默认账本隐藏</view>
          <view class="sub-text">
            隐藏后，在所有页面
            <text class="highlight-text">【默认账本】</text>
            处于删除状态
          </view>
        </view>
      </view>
      <view class="item-right">
        <switch checked="{{hideDefaultBook}}" bindchange="toggleHideDefaultBook" color="#40E0D0" />
      </view>
    </view>
    <!-- 账本封存 -->
    <view class="setting-item" bindtap="showArchiveBookPopup" wx:if="{{is_default != 1}}">
      <view class="item-left">
        <view class="item-icon image">
          <image src="/static/icon/cover.png" mode="aspectFit"></image>
        </view>
        <view class="item-text">
          <view class="main-text">账本封存</view>
          <view class="sub-text">不常使用的账本进行封存，简化账本列表</view>
        </view>
      </view>
      <view class="item-right">
        <van-icon name="arrow" color="#dedede" />
      </view>
    </view>

    <!-- 账本排序 -->
    <view class="setting-item" bindtap="navigateToBookSort" wx:if="{{is_default != 1}}">
      <view class="item-left">
        <view class="item-icon list">
          <image src="/static/icon/book.png" mode="aspectFit"></image>
        </view>
        <view class="item-text">账本排序</view>
      </view>
      <view class="item-right">
        <van-icon name="arrow" color="#dedede" />
      </view>
    </view>

    <!-- 删除账本 --> 
    <view class="setting-item" bindtap="showDeleteBookConfirm" wx:if="{{is_default != 1}}">
      <view class="item-left">
        <view class="item-icon list">
          <image src="/static/icon/delete.png" mode="aspectFit"></image>
        </view>
        <view class="item-text">删除账本</view>
      </view>
      <view class="item-right">
        <van-icon name="arrow" color="#dedede" />
      </view>
    </view>
  </view>
  <confirmPopup show="{{showDeletePopup}}"
  title="是否删除【默认账本】账本内所有账单"
  content="确认删除账单"
  titleSize="18rpx"
  titleColor="#000"
  contentSize="28rpx"
  closePosition="right"
  contentColor="#cecece"
  confirmText="确认删除"
  bind:confirm="handleConfirm"
  bind:cancel="handleCancel"
  bind:close="hideDeleteConfirmPopup"></confirmPopup>
  
  <!-- 设置默认查询账本弹窗 -->
  <confirmPopup show="{{showQueryBookPopup}}"
  title="设置默认查询账本"
  content="将【默认账本】设置为主页默认查询账本，将会开启【默认查询】账本功能。设置此操作需要手动退出app后台,重启后生效～"
  titleSize="22rpx"
  contentSize="24rpx"
  closePosition="right"
  confirmText="确认"
  bind:confirm="confirmSetQueryBook"
  bind:cancel="hideQueryBookPopup"
  bind:close="hideQueryBookPopup"></confirmPopup>
  
  <!-- 关闭默认查询账本弹窗 -->
  <confirmPopup show="{{showCloseQueryPopup}}"
  title="关闭默认查询账本"
  content="确定关闭【默认账本】的默认查询功能吗?关闭后需要手动退出app后台,重启后生效～"
  titleSize="22rpx"
  contentSize="24rpx"
  closePosition="right"
  confirmText="确认"
  bind:confirm="confirmCloseQueryBook"
  bind:cancel="hideCloseQueryPopup"
  bind:close="hideCloseQueryPopup"></confirmPopup>
  
  <!-- 开启默认选择账本弹窗 -->
  <confirmPopup show="{{showSelectedBookPopup}}"
  title="设置默认选择账本"
  content="将【默认账本]设置为记账页面默认选择账本~"
  titleSize="22rpx"
  contentSize="24rpx"
  closePosition="right"
  confirmText="确认"
  bind:confirm="confirmSetSelectedBook"
  bind:cancel="hideSelectedBookPopup"
  bind:close="hideSelectedBookPopup"></confirmPopup>
  
  <!-- 关闭默认选择账本弹窗 -->
  <confirmPopup show="{{showCloseSelectedPopup}}"
  title="关闭默认选择账本"
  content="恢复记账页面默认选择账本为跟随模式"
  titleSize="22rpx"
  contentSize="24rpx"
  closePosition="right"
  confirmText="确认"
  bind:confirm="confirmCloseSelectedBook"
  bind:cancel="hideCloseSelectedPopup"
  bind:close="hideCloseSelectedPopup"></confirmPopup>

  <!-- 确认隐藏账本弹窗 -->
  <confirmPopup show="{{showHideBookPopup}}"
  title="确认隐藏账本"
  content="是否隐藏【默认账本】账本，可在【我的账本】顶部取消隐藏"
  titleSize="22rpx"
  contentSize="24rpx"
  closePosition="right"
  confirmText="确认"
  bind:confirm="confirmHideBook"
  bind:cancel="hideHideBookPopup"
  bind:close="hideHideBookPopup"></confirmPopup>

  <!--账本封面修改 -->
  <custom-popup visible="{{showAddBookPopup}}" closeButtonPosition="left" title="账本修改" position="bottom" bind:close="closeAddBookPopup">
    <view class="popup-container">
      <!-- 提示部分 -->
      <view class="tip-container">
          <view class="title-line" style="background-image: linear-gradient(to bottom, {{titleLineColor || '#a2b486'}}, white);"></view>
        <view class="tip-content">
          <text class="tip-text">默认账本修改内容，仅支持本设置生效，其他设备仍然为默认账本，需手动修改~</text>
        </view>
      </view>
      <scroll-view scroll-y="true" class="add-book-popup">
        <view class="book-cover">
          <image src="{{bookCoverImage}}" mode="aspectFill" class="cover-image" />
          <view class="book-cover-overlay" bindtap="showCoverSelect">
            <view style="display: flex;align-items: center;justify-content: center;">
              <view class="refresh-icon">
                <image src="/static/icon/refresh.png" mode="aspectFit" />
              </view>
              <view class="cover-text">选择封面</view>
            </view>
          </view>
          <!-- <view class="cover-refresh" bindtap="refreshBookCover">
            <image src="/static/icon/refresh.png" mode="aspectFit" />
          </view> -->
        </view>
        <!-- 分类初始化 -->
        <view class="category-init" bindtap="initCategory">
          <view class="init-icon">
            <image src="http://www.youcai.com/uploads/20250521/dd7091ec2bd62f9cd48ea478986958ad.png" mode="aspectFit" />
          </view>
          <view class="init-content">
            <view class="init-text"
              >{{selectedCategoryName || '分类初始化'}}
              <!-- <text class="required-mark" wx:if="{{!selectedCategoryId}}">*</text> -->
            </view>
            <view class="init-desc">{{selectedCategoryDesc || '内含不同账单分类组合'}}</view>
          </view>
          <view class="init-arrow"><van-icon name="arrow"/></view>
        </view>
        <view class="book-form">
          <view class="form-item">
            <view class="input-container">
              <input
                type="text"
                class="book-input {{inputFocusStates.name ? 'focus' : ''}}"
                bindinput="onBookNameInput"
                value="{{bookName}}"
                bindfocus="onInputFocus"
                bindblur="onInputBlur"
                data-field="name"
                placeholder=""
              />
              <view
                class="input-label {{bookName ? 'has-value' : ''}} {{inputFocusStates.name ? 'focus' : ''}}"
                style="{{(inputFocusStates.name || bookName) ? 'color:' + selectedColor + ';' : ''}}"
                >名称</view
              >
              <view class="input-placeholder {{inputFocusStates.name && !bookName ? 'show' : ''}}">请输入名称</view>
            </view>
          </view>
          <view class="form-item">
            <view class="input-container">
              <input
                type="text"
                class="book-input {{inputFocusStates.remark ? 'focus' : ''}}"
                bindinput="onBookRemarkInput"
                value="{{bookRemark}}"
                bindfocus="onInputFocus"
                bindblur="onInputBlur"
                data-field="remark"
                placeholder=""
              />
              <view
                class="input-label {{bookRemark ? 'has-value' : ''}} {{inputFocusStates.remark ? 'focus' : ''}}"
                style="{{(inputFocusStates.remark || bookRemark) ? 'color:' + selectedColor + ';' : ''}}"
                >备注</view
              >
              <view class="input-placeholder {{inputFocusStates.remark && !bookRemark ? 'show' : ''}}">请输入备注</view>
            </view>
          </view>
        </view>
        <!-- 占位区域，确保内容可以滚动到底部按钮上方 -->
        <!-- <view class="bottom-placeholder"></view> -->
      </scroll-view>
      <view class="save-btn-container">
        <view class="save-btn" bindtap="validateAndSaveNewBook">保存</view>
      </view>
    </view>
  </custom-popup>

  <!-- 点击选择封面弹出层 -->
  <custom-popup visible="{{showCoverSelectPopup}}"
    closeButtonPosition="left"
    title="选择封面"
    position="bottom"
    bind:close="closeCoverSelectPopup"
    showCustomUpload="{{true}}"
    customUploadText="自定义"
    bind:customUpload="uploadCustomCover">
    <view class="cover-select-container">
      <view class="cover-grid">
        <view class="cover-item" wx:for="{{coverList}}" wx:key="index" bindtap="selectCover" data-url="{{item.url}}">
          <image src="{{item.url}}" mode="aspectFill" class="cover-thumb" />
        </view>
      </view>
    </view>
  </custom-popup>

  <!-- 分类初始化弹层 -->
  <custom-popup visible="{{showCategoryInitPopup}}" closeButtonPosition="left" title="请选择分类初始化" position="bottom" bind:close="closeCategoryInitPopup">
    <view class="more-menu-list">
      <!-- 分类选项列表 -->
      <view class="more-menu-item" wx:for="{{categoryList}}" wx:key="id" bindtap="selectCategory" data-id="{{item.id}}">
        <view class="menu-item-content">
          <view class="menu-item-title">{{item.name}}</view>
          <view class="menu-item-desc">{{item.desc}}</view>
        </view>
        <view class="menu-item-arrow">
          <van-icon name="arrow" />
        </view>
      </view>
    </view>
  </custom-popup>

  <custom-popup visible="{{showArchiveBookPopup}}" closeButtonPosition="left" title="状态设置" position="bottom" bind:close="closeArchiveBookPopup">
    <view class="archive-popup-container">
      <!-- <view class="archive-desc">
        <view class="desc-title">账本封存说明</view>
        <view class="desc-content">将账本封存后，该账本将不会在常用账本列表中显示，可在单独列表中查看</view>
      </view> -->
      <view class="archive-status-item">
        <view class="item-left">
          <view class="item-icon store">
            <image src="/static/icon/home.png" mode="aspectFit"></image>
          </view>
          <view class="item-text">
            <view class="main-text">封存状态</view>
            <view class="sub-text">将账本封闭保存，可在单独列表查看</view>
          </view>
        </view>
        <view class="item-right">
          <switch checked="{{isArchived}}" bindchange="toggleArchiveStatus" color="#40E0D0" />
        </view>
      </view>
    </view>
  </custom-popup>
  <book-selector
    show="{{showBookSelector}}"
    bookList="{{bookList}}"
    showSelectionIndicator="{{false}}"
    showSelectModeToggle="{{false}}"
    showAllBooksOption="{{false}}"
    showAddButton="{{false}}"
    showViewModeToggle="{{true}}"
    showReloadOption="{{true}}"
    title="选择账本"
    bind:select="onBookSelect"
    bind:close="closeBookSelector"/>

  <!-- 确认删除账本弹窗 -->
  <confirmPopup show="{{showDeleteBookConfirmPopup}}"
  title="确认删除账本"
  content="确定要删除当前账本吗？删除后账本内所有数据将无法恢复"
  titleSize="22rpx"
  contentSize="24rpx"
  closePosition="right"
  confirmText="确认删除"
  bind:confirm="confirmDeleteBook"
  bind:cancel="hideDeleteBookConfirmPopup"
  bind:close="hideDeleteBookConfirmPopup"></confirmPopup>

</view>