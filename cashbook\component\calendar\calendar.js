const app=getApp();
Component({
  properties: {
    // 可传入初始年份和月份
    initYear: {
      type: Number,
      value: new Date().getFullYear()
    },
    initMonth: {
      type: Number,
      value: new Date().getMonth() + 1
    },
    isShow:{
      type:Boolean,
      default:false,
    },
    // 控制显示年份还是月份
    bottomId:{
        type:Number
    }

  },
  data: {
    year: new Date().getFullYear(),
    month: new Date().getMonth() + 1,
    selectedMonth: new Date().getMonth() + 1,
    selectedYear: new Date().getFullYear(),
    isMonthView: true,//0 为月份 1为年份
    startYear: Math.floor(new Date().getFullYear() / 10) * 10,
    activeId:4, //用来判断当前选中的月份添加样式
    activeYear:0,
    isMask:null,
  },
created(){
   console.log('botom',this.properties.bottomId);
   let id=this.properties.bottomId;
   if(id==0){
     this.setData({
      isMonthView:true
     })
   }else{
    this.setData({
      isMonthView:false
     })
   }
},
  lifetimes: {
    attached() {
      this.setData({
        year: this.properties.initYear,
        month: this.properties.initMonth,
        selectedMonth: this.properties.initMonth,
        selectedYear: this.properties.initYear,
        startYear: Math.floor(this.properties.initYear / 10) * 10
      });
    }
  },
  methods: {
    // 切换视图（月份和年份）
    toggleView() {
      this.setData({
        isMonthView:!this.data.isMonthView
      });
    },
    // 上一年
    prevYear() {
      let newYear = this.data.year - 1;
      const currentYear = new Date().getFullYear();
      if (newYear > currentYear) {
        newYear = currentYear;
      }
      this.setData({
        year: newYear,
        selectedYear: newYear
      });
    },
    // 下一年
    nextYear() {
      let newYear = this.data.year + 1;
      const currentYear = new Date().getFullYear();
      if (newYear > currentYear) {
        newYear = currentYear;
      }
      this.setData({
        year: newYear,
        selectedYear: newYear
      });
    },
    // 上一个十年
    prevDecade() {
      let newStartYear = this.data.startYear - 10;
      const currentYear = new Date().getFullYear();
      if (newStartYear + 9 > currentYear) {
        newStartYear = Math.floor(currentYear / 10) * 10;
      }
      this.setData({
        startYear: newStartYear
      });
    },
    // 下一个十年
    nextDecade() {
      let newStartYear = this.data.startYear + 10;
      const currentYear = new Date().getFullYear();
      if (newStartYear > currentYear) {
        newStartYear = Math.floor(currentYear / 10) * 10;
      }
      this.setData({
        startYear: newStartYear
      });
    },
    // 选择月份
    selectMonth(e) {
      this.setData({
        selectedMonth: e.currentTarget.dataset.month,
        activeId:e.currentTarget.dataset.month-1
      });
    },
    // 选择年份
    selectYear(e) {
      console.log('yearE',e);
      this.setData({
        selectedYear: e.currentTarget.dataset.year,
        year: e.currentTarget.dataset.year,
        activeYear:e.currentTarget.dataset.index
      });
    },
    // 取消
    cancel() {
      this.triggerEvent('cancel');
      this.setData({
        isShow:false
      })
    },
    // 确定
    confirm() {
      const { selectedYear, selectedMonth } = this.data;
      console.log(`选择的日期是：${selectedYear}年${selectedMonth}月`);
      this.triggerEvent('confirm', { year: selectedYear, month: selectedMonth });
      this.setData({
        isShow:false
      })
    }
  }
});