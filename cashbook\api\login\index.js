/**
 * 登录相关API
 */

import request from '../request';

/**
 * 微信登录
 * @param {Object} data - 登录参数
 * @param {string} data.code - 微信登录code
 * @param {string} [data.iv] - 加密算法的初始向量
 * @param {string} [data.encryptedData] - 包括敏感数据在内的完整用户信息的加密数据
 * @param {string} [data.from_user_id] - 分享人ID
 * @returns {Promise} - 返回登录结果
 */
export function wxLogin(data) {
  return request({
    url: '/login/wxlogin',
    method: 'POST',
    data: data
  });
}

/**
 * 手机号登录
 * @param {Object} data - 登录参数
 * @param {string} data.account - 账号/手机号
 * @param {string} data.password - 密码
 * @returns {Promise} - 返回登录结果
 */
export function phoneLogin(data) {
  return request({
    url: '/login/login',
    method: 'POST',
    data: data
  });
}

/**
 * 注册账号
 * @param {Object} data - 注册参数
 * @param {string} data.nickname - 昵称
 * @param {string} data.phone - 手机号
 * @param {string} data.password - 密码
 * @param {string} data.code - 验证码
 * @returns {Promise} - 返回注册结果
 */
export function register(data) {
  return request({
    url: '/login/register',
    method: 'POST',
    data: data
  });
}