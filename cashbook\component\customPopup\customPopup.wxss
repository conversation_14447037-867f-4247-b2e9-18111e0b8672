.custom-popup-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  pointer-events: none;
}

.custom-popup-container.visible {
  pointer-events: auto;
}

.custom-popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.custom-popup-container.visible .custom-popup-mask {
  opacity: 1;
}

.custom-popup-content {
  position: absolute;
  background-color: #fff;
  width: calc(100% - 60rpx);
  margin: 0 30rpx;
  z-index: 10000;
  transform: translateY(100%);
  transition: transform 0.35s cubic-bezier(0.33, 0.66, 0.66, 1);
  will-change: transform;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.custom-popup-container.visible .custom-popup-content {
  transform: translateY(0);
}

.custom-popup-round {
  border-radius: 35px;
  overflow: hidden;
}

.custom-popup-bottom {
  bottom: 15px;
  left: 0;
  right: 0;
}

.custom-popup-center {
  top: 50%;
  left: 0;
  right: 0;
  transform: translate(0, -50%) scale(0.85);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.custom-popup-container.visible .custom-popup-center {
  transform: translate(0, -50%) scale(1);
  opacity: 1;
}

.custom-popup-top {
  top: 0;
  left: 0;
  right: 0;
  transform: translateY(-100%);
}

.custom-popup-container.visible .custom-popup-top {
  transform: translateY(0);
}

.custom-popup-header {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  padding: 34rpx 20rpx;
}

.reverse-header {
  flex-direction: row-reverse;
}

.custom-popup-title {
  font-size: 34rpx;
  font-weight: 500;
  text-align: center;
}

.custom-popup-close {
  position: absolute;
  left: 30rpx;
  top: 30rpx;
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  background-color: #f5f5f5;
  border-radius: 50%;
  font-size: 40rpx;
  line-height: 40rpx;
}

.reverse-header .custom-popup-close {
  position: absolute;
  left: 30rpx;
  right: auto;
}

.custom-popup-body {
  overflow: hidden;
  padding: 0;
  flex: 1;
  position: relative;
}

/* 自定义上传按钮样式 */
.custom-upload-button {
  position: absolute;
  right: 40rpx;
  top: 34rpx;
  background-color: #4285f4;
  border-radius: 30rpx;
  padding: 10rpx 24rpx;
  font-size: 28rpx;
  color: white;
  z-index: 999;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}