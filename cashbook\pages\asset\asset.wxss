/* 资产页面样式 */
.asset-container {
  min-height: 100vh;
  background-color: #ffffff;
}

/* 顶部导航栏 */
.asset-container .nav-header {
  height: 88rpx;
  background-color: #3498db;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  position: relative;
  padding-bottom: 20rpx;
}

.asset-container .nav-header .nav-back {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}

.asset-container .nav-header .nav-title {
  font-size: 36rpx;
  color: #ffffff;
  font-weight: 500;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.asset-container .nav-header .nav-right {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* 资产总计卡片 */
.asset-container .asset-total-card {
  background-color: #ffffff;
  padding: 30rpx;
}

.asset-container .asset-total-card .asset-total-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.asset-container .asset-total-card .asset-total-header .asset-total-title {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
}

.asset-container .asset-total-card .asset-total-header .asset-add-btn {
  background-color: #3498db;
  color: #ffffff;
  font-size: 28rpx;
  padding: 8rpx 30rpx;
  border-radius: 30rpx;
}

.asset-container .asset-total-card .asset-total-content .asset-total-label {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 10rpx;
}

.asset-container .asset-total-card .asset-total-content .asset-total-amount {
  font-size: 60rpx;
  color: #333333;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.asset-container .asset-total-card .asset-total-content .asset-details {
  display: flex;
  justify-content: space-between;
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}

.asset-container .asset-total-card .asset-total-content .asset-details .asset-detail-item {
  display: flex;
  flex-direction: column;
}

.asset-container .asset-total-card .asset-total-content .asset-details .asset-detail-item text:first-child {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 10rpx;
}

.asset-container .asset-total-card .asset-total-content .asset-details .asset-detail-item text:last-child {
  font-size: 32rpx;
  color: #333333;
}

/* 资产变动区域 */
.asset-container .asset-changes-section {
  background-color: #ffffff;
  border-top: 20rpx solid #f8f8f8;
  position: relative;
}

/* 资产变动标题栏 */
.asset-container .asset-changes-section .asset-changes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  position: relative;
}

.asset-container .asset-changes-section .asset-changes-header .asset-changes-title {
  display: flex;
  align-items: center;
}

.asset-container .asset-changes-section .asset-changes-header .asset-changes-title .blue-bar {
  width: 6rpx;
  height: 36rpx;
  background-color: #3498db;
  margin-right: 12rpx;
  border-radius: 3rpx;
}

.asset-container .asset-changes-section .asset-changes-header .asset-changes-title text {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
  margin-right: 10rpx;
}

.asset-container .asset-changes-section .asset-changes-header .asset-changes-title .asset-changes-icon {
  margin-left: 8rpx;
}

.asset-container .asset-changes-section .asset-changes-header .asset-changes-crown {
  display: flex;
  align-items: center;
}

/* 资产变动详情 */
.asset-container .asset-changes-section .asset-changes-detail {
  background-color: #ffffff;
  padding: 0 30rpx;
  height: 0;
  overflow: hidden;
  transition: height 0.3s ease;
  opacity: 0;
}

.asset-container .asset-changes-section .asset-changes-detail.show {
  height: auto;
  opacity: 1;
  padding-bottom: 30rpx;
}

.asset-container .asset-changes-section .asset-changes-detail .asset-changes-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
}

.asset-container .asset-changes-section .asset-changes-detail .asset-changes-info .asset-changes-date {
  font-size: 28rpx;
  color: #999999;
}

.asset-container .asset-changes-section .asset-changes-detail .asset-changes-info .asset-changes-actions {
  display: flex;
  align-items: center;
}

.asset-container .asset-changes-section .asset-changes-detail .asset-changes-info .asset-changes-actions .asset-changes-action {
  margin-left: 20rpx;
  font-size: 24rpx;
  color: #666666;
  display: flex;
  align-items: center;
}

.asset-container .asset-changes-section .asset-changes-detail .asset-changes-stats-button {
  width: 100%;
  height: 90rpx;
  background-color: #3498db;
  color: #ffffff;
  font-size: 32rpx;
  border-radius: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 40rpx 0;
}

.asset-container .asset-changes-section .asset-changes-detail .asset-changes-empty-tip {
  text-align: center;
  font-size: 28rpx;
  color: #999999;
  margin: 30rpx 0;
}

/* 资产标题行 */
.asset-container .asset-changes-section .asset-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.asset-container .asset-changes-section .asset-title-row .asset-title-left {
  display: flex;
  align-items: center;
}

.asset-container .asset-changes-section .asset-title-row .asset-title-left text {
  font-size: 30rpx;
  color: #333333;
  margin-right: 10rpx;
}

.asset-container .asset-changes-section .asset-title-row .asset-title-right {
  display: flex;
  align-items: center;
}

.asset-container .asset-changes-section .asset-title-row .asset-title-right van-icon {
  margin-left: 30rpx;
}

/* 查看隐藏资产链接 */
.asset-container .view-hidden-assets {
  text-align: center;
  padding: 30rpx 0;
  color: #3498db;
  font-size: 28rpx;
}

/* 高斯模糊效果 */
.blur-effect {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.6);
  z-index: 1;
} 