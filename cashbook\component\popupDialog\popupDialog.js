// components/popupDialog/popupDialog.js
Component({
  properties: {
    visible: {
      type: Boolean,
      value: false,
      observer: function(newVal) {
        if (newVal) {
          this.showPopup();
        } else {
          this.hidePopup();
        }
      }
    },
    title: {
      type: String,
      value: '标题'
    },
    position: {
      type: String,
      value: 'bottom' // bottom, center, top
    },
    round: {
      type: Boolean,
      value: true
    },
    closeOnClickOverlay: {
      type: Boolean,
      value: true
    },
    maxHeight: {
      type: String,
      value: '90%'
    },
    closeButtonPosition: {
      type: String,
      value: 'right' // right, left
    },
    // 底部按钮相关
    showFooter: {
      type: Boolean,
      value: false
    },
    showCancelButton: {
      type: Boolean,
      value: true
    },
    cancelText: {
      type: String,
      value: '取消'
    },
    confirmText: {
      type: String,
      value: '确定'
    },
    confirmButtonColor: {
      type: String,
      value: '#4285f4'
    },
    // 额外按钮区域
    showActions: {
      type: Boolean,
      value: false
    },
    // 是否显示分类列表
    showCategoryList: {
      type: Boolean,
      value: false
    },
    // 分类列表数据，允许外部传入
    categoryList: {
      type: Array,
      value: []
    }
  },

  data: {
    isFormDirty: false, // 标记表单是否有未保存数据
    popupVisible: false,
    animating: false,
    // 默认的分类列表数据
    defaultCategoryList: [
      {
        id: 1,
        name: '收入',
        desc: '工资、奖金、投资收益等',
        icon: '/assets/icons/income.png'
      },
      {
        id: 2,
        name: '购物',
        desc: '日常购物、网购等消费',
        icon: '/assets/icons/shopping.png'
      },
      {
        id: 3,
        name: '餐饮',
        desc: '餐厅、外卖、食材等',
        icon: '/assets/icons/food.png'
      },
      {
        id: 4,
        name: '交通',
        desc: '公交、地铁、打车等',
        icon: '/assets/icons/transport.png'
      },
      {
        id: 5,
        name: '住房',
        desc: '房租、水电费、物业费等',
        icon: '/assets/icons/house.png'
      },
      {
        id: 6,
        name: '娱乐',
        desc: '电影、游戏、旅游等',
        icon: '/assets/icons/entertainment.png'
      },
      {
        id: 7,
        name: '医疗',
        desc: '药品、诊疗、保健等',
        icon: '/assets/icons/medical.png'
      }
    ]
  },

  lifetimes: {
    attached: function() {
      // 如果没有传入分类列表，则使用默认数据
      if (this.properties.categoryList.length === 0 && this.properties.showCategoryList) {
        this.setData({
          categoryList: this.data.defaultCategoryList
        });
      }
      
      if (this.properties.visible) {
        this.showPopup();
      }
    }
  },

  methods: {
    onBackPress() {
      if (this.data.isFormDirty) {
        wx.showModal({
          title: '提示',
          content: '确认返回吗？',
          success: (res) => {
            if (res.confirm) {
              // 用户确认后手动返回
              wx.navigateBack();
            }
          }
        });
        return true; // 阻止默认返回
      }
      return false; // 允许返回
    },
    showPopup() {
      // First make the container visible
      this.setData({ popupVisible: true });
      
      // Then after a small delay, add the visible class to trigger animations
      setTimeout(() => {
        this.setData({ animating: true });
      }, 30);
    },
    
    hidePopup() {
      // First remove the visible class to trigger animations
      this.setData({ animating: false });
      
      // Then after animation completes, hide the container
      setTimeout(() => {
        this.setData({ popupVisible: false });
      }, 350); // Match this to the longest transition duration
    },

    onClose() {
      this.hidePopup();
      this.triggerEvent('close');
    },

    onClickOverlay() {
      if (this.data.closeOnClickOverlay) {
        this.onClose();
      }
    },

    stopPropagation() {
      // Prevent event bubbling
    },
    
    // 取消按钮事件
    onCancel() {
      this.hidePopup();
      this.triggerEvent('cancel');
    },
    
    // 确认按钮事件
    onConfirm() {
      this.hidePopup();
      this.triggerEvent('confirm');
    },
    
    // 选择分类事件
    selectCategory(e) {
      const categoryId = e.currentTarget.dataset.id;
      const selectedCategory = this.data.categoryList.find(item => item.id === categoryId);
      
      if (selectedCategory) {
        this.triggerEvent('selectCategory', { category: selectedCategory });
        this.hidePopup();
      }
    }
  }
}) 