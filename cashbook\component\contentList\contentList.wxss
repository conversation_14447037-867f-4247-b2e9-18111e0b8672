@import "/component/billShareCard/billShareCard.wxss";

.list {
  /* margin: 0 10px; */
  background-color: transparent;
}

.oneColonm {
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
}

.time {
  background-color: #a1b386;
  padding: 3px 5px;
  border-radius: 10px;
  color: white;
  justify-self: end;
  width: fit-content;
}

/* 日期标题栏样式 - 按时间排序 */
.date-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 5px;
  border-bottom: 1px solid #f0f0f0;
  margin-top: 10px;
}

.date-left {
  display: flex;
  align-items: center;
}

.date-day {
  font-size: 15px;
  color: #333;
  font-weight: 500;
  margin-right: 8px;
}

.date-today {
  font-size: 12px;
  color: #888;
  margin-right: 8px;
}

.date-refresh {
  width: 18px;
  height: 18px;
}

.date-income {
  font-size: 13px;
  color: #888;
  display: flex;
}

/* 列表项共用样式 */
.info {
  display: grid;
  grid-template-columns: auto auto auto 1fr;
  margin-top: 10px;
  color: #8e8e8e;
  gap: 10px;
  padding: 5px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info view:nth-child(4) {
  justify-self: end;
}

.item2 {
  display: grid;
  grid-template-columns: 60px auto 1fr;
  align-self: center;
  align-items: center;
  gap: 10px;
  margin: 0 10px;
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 0;
  background-color: #fff;
}

.item:last-child .item2 {
  border-bottom: 0px solid;
}

.avator {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ff9966;
}

.share image {
  width: 20px;
  height: 20px;
}

.avator image {
  width: 30px;
  height: 30px;
  object-fit: cover;
}

.acount {
  justify-self: end;
  color: #ff3333;
  font-size: 16px;
  font-weight: 500;
}

/* 内部转账样式 */
.transfer-item {
  display: flex;
  position: relative;
  padding: 10px;
  /* background-color: #fff; */
  border-radius: 0;
  margin: 0;
  align-items: center;
  box-shadow: none;
  justify-content: space-between;
  overflow: hidden;
  /* 防止内容溢出 */
}

/* 如果上面的border-bottom方法不起作用，则使用这个伪元素方法 */
.transfer-item::after {
  content: '';
  position: absolute;
  right: 0;
  width: 90%;
  bottom: 0;
  height: 1px; /* 稍微增加高度 */
  background-color: rgba(0, 0, 0, 0.2); /* 使用rgba增加不透明度 */
  transform: scaleY(0.5);
  z-index: 1; /* 确保在上层 */
}


.transfer-icon {
  width: 55px;
  height: 55px;
  background-color: #f5f7f6;
  border-radius: 30%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #eef0ef;
  flex-shrink: 0;
  margin-right: 10px;
  /* 添加右边距，避免与中间内容过近 */
}

.transfer-icon image {
  width: 25px;
  height: 25px;
  opacity: 0.7;
}

.transfer-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  max-width: 200px;
  overflow: hidden;
  /* 防止内容溢出 */
  white-space: nowrap;
  /* 文本不换行 */
  text-overflow: ellipsis;
  /* 溢出显示省略号 */
  margin-right: 10px;
  /* 与右侧金额保持距离 */
}

.transfer-title {
  font-size: 12px;
  color: #333;
  font-weight: 500;
  margin-bottom: 5px;
  line-height: 1.2;
}

.transfer-desc-text {
  font-size: 12px;
  color: #999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 220px;
  line-height: 1.2;
}

.transfer-right {
  width: 120px;
  /* 固定宽度 */
  min-width: 100px;
  /* 最小宽度 */
  max-width: 80px;
  /* 最大宽度限制 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
  text-align: right;
  flex-shrink: 0;
  /* 防止被压缩 */
  overflow: hidden;
  /* 防止内容溢出 */
}

.transfer-amount {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  text-align: right;
  width: 100%;
  overflow: hidden;
  /* 防止内容溢出 */
  text-overflow: ellipsis;
  /* 溢出显示省略号 */
  white-space: nowrap;
  /* 文本不换行 */
}

.transfer-remark {
  font-size: 12px;
  color: #999;
  text-align: right;
  line-height: 1.2;
  width: 100%;
  overflow: hidden;
  /* 防止内容溢出 */
  text-overflow: ellipsis;
  /* 溢出显示省略号 */
  white-space: nowrap;
  /* 文本不换行 */
}

/* 按金额排序时的账单项目样式 */
.bill-item {
  display: flex;
  padding: 15px 5px;
  border-bottom: 1px solid #f5f5f5;
  align-items: center;
  background-color: #ffffff;
}

.bill-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #f5f7f6;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
  flex-shrink: 0;
}

.bill-icon image {
  width: 25px;
  height: 25px;
  opacity: 0.8;
}

.bill-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
}

.bill-title {
  font-size: 15px;
  color: #333;
  font-weight: 500;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.bill-time {
  font-size: 12px;
  color: #999;
}

.bill-amount {
  width: 100px;
  text-align: right;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.bill-amount.expense {
  color: #333;
}

.bill-tag {
  font-size: 10px;
  color: #999;
  margin-top: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px;
}

/* 滑动容器相关样式 */
.swipe-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  margin-bottom: 1px;
  background-color: #ffffff;
}

.swipe-wrapper {
  transition: transform 0.3s ease;
  width: 100%;
  z-index: 2;
  background-color: #fff;
}

.swipe-actions {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  display: flex;
  z-index: 1;
}

.swipe-action-right {
  display: flex;
  height: 100%;
  text-align: center;
}

.swipe-action-btn {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 14px;
}

/* 滑动按钮相关样式 */
.van-swipe-cell-right {
  height: 100%;
  display: flex;
}

.swipe-action-btns {
  display: flex;
  height: 100%;
}

.swipe-action-btn {
  height: 100%;
  min-width: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 14px;
}

.swipe-action-btn.delete-btn {
  background-color: #EE6363;
}

.swipe-action-btn.edit-btn {
  background-color: #AAE285;
}

/* 确保转账项和普通消费项的滑动背景正确 */
.transfer-item,
.item2 {
  width: 100%;
  box-sizing: border-box;
}

/* 兼容Vant swipe cell样式 */
.van-swipe-cell {
  background-color: #ffffff;
  margin-bottom: 1px;
  overflow: visible !important;
}

.van-swipe-cell__right {
  height: 100%;
  display: flex !important;
}

/* 复制按钮容器样式 */
.copy-button-container {
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: center;
  margin-right: 10rpx;
}

.copy-button {
  width: 80rpx;
  height: 80rpx;
  background-color: #d2d38f;
  /* 浅黄绿色 */
  color: #ffffff;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 14rpx;
  /* 圆角 */
  padding: 10rpx;
}

/* 右侧滑动按钮样式 */
.right-buttons {
  display: flex;
  height: 100%;
  align-items: center;
}

.edit-button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  width: 80rpx;
  color: #fff;
  font-size: 24rpx;
  background-color: #8fd495;
  border-radius: 14rpx;
  margin: 0 5rpx;
  padding: 10rpx;
}

.migration-button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  width: 80rpx;
  color: #fff;
  font-size: 24rpx;
  background-color: #c5c5c5;
  border-radius: 14rpx;
  margin: 0 5rpx;
  padding: 10rpx;
}

.delete-button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  width: 80rpx;
  color: #fff;
  font-size: 24rpx;
  background-color: #e95959;
  border-radius: 14rpx;
  margin: 0 5rpx;
  padding: 10rpx;
}

.icon-container {
  margin-right: 10rpx;
}

.transfer-arrow {
  color: #ca4e59;
  font-size: 12px;
}

/* 分享内容样式 */
.share-content {
  padding: 0 20rpx;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  margin-top: 20rpx;
  padding-bottom: 120rpx;
  /* 为底部按钮留出空间 */
}

.bill-card {
  background-color: #ffb6c1;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
  transition: background-color 0.3s ease;
  padding-bottom: 15rpx;
}

.bill-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.bill-title {
  display: flex;
  flex-direction: column;
}

.app-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #06866c;
  margin-bottom: 8rpx;
}

.app-desc {
  font-size: 24rpx;
  color: #06866c;
  opacity: 0.8;
}

.frog-icon {
  width: 80rpx;
  height: 80rpx;
}

.frog-icon image {
  width: 100%;
  height: 100%;
}

/* 账单总结 */
.bill-summary {
  padding: 20rpx 30rpx;
}

.summary-item {
  background-color: rgba(255, 255, 255, 0.5);
  padding: 20rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  transition: background-color 0.3s ease;
}

.dark-summary-item {
  background-color: rgba(0, 0, 0, 0.7) !important;
}

.summary-text {
  font-size: 28rpx;
  color: #fff;
  margin-right: 10rpx;
}

.summary-amount {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffb0bc;
}

.summary-currency {
  font-size: 28rpx;
  color: #fff;
  margin-left: 4rpx;
}

/* 日期分割线 */
.date-divider {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
}

.divider-line {
  height: 1rpx;
  background-color: rgba(0, 0, 0, 0.1);
  flex: 1;
}

.divider-date {
  padding: 0 20rpx;
  font-size: 24rpx;
  color: #666;
}

/* 账单明细 */
.bill-details-wrapper {
  padding: 10rpx 30rpx 20rpx;
}

.empty-bill-item {
  padding: 10rpx 0;
}

.bill-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.bill-item:last-child {
  border-bottom: none;
}

.item-left {
  display: flex;
  align-items: center;
}

.item-icon {
  width: 60rpx;
  height: 60rpx;
  background-color: #f5f5f5;
  border-radius: 50%;
  margin-right: 15rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.item-icon image {
  width: 70%;
  height: 70%;
}

.item-category {
  font-size: 28rpx;
  color: #333;
  display: flex;
  flex-direction: column;
}

.item-subcategory {
  margin-top: 5rpx;
  font-size: 24rpx;
  color: #666;
}

.item-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.item-amount {
  font-size: 30rpx;
  font-weight: bold;
  color: #ff85a2;
}

.item-tag {
  font-size: 22rpx;
  color: #666;
  background-color: rgba(255, 255, 255, 0.5);
  padding: 2rpx 10rpx;
  border-radius: 10rpx;
  margin-top: 4rpx;
}

.action-buttons {
  display: flex;
  padding: 20rpx 30rpx;
  justify-content: center;
  gap: 15rpx;
  width: 100%;
  box-sizing: border-box;
  margin-top: 20rpx;
}

.fixed-footer {
  position: relative;
  z-index: 101;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding-bottom: env(safe-area-inset-bottom);
}

.action-btn {
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 30rpx;
  flex: 0 0 auto;
  background-color: #ffb0bc;
  color: #fff;
  border: none;
  box-shadow: 0 2rpx 8rpx rgba(255, 176, 188, 0.3);
}

/* 样式选择弹窗 */
/* 移除不需要的样式，因为使用了custom-popup组件 */

.format-options {
  padding: 0 30rpx;
}

.format-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 36rpx 20rpx;
  border-bottom: 1px solid #f8f8f8;
  transition: background-color 0.2s;
}

.format-option:active {
  background-color: #f5f7fa;
}

.format-option:last-child {
  border-bottom: none;
  margin-bottom: 20rpx;
}

.option-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

/* 新版分享卡片样式 */
.bill-card-new {
  /* background-color: #ffb6c1; */
  overflow: hidden;
  padding: 30rpx 30rpx 0px 30rpx;
  position: relative;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.bill-header-new {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.bill-title-new {
  display: flex;
  flex-direction: column;
}

.app-name-new {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.app-desc-new {
  font-size: 24rpx;
  color: #fff !important;
  opacity: 0.8;
}

.frog-icon-new {
  width: 80rpx;
  height: 80rpx;
}

.frog-icon-new image {
  width: 100%;
  height: 100%;
}

/* 账单总结 */
.bill-summary-new {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.summary-item-new {
  background-color: rgba(242, 250, 254, 0.888);
  padding: 20rpx 30rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
}

.summary-text-new {
  font-size: 28rpx;
  color: #fff;
}

.summary-amount-new {
  font-size: 28rpx;
}

/* 日期分割线 */
.date-divider-new {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
}

.divider-line-new {
  flex: 1;
  height: 1rpx;
  background: repeating-linear-gradient(90deg, #ccc, #ccc 5rpx, transparent 5rpx, transparent 10rpx);
}

.divider-date-new {
  padding: 0 20rpx;
  font-size: 24rpx;
  color: #666;
  white-space: nowrap;
}

/* 账单明细 */
.bill-details-wrapper-new {
  border-radius: 16rpx;
  margin-top: 10rpx;
}

.bill-item-new {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.bill-item-new {
  border-bottom: 0.5px solid rgba(0, 0, 0, 0.2); /* 直接使用border-bottom */
}

.item-left-new {
  display: flex;
  align-items: center;
}

.item-icon-new {
  width: 50rpx;
  height: 50rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  margin-right: 15rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.item-icon-new image {
  width: 70%;
  height: 70%;
}

.item-category-new {
  font-size: 28rpx;
  color: #333;
  display: flex;
  flex-direction: column;
  max-width: 200rpx;
  overflow: hidden;
}

.category-main {
  font-weight: 500;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.item-subcategory-new {
  margin-top: 5rpx;
  font-size: 22rpx;
  color: #999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-right-new {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.item-amount-new {
  font-size: 30rpx;
  font-weight: bold;
  color: #ff4d4f;
}

.item-interest {
  font-size: 24rpx;
  color: #ff4d4f;
  margin-top: 4rpx;
}

.item-transfer-info {
  font-size: 22rpx;
  color: #999;
  margin-top: 2rpx;
}

/* 品牌底部 */

.frog-brand-new {
  display: flex;
  align-items: center;
  padding: 15rpx;
}

.brand-logo-new {
  width: 60rpx;
  height: 60rpx;
  margin-right: 15rpx;
}

.brand-logo-new image {
  width: 100%;
  height: 100%;
}

.brand-info-new {
  flex: 1;
}

.brand-name-new {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--accent-color, #06866c);
}

.brand-slogan-new {
  font-size: 22rpx;
  color: #999;
  margin-top: 4rpx;
}

/* 底部按钮 */
.buttons-container {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx 0;
  z-index: 1000;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-top: auto;
}

.action-buttons-new {
  display: flex;
  padding: 10rpx 30rpx;
  justify-content: space-around;
  gap: 20rpx;
  width: 100%;
  box-sizing: border-box;
}

.action-btn-new {
  height: 80rpx;
  min-width: 180rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  border: none;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  padding: 0 20rpx;
  background-color: var(--card-bg-color, #ffb0bc);
}

/* 弹窗容器 */
.popup-container {
  display: flex;
  flex-direction: column;
  max-height: 90vh;
  height: 90vh;
}

/* 底部固定按钮 */
.fixed-bottom-buttons {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 30rpx;
  background-color: #fff;
  position: relative;
  border-top: 1rpx solid #f0f0f0;
  z-index: 200;
  margin-top: 20rpx;
}

/* 分享弹窗滚动区域 */
.share-scroll-view {
  height: calc(65vh - 120rpx);
  padding: 0 20rpx;
}

/* 底部按钮栏 */
.bottom-buttons-bar {
  display: flex;
  justify-content: space-around;
  padding: 30rpx;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  box-sizing: border-box;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
}

/* 弹窗flex容器 */
.popup-flex-container {
  display: flex;
  flex-direction: column;
  height: 75vh;
}

/* 分享弹窗滚动区域 */
.share-scroll-view {
  flex: 1;
  padding: 0 20rpx;
  overflow-y: scroll;
}

/* 底部按钮栏 */
.bottom-buttons-bar {
  display: flex;
  justify-content: space-around;
  padding: 30rpx;
  /* background-color: #fff; */
  border-top: 1rpx solid #f0f0f0;
  z-index: 1000;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
}

/* 浮动底部按钮 */
.floating-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  background-color: transparent;
  padding: 20rpx;
  z-index: 10001;
  /* 确保高于弹窗的z-index */
}

/* 确保滚动视图正常工作 */
.bill-card-scroll {
  display: block !important;
  height: 50vh !important;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  position: relative;
  background-color: #fff;
}

.bill-card-new {
  display: flex;
  flex-direction: column;
  overflow: visible;
  min-height: 100%;
}

/* 确保内容可以正常滚动 */
.bill-details-wrapper-new {
  overflow: visible;
  padding-bottom: 30rpx;
}