/* 蒙层和容器 */
.month-picker-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.month-picker-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.month-picker-container.show .month-picker-mask {
  opacity: 1;
}

.month-picker-content {
  position: relative;
  z-index: 2;
  background-color: #fff;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  overflow: hidden;
  padding-bottom: env(safe-area-inset-bottom);
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.month-picker-container.show .month-picker-content {
  transform: translateY(0);
}

/* 年份选择区域 */
.year-selector {
  background-color: #9ab96d;
  padding: 30rpx 40rpx;
  color: #fff;
  position: relative;
}

.current-year {
  font-size: 32rpx;
  margin-bottom: 10rpx;
}

.year-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.year {
  font-size: 40rpx;
  font-weight: bold;
}

.year-controls {
  display: flex;
}

.year-control {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 5rpx;
  cursor: pointer;
}

.arrow {
  font-size: 24rpx;
}

.year-control.down .arrow {
  transform: rotate(180deg);
}

/* 月份滑动区域 */
.month-swiper {
  height: 400rpx;
  width: 100%;
  background-color: #f8f8f8;
  overflow: hidden; /* 防止出现滚动条 */
  touch-action: pan-y; /* 允许垂直滑动 */
}

/* 年份标签 */
.year-label {
  font-size: 28rpx;
  color: #999;
  text-align: center;
  padding: 20rpx 0 0;
}

/* 月份选择网格 */
.month-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 20rpx;
  padding: 40rpx;
  background-color: #f8f8f8;
  height: 100%;
  box-sizing: border-box; /* 确保padding不会导致溢出 */
  overflow: hidden; /* 防止出现滚动条 */
}

.month-item {
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #000000; /* 可选月份为黑色 */
  position: relative;
  transition: all 0.2s ease;
  border-radius: 40rpx;
}

.month-item.active {
  background-color: #9ab96d;
  color: #fff;
  border-radius: 40rpx;
  font-weight: 500;
  animation: pulse 0.3s ease;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 禁用的月份样式 */
.month-item.disabled {
  color: #cccccc; /* 不可选月份为灰色 */
  cursor: not-allowed;
  pointer-events: none; /* 防止点击事件 */
}

/* 底部按钮 */
.picker-actions {
  display: flex;
  border-top: 1rpx solid #eee;
  margin-top: 0;
}

.action-btn {
  flex: 1;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  transition: background-color 0.2s ease;
}

.action-btn:active {
  background-color: #f8f8f8;
}

.cancel {
  color: #999;
}

.confirm {
  color: #9ab96d;
  font-weight: 500;
}

/* 年份选择器 - 仅年份模式 */
.year-only-selector {
  background-color: #f8f8f8;
  padding: 30rpx 0;
  height: 400rpx;
  overflow-y: auto;
}

.year-option-list {
  display: flex;
  flex-direction: column;
  padding: 0 40rpx;
}

.year-option {
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #333;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  transition: all 0.2s ease;
}

.year-option.active {
  background-color: #9ab96d;
  color: #fff;
  font-weight: 500;
}

.year-option:active {
  opacity: 0.8;
}