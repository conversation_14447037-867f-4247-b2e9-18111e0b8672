Component({
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    title: {
      type: String,
      value: '明细'
    },
    position: {
      type: String,
      value: 'bottom' // bottom, center, top
    },
    round: {
      type: Boolean,
      value: true
    },
    closeOnClickOverlay: {
      type: Boolean,
      value: true
    },
    maxHeight: {
      type: String,
      value: '90%'
    },
    closeButtonPosition: {
      type: String,
      value: 'right' // right, left
    },
    // 标题线条颜色（渐变顶部颜色，底部将渐变为白色）
    titleLineColor: {
      type: String,
      value: '#a2b486'
    },
    // 账单详情相关属性
    categoryName: {
      type: String,
      value: '退款'
    },
    categoryIcon: {
      type: String,
      value: ''
    },
    amount: {
      type: String,
      value: '0.00'
    },
    datetime: {
      type: String,
      value: ''
    },
    accountBook: {
      type: String,
      value: '默认账本'
    },
    assetAccount: {
      type: String,
      value: '邮政'
    },
    // 可以从外部传入分类列表
    categoryList: {
      type: Array,
      value: []
    }
  },

  data: {
    sortType: 'time', // 初始排序类型
    popupVisible: false,
    animating: false,
    closeTimer: null,
    // 标题线条的渐变色样式
    titleLineStyle: '',
    showCategoryInitPopup: false,
    // 默认分类列表数据
    defaultCategoryList: [
      {
        id: 1,
        name: '按时间',
        desc: '支:-20.00',
        time: '5月10日',
        amount: '-20.00',
        title: '按时间排序'
      },
      {
        id: 2,
        name: '按金额',
        desc: '¥ 100.00',
        time: '5月10日',
        amount: '100.00',
        title: '按金额排序'
      }
    ],
    categoryList: [] // 初始化为空数组
  },

  observers: {
    'visible': function(visible) {
      console.log('accountDetailsPopup - visible 属性变更为:', visible);
      
      if (visible) {
        console.log('准备显示弹窗');
        this.showPopup();
      } else {
        console.log('准备隐藏弹窗');
        this.hidePopup();
      }
    },
    'categoryList': function(list) {
      // 监控 categoryList 变化
      console.log('categoryList changed:', list);
    }
  },

  lifetimes: {
    attached: function() {
      // 如果没有外部传入分类列表，则使用默认数据
      if (!this.data.categoryList || this.data.categoryList.length === 0) {
        this.setData({
          categoryList: this.data.defaultCategoryList
        });
      }
      
      if (this.properties.visible) {
        this.showPopup();
      }
    },
    
    detached: function() {
      // 清除计时器避免内存泄漏
      if (this.data.closeTimer) {
        clearTimeout(this.data.closeTimer);
      }
    }
  },

  methods: {
    sort1() {
      this.setData({
        sortType: 'amount'
      })
    },
    sort2() {
      this.setData({
        sortType: 'time'
      })
    },
    toggleSort() {
      console.log('toggleSort');
      
      // 切换排序类型
      const newSortType = this.data.sortType === 'amount' ? 'time' : 'amount';
      console.log('切换排序类型为:', newSortType);
      this.setData({
        sortType: newSortType
      });
    },
    showPopup() {
      console.log('执行 showPopup 方法');
      
      // 清除可能存在的关闭计时器
      if (this.data.closeTimer) {
        clearTimeout(this.data.closeTimer);
        this.setData({ closeTimer: null });
      }
      
      // 设置可见状态
      this.setData({ popupVisible: true });
      console.log('已设置 popupVisible 为:', true);
      
      // 延迟添加动画类
      wx.nextTick(() => {
        this.setData({ animating: true });
        console.log('已设置 animating 为:', true);
      });
    },
    
    hidePopup() {
      // 避免重复触发
      if (!this.data.popupVisible || !this.data.animating) return;
      
      // 移除动画类
      this.setData({ animating: false });
      
      // 延迟隐藏容器
      const timer = setTimeout(() => {
        // 只有在组件仍然存在时才执行
        if (this) {
          this.setData({ 
            popupVisible: false,
            closeTimer: null
          });
          this.triggerEvent('afterClose');
        }
      }, 350);
      
      this.setData({ closeTimer: timer });
    },

    onClose() {
      this.hidePopup();
      this.triggerEvent('close');
    },

    onClickOverlay() {
      if (this.data.closeOnClickOverlay) {
        this.onClose();
      }
    },

    stopPropagation() {
      // Prevent event bubbling
    },
    
    // 选择分类
    onCategorySelected(e) {
      console.log('onCategorySelected');
      const { name, time, total } = e.currentTarget.dataset;
      // 准备前往分类详情页面
      wx.navigateTo({
        url: `/packageA/pages/categoryDetail/categoryDetail?name=${name}&time=${time}&total=${total}` 
      })
    },
    
    // 删除记录
    onDelete() {
      this.triggerEvent('delete');
      this.hidePopup();
    },
    
    // 修改记录
    onEdit() {
      this.triggerEvent('edit');
      this.hidePopup();
    },
    
    closeCategoryInitPopup() {
      this.setData({
        showCategoryInitPopup: false
      });
    },
    
    // 选择分类事件
    selectCategory(e) {
      const categoryId = e.currentTarget.dataset.id;
      const selectedCategory = this.data.categoryList.find(item => item.id === categoryId);
      
      if (selectedCategory) {
        this.setData({
          showCategoryInitPopup: false
        });
        
        // 对于排序选项，触发特殊事件
        if (selectedCategory.id === 1) {
          // 按时间排序
          this.triggerEvent('sortByTime');
        } else if (selectedCategory.id === 2) {
          // 按金额排序
          this.triggerEvent('sortByAmount');
        } else {
          // 普通分类选择
          this.setData({
            categoryName: selectedCategory.name,
            categoryIcon: selectedCategory.icon
          });
          this.triggerEvent('categoryChange', { category: selectedCategory });
        }
      }
    },
    
    // 处理 contentList 项目点击
    onContentItemTap(e) {
      const { item, index } = e.detail;
      console.log('点击了账单项:', item, '索引:', index);
      
      // 处理点击事件，可以根据需要触发其他事件或更新数据
      this.triggerEvent('contentItemTap', {
        item: item,
        index: index
      });
    }
  }
}) 