{"condition": {"miniprogram": {"list": [{"name": "packageA/pages/bookSetting/incomeCategory/incomeCategory", "pathName": "packageA/pages/bookSetting/incomeCategory/incomeCategory", "query": "", "scene": null, "launchMode": "default"}, {"name": "packageA/pages/bookSetting/billTransfer/billTransfer", "pathName": "packageA/pages/bookSetting/billTransfer/billTransfer", "query": "id=55", "launchMode": "default", "scene": null}, {"name": "packageA/pages/bookSetting/billTransfer/billTransfer", "pathName": "packageA/pages/bookSetting/billTransfer/billTransfer", "query": "id=55", "launchMode": "default", "scene": null}, {"name": "packageA/pages/settings/bookSettings/bookSettings", "pathName": "packageA/pages/settings/bookSettings/bookSettings", "query": "", "launchMode": "default", "scene": null}, {"name": "packageA/pages/myBooks/myBooks", "pathName": "packageA/pages/myBooks/myBooks", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/report/report", "pathName": "pages/report/report", "query": "", "launchMode": "default", "scene": null}, {"name": "packageA/pages/bookDetail/bookDetail", "pathName": "packageA/pages/bookDetail/bookDetail", "query": "id=55", "launchMode": "default", "scene": null}]}}, "setting": {"urlCheck": false, "bigPackageSizeSupport": true, "compileHotReLoad": true}}