<t-popup visible="{{show}}" bind:visible-change="onPopupChange" placement="bottom">
  <view class="book-selector-popup">
    <!-- 固定的顶部区域 -->
    <view class="popup-header">
      <view class="close-btn" bindtap="closeBookSelector">
        <t-icon name="close" size="48rpx" />
      </view>
      <view class="popup-title" wx:if="{{title}}">{{title}}</view>
      <view class="header-actions">
        <view class="view-mode-switch" bindtap="toggleViewMode" wx:if="{{showViewModeToggle}}">
          <t-icon name="{{isCardMode ? 'image' : 'list'}}" size="48rpx" />
        </view>
        <view class="add-btn" bindtap="addNewBook" wx:if="{{showAddButton}}">添加</view>
      </view>
    </view>

    <!-- 可滚动的内容区域 -->
    <scroll-view class="book-list-container" scroll-y enable-flex show-scrollbar="{{true}}">
      <view class="book-list {{isCardMode ? 'card-mode' : 'list-mode'}}">
        <!-- 卡片模式 -->
        <block wx:if="{{isCardMode}}">
          <view class="book-card-grid">
            <view class="book-card {{item.selected ? 'selected' : ''}}" wx:for="{{internalBookList}}" wx:key="id" bindtap="selectBook" data-id="{{item.id}}">
              <view class="book-card-wrap">
                <view class="book-card-content" style="background-color: {{item.image ? '' : '#a5ddb9'}}">
                  <image src="{{item.image}}" mode="aspectFill" wx:if="{{item.image}}"></image>
                  <text class="book-card-abbr" wx:else>{{item.name.substr(0,2)}}</text>
                  <view class="book-card-title-wrap">
                    <view class="book-card-title">{{item.name}}</view>
                  </view>
                </view>
                <view class="book-card-radio {{item.selected ? 'selected' : ''}}" wx:if="{{showSelectionIndicator}}"></view>
              </view>
            </view>
          </view>
        </block>
        <!-- 列表模式 -->
        <block wx:else>
          <!-- 全部账本选项 -->
          <view class="book-item {{allBooksSelected ? 'selected' : ''}}" bindtap="selectAllBooks" wx:if="{{showAllBooksOption}}">
            <view class="book-icon all-books-icon">
              <t-icon name="folder" size="48rpx" />
            </view>
            <view class="book-info">
              <view class="book-name">选取全部账本</view>
              <view class="book-desc">全部账本</view>
            </view>
            <view class="book-select-indicator" wx:if="{{allBooksSelected && showSelectionIndicator}}">
              <view class="select-circle"></view>
            </view>
          </view>
          <!-- 账本列表 -->
          <view class="book-item {{item.selected ? 'selected' : ''}}" wx:for="{{internalBookList}}" wx:key="id" bindtap="selectBook" data-id="{{item.id}}">
            <view class="book-icon">
              <image src="{{item.image}}" mode="aspectFit" wx:if="{{item.image}}"></image>
              <text wx:else>{{item.name.substr(0,2)}}</text>
            </view>
            <view class="book-info">
              <view class="book-name">{{item.name}}</view>
              <view class="book-desc">{{item.notes || ''}}</view>
            </view>
            <view class="book-select-indicator" wx:if="{{item.selected && showSelectionIndicator}}">
              <view class="select-circle"></view>
            </view>
          </view>
        </block>
      </view>

      <!-- 全部账本选项 (卡片模式) -->
      <view class="all-books-option" wx:if="{{isCardMode && showAllBooksOption}}" bindtap="selectAllBooks">
        <view class="all-books-icon-container">
          <t-icon name="folder" size="48rpx" />
        </view>
        <view class="all-books-info">
          <view class="all-books-title">选取全部账本</view>
          <view class="all-books-desc">全部账本</view>
        </view>
      </view>
      <view class="divider" wx:if="{{isCardMode && showAllBooksOption}}"></view>
      <!-- 重新加载数据 -->
      <view class="reload-hint" bindtap="reloadBookList" wx:if="{{showReloadOption}}">
        <text>重新加载数据</text>
      </view>
    </scroll-view>

    <!-- 固定的底部区域 -->
    <view class="popup-footer" wx:if="{{showSelectModeToggle}}">
      <view class="select-mode-container">
        <view class="select-mode {{!isMultiSelect ? 'active' : ''}}" bindtap="toggleSelectMode">
          单选
        </view>
        <view class="select-mode {{isMultiSelect ? 'active' : ''}}" bindtap="toggleSelectMode">
          多选
        </view>
      </view>
      <view class="confirm-btn" bindtap="confirmBookSelection" wx:if="{{isMultiSelect}}">确认</view>
    </view>
  </view>
</t-popup>
