<!-- 信用卡还款页面 -->
<scroll-view scroll-y="true" class="page-container">
  <!-- 顶部状态栏 -->
  <view class="status-bar" style="height: {{statusBarHeight}}px; background-color: {{themeColor}};"></view>

  <!-- 顶部导航栏 -->
  <view class="navigation-bar" style="height: {{navHeight}}px; background-color: {{themeColor}};">
    <view class="nav-left" bindtap="onBackTap">
      <van-icon name="arrow-left" />
    </view>
    <view class="nav-title">信用卡还款</view>
    <view class="nav-right"></view>
  </view>

  <!-- 总计卡片 -->
  <view class="total-card">
    <view class="total-title">
      <view class="title-indicator" style="background-color: {{themeColor}};"></view>
      <text>合计</text>
    </view>
    <view class="total-content">
      <!-- 总负债区域 -->
      <view class="debt-label">总负债(元)</view>
      <view class="debt-amount">{{totalDebt}}</view>

      <!-- 统计数据区域 -->
      <view class="stats-row">
        <view class="stats-item"> <text>负债资产</text><text class="stats-highlight">数量</text> <text class="stats-value">{{debtAssetCount}}</text> </view>
        <view class="stats-item"> <text>今日到期</text><text class="stats-highlight">数量</text> <text class="stats-value">{{dueTodayCount}}</text> </view>
      </view>

      <!-- 右侧信用卡图标 -->
      <image class="card-illustration" src="/static/icon/card.png" mode="aspectFit"></image>
    </view>
  </view>

  <!-- 还款列表标题 -->
  <view class="repayment-list-title">
    <text>还款列表</text>
  </view>

  <!-- 还款列表 -->
  <view class="repayment-list">
    <block wx:for="{{repaymentList}}" wx:key="id">
      <view class="repayment-item">
        <!-- 使用资产详情卡片组件 -->
        <asset-detail-card
          asset="{{item}}"
          progressWidth="{{item.progressWidth}}"
          themeColor="{{themeColor}}"
          showRepayButton="{{true}}"
          bind:edit="handleEditAsset"
          bind:repay="handleRepay"
        >
        </asset-detail-card>
      </view>
    </block>
  </view>

  <!-- 底部空白 -->
  <view class="bottom-space"></view>
</scroll-view>
