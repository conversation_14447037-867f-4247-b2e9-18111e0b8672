import { getAccountBookList, setDefaultBook } from '../../../../api/book/index'
import { getUserAccounts } from '../../../../api/account/index'

// 引入接口
const { setConfig } = require('../../../../api/user/setconfig')
const util = require('../../../../utils/index.js')

// 更新记账功能设置
function updateSettings(data) {
  setConfig(data)
    .then((res) => {
      if (res.code === 1) {
        // wx.showToast({
        //   title: '设置成功',
        //   icon: 'success'
        // });
      } else {
        wx.showToast({
          title: res.msg || '设置失败',
          icon: 'none'
        })
      }
    })
    .catch((err) => {
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      })
    })
}

// 记账偏好设置页面
Page({
  data: {
    bookList: [],
    defaultBook: '全部账本',
    showBookSelector: false,
    loading: false,
    selectedBookId: null,
    showBookingPagePopup: false,
    showAssetReminder: true,
    rememberCategoryAsset: true,
    allowFutureRecords: false,
    checkBalanceShortage: false,
    keyboardVibration: true,
    incomeAnimation: true,
    showDatePickerPopup: false,
    selectedDay: 1,
    currentMonthName: '',
    nextMonthName: '',
    daysInLastRow: [],
    billCycleExample: '', // 新增：账单周期示例文本
    assetAccounts: [
      {
        id: 1,
        name: '现金',
        icon: '/static/icon/cash.png',
        balance: '1000.00',
        selected: false
      },
      {
        id: 2,
        name: '支付宝',
        icon: '/static/icon/alipay.png',
        balance: '2500.00',
        selected: false
      },
      {
        id: 3,
        name: '微信',
        icon: '/static/icon/wechat.png',
        balance: '1800.00',
        selected: false
      },
      {
        id: 4,
        name: '银行卡',
        icon: '/static/icon/bank.png',
        balance: '5000.00',
        selected: true
      }
    ],
    selectedAssetAccount: '默认账户',
    showCategoryStylePopup: false,
    categoryStyle: '列表翻页',
    showRowsSelector: false,
    selectedRows: 4, // 默认选择4行
    showDateTypePopup: false,
    dateType: '年月日',
    showFeaturePopup: false,
    features: {
      assetAccount: true,
      multiBook: true,
      reimbursement: true,
      tags: false,
      discount: true,
      notIncluded: true,
      billTemplate: false,
      billImage: true,
      viewMode: 'list', // 默认列表模式
      isMultiSelect: false, // 是否为多选模式
      isCardMode: true // 默认卡片模式
    },
    showAccountSelector: false,
    accountSelectorMode: 'list', // 默认为列表模式
    // 账户数据列表
    accounts: [
      { id: 1, name: '微信钱包111', subName: '微信钱包subName', amount: '¥ 800.00', icon: '/static/icon/card.png', selected: true },
      { id: 2, name: '支付宝', subName: '支付宝subName', amount: '¥ 980.00', icon: '/static/icon/hz.png', selected: false },
      { id: 2, name: '支付宝', subName: '支付宝subName', amount: '¥ 980.00', icon: '/static/icon/hz.png', selected: false },
      { id: 3, name: '不选择具体账户', subName: '仅计入收支账单，不计入资产', amount: '', icon: '/static/icon/card.png', selected: false }
    ],
    // 主题颜色
    selectedColor: '',
    currentAccountType: '', // 当前选择的账户类型（from 或 to）
    currentAccount: { id: 1, name: '微信钱包', subName: '微信钱包subName', amount: '¥ 800.00', icon: '/static/icon/card.png' },
    currentAccount2: { id: 2, name: '支付宝', subName: '支付宝subName', amount: '¥ 980.00', icon: '/static/icon/hz.png' }
  },

  onLoad: function () {
    // 页面加载时获取账本列表
    this.fetchBookList()
    // 获取用户账户列表
    this.fetchUserAccounts()

    // 从缓存中读取账单周期设置
    wx.getStorage({
      key: 'monthStartDay',
      success: (res) => {
        this.setData({
          selectedDay: res.data || 1
        })
      }
    })

    // 从缓存中读取默认账本设置
    wx.getStorage({
      key: 'defaultBookSettings',
      success: (res) => {
        const bookSettings = res.data
        if (bookSettings) {
          this.setData({
            selectedBookIds: bookSettings.bookIds || [],
            defaultBook: bookSettings.bookName || '全部账本',
            allBooksSelected: bookSettings.allBooksSelected || false
          })
        }
      }
    })

    // 从缓存中读取默认账户设置
    wx.getStorage({
      key: 'defaultAccountSettings',
      success: (res) => {
        const accountSettings = res.data
        if (accountSettings) {
          this.setData({
            currentAccount: accountSettings
          })
        }
      }
    })

    // 从缓存中读取转入账户设置
    wx.getStorage({
      key: 'defaultAccount2Settings',
      success: (res) => {
        const account2Settings = res.data
        if (account2Settings) {
          this.setData({
            currentAccount2: account2Settings
          })
        }
      }
    })

    // 从缓存中读取分类样式设置
    wx.getStorage({
      key: 'bookingPageSettings',
      success: (res) => {
        const bookingSettings = res.data
        console.log('bookingSettings', bookingSettings)

        if (bookingSettings) {
          // 更新设置数据，始终设置这些值而不考虑categoryStyle是否存在
          this.setData({
            categoryStyle: bookingSettings.categoryStyle || '列表翻页',
            selectedRows: bookingSettings.categoryRows || 4,
            dateType: bookingSettings.dateType || '年月日',
            showAssetReminder: bookingSettings.showAssetReminder !== undefined ? bookingSettings.showAssetReminder : true,
            rememberCategoryAsset: bookingSettings.rememberCategoryAsset !== undefined ? bookingSettings.rememberCategoryAsset : true,
            allowFutureRecords: bookingSettings.allowFutureRecords || false,
            checkBalanceShortage: bookingSettings.checkBalanceShortage || false,
            keyboardVibration: bookingSettings.keyboardVibration !== undefined ? bookingSettings.keyboardVibration : true,
            incomeAnimation: bookingSettings.incomeAnimation !== undefined ? bookingSettings.incomeAnimation : true
          })
        }
      }
    })

    // 设置卡片模式
    this.setData({
      isCardMode: true
    })

    // 加载记账功能设置
    wx.getStorage({
      key: 'bookingFeatures',
      success: (res) => {
        this.setData({
          features: res.data
        })
      }
    })
  },
  openAccountSelector(e) {
    const type = e.currentTarget.dataset.type || 'from'
    console.log('打开账户选择器，类型:', type)

    this.setData({
      showAccountSelector: true,
      currentAccountType: type
    })

    // 确保当前显示正确的账户列表
    if (type === 'asset') {
      // 在借还页面选择资产账户时，使用全部账户列表
      console.log('资产账户选择器打开')
    }
  },
  closeDatePicker() {
    this.setData({
      showDatePickerPopup: false
    })
  },
  // 关闭账户选择器
  closeAccountSelector() {
    // 如果是借入/借出账户选择器，使用动画效果关闭
    if (this.data.currentAccountType === 'borrowing') {
      const animation = wx.createAnimation({
        duration: 200,
        timingFunction: 'ease'
      })

      animation.translateY('100%').step()

      this.setData({
        slideUpAnimation: animation.export()
      })

      // 等待动画完成后再隐藏组件
      setTimeout(() => {
        this.setData({
          showAccountSelector2: false,
          currentAccountType: ''
        })
      }, 200)
    } else {
      this.setData({
        showAccountSelector: false,
        currentAccountType: ''
      })
    }
  },
  selectDefaultBookMode() {
    this.setData({
      showCategoryStylePopup: false
    })
  },

  // 处理账户选择器模式变化
  handleAccountSelectorModeChange(e) {
    const mode = e.detail.mode
    this.setData({
      accountSelectorMode: mode
    })
  },

  // 处理账户选择
  handleAccountSelect(e) {
    const account = e.detail.account
    if (this.data.currentAccountType === 'from') {
      console.log('转出账户选择:', account)

      // 更新转出账户
      this.setData({
        currentAccount: {
          id: account.id,
          name: account.name,
          subName: account.subName || '',
          icon: account.icon || '/static/icon/card.png',
          amount: account.amount || ''
        }
      })

      // 更新设置并保存到缓存
      updateSettings({
        account_id: account.id
      })

      // 保存到缓存
      wx.setStorage({
        key: 'defaultAccountSettings',
        data: {
          id: account.id,
          name: account.name,
          subName: account.subName || '',
          icon: account.icon || '/static/icon/card.png',
          amount: account.amount || ''
        }
      })
    } else if (this.data.currentAccountType === 'to') {
      console.log('转入账户选择:', account)
      // 更新转入账户
      this.setData({
        currentAccount2: {
          id: account.id,
          name: account.name,
          subName: account.subName || '',
          icon: account.icon || '/static/icon/card.png',
          amount: account.amount || ''
        }
      })

      // 保存到缓存
      wx.setStorage({
        key: 'defaultAccount2Settings',
        data: {
          id: account.id,
          name: account.name,
          subName: account.subName || '',
          icon: account.icon || '/static/icon/card.png',
          amount: account.amount || ''
        }
      })
    } else if (this.data.currentAccountType === 'asset') {
      console.log('资产账户选择:', account)
      // 更新资产账户
      this.setData({
        currentAccount: {
          id: account.id,
          name: account.name,
          subName: account.subName || '',
          icon: account.icon || '/static/icon/card.png',
          amount: account.amount || ''
        }
      })

      // 更新设置并保存到缓存
      updateSettings({
        account_id: account.id
      })

      // 保存到缓存
      wx.setStorage({
        key: 'defaultAccountSettings',
        data: {
          id: account.id,
          name: account.name,
          subName: account.subName || '',
          icon: account.icon || '/static/icon/card.png',
          amount: account.amount || ''
        }
      })
    }

    this.closeAccountSelector()
  },

  // 处理添加账户
  handleAddAccount() {
    wx.navigateTo({
      url: '/packageA/pages/settings/assetManagement/assetManagement'
    })
  },

  // 处理刷新账户数据
  handleRefreshAccounts() {
    console.log('刷新账户数据')
    wx.showToast({
      title: '刷新账户数据功能待实现',
      icon: 'none'
    })
  },

  // 处理账户设置
  handleAccountSettings() {
    console.log('账户设置')
    wx.showToast({
      title: '账户设置功能待实现',
      icon: 'none'
    })
  },
  /**
   * 切换账本选择模式（单选/多选）
   */
  toggleSelectMode: function () {
    const isMultiSelect = !this.data.isMultiSelect

    // 如果从多选切换到单选，需要清除多选状态
    if (!isMultiSelect) {
      // 恢复默认选中状态
      const bookList = this.data.bookList.map((book) => {
        return {
          ...book,
          selected: book.name === this.data.defaultBook
        }
      })

      this.setData({
        bookList: bookList,
        allBooksSelected: this.data.defaultBook === '全部账本'
      })
    }

    this.setData({
      isMultiSelect: isMultiSelect
    })
  },

  // 获取用户账户列表
  fetchUserAccounts: function () {
    wx.showLoading({
      title: '加载账户...'
    })

    // 调用获取用户账户接口
    getUserAccounts({
      show_type: 'group',
      status: 'normal'
    })
      .then((res) => {
        wx.hideLoading()

        if (res && res.code === 1) {
          console.log('获取账户成功:', res.data)

          // 更新账户数据
          // this.setData({
          //   userAccounts: res.data.data || [],
          //   netAssets: res.data.netassets || 0,
          //   capital: res.data.capital || '0.00',
          //   liabilities: res.data.liabilities || 0
          // });

          // 处理账户数据，更新账户选择器数据
          this.processAccountData(res.data.data)
        } else {
          wx.showToast({
            title: res.msg || '获取账户失败',
            icon: 'none'
          })
        }
      })
      .catch((err) => {
        wx.hideLoading()
        wx.showToast({
          title: err.msg,
          icon: 'none'
        })
        console.error('获取账户列表失败:', err)
      })
  },

  // 处理账户数据，转换为账户选择器可用的格式
  processAccountData: function (accountGroups) {
    if (!accountGroups || accountGroups.length === 0) return

    let accountList = []

    // 遍历账户分组
    accountGroups.forEach((group) => {
      if (group.data && group.data.length > 0) {
        console.log(group.data)

        // 将每个分组中的账户添加到列表
        group.data.forEach((account) => {
          accountList.push({
            id: account.id,
            name: account.name || account.account_name,
            subName: account.account_name,
            amount: account.money ? `¥ ${account.money}` : '',
            icon: util.getImageUrl(account.image) || '/static/icon/card.png',
            selected: false,
            accountType: account.account_type,
            accountCategory: account.account_category,
            include: account.include === '1'
          })
        })
      }
    })

    // 如果有账户，默认选中第一个
    if (accountList.length > 0) {
      accountList[0].selected = true
    }

    // 更新账户列表
    this.setData({
      accounts: accountList
    })
  },

  /**
   * 确认账本选择（多选模式下）
   */
  confirmBookSelection: function (e) {
    const { selectedIds, allBooksSelected } = e.detail

    if (allBooksSelected) {
      // 选择全部账本
      this.setData({
        defaultBook: '全部账本',
        allBooksSelected: true,
        selectedBookIds: selectedIds
      })
    } else if (selectedIds.length > 0) {
      // 选择了多个账本
      this.setData({
        defaultBook: `已选择${selectedIds.length}个账本`,
        allBooksSelected: false,
        selectedBookIds: selectedIds
      })
    } else {
      // 没有选择账本，默认选择第一个
      const firstBookId = this.data.bookList.length > 0 ? this.data.bookList[0].id : null
      const firstBookName = this.data.bookList.length > 0 ? this.data.bookList[0].name : '默认账本'

      this.setData({
        defaultBook: firstBookName,
        allBooksSelected: false,
        selectedBookIds: firstBookId ? [firstBookId] : []
      })

      // 更新selectedIds
      selectedIds.push(firstBookId)
    }

    // 更新设置
    updateSettings({
      select_book_id: selectedIds.length > 0 ? selectedIds : [0] // 传递所有账本ID数组，如果没有账本则传递[0]
    })

    // 保存设置到缓存
    wx.setStorage({
      key: 'defaultBookSettings',
      data: {
        bookIds: selectedIds,
        bookName: this.data.defaultBook,
        allBooksSelected: allBooksSelected
      }
    })

    // 关闭账本选择器
    this.closeBookSelector()
  },
  // 切换视图模式
  toggleViewMode() {
    const isCardMode = !this.data.isCardMode

    this.setData({
      isCardMode: isCardMode
    })

    // 保存视图模式到本地缓存
    wx.setStorage({
      key: 'bookListViewMode',
      data: isCardMode ? 'card' : 'list'
    })
  },

  // 重新加载账本列表
  reloadBookList: function () {
    this.fetchBookList()
    wx.showToast({
      title: '数据已刷新',
      icon: 'success'
    })
  },
  confirmDateSelection() {
    updateSettings({
      start_days: this.data.selectedDay
    })
    // 保存设置到本地缓存
    wx.setStorage({
      key: 'monthStartDay',
      data: this.data.selectedDay
    })

    this.setData({
      showDatePickerPopup: false
    })
  },
  // 打开记账功能弹窗
  goToBookingFeatures: function () {
    this.setData({
      showFeaturePopup: true
    })
  },

  // 关闭记账功能弹窗
  closeFeaturePopup: function () {
    this.setData({
      showFeaturePopup: false
    })
  },

  // 处理弹窗状态变化
  onFeaturePopupChange: function (e) {
    if (!e.detail.visible) {
      this.setData({
        showFeaturePopup: false
      })
    }
  },

  // 切换功能开关
  toggleFeature: function (e) {
    const feature = e.currentTarget.dataset.feature
    const value = e.detail.value

    // 更新对应功能的状态
    const features = this.data.features
    features[feature] = value

    this.setData({
      features: features
    })

    // 根据feature名称映射到setconfig.js中定义的参数名
    const paramMapping = {
      assetAccount: 'bill_auto_deduction_switch',
      multiBook: 'bill_many_book_switch',
      reimbursement: 'bill_reimburse_switch',
      tags: 'bill_tag_switch',
      discount: 'bill_discount_switch',
      notIncluded: 'bill_not_included_switch',
      billTemplate: 'bill_model_switch',
      billImage: 'bill_image_switch'
    }

    // 获取对应的参数名
    const paramName = paramMapping[feature]

    // 如果存在映射关系，则更新设置
    if (paramName) {
      const params = {}
      params[paramName] = value ? 1 : 0
      updateSettings(params)
    }

    // 保存功能设置
    this.saveFeatureSettings()
  },

  // 保存功能设置
  saveFeatureSettings: function () {
    wx.setStorage({
      key: 'bookingFeatures',
      data: this.data.features
    })
  },
  // 打开日期类型选择弹窗
  selectDateType: function () {
    this.setData({
      showDateTypePopup: true
    })
  },

  // 关闭日期类型选择弹窗
  closeDateTypePopup: function () {
    this.setData({
      showDateTypePopup: false
    })
  },

  // 处理弹窗状态变化
  onDateTypePopupChange: function (e) {
    if (!e.detail.visible) {
      this.setData({
        showDateTypePopup: false
      })
    }
  },

  // 选择日期类型
  selectDate: function (e) {
    const type = e.currentTarget.dataset.type
    let typeName = '年月日'
    let dateFormat = 'Ymd' // 默认年月日格式

    if (type === 'datetime') {
      typeName = '年月日时分'
      dateFormat = 'YmdHi' // 年月日时分格式
    }

    this.setData({
      dateType: typeName,
      showDateTypePopup: false
    })
    updateSettings({
      bill_date_type: dateFormat
    })

    // 保存设置
    this.saveBookingPageSettings()
  },
  // 打开分类显示行数选择弹窗
  selectCategoryRows: function () {
    this.setData({
      showRowsSelector: true
    })
  },

  // 关闭分类显示行数选择弹窗
  closeRowsSelector: function () {
    this.setData({
      showRowsSelector: false
    })
  },

  // 处理弹窗状态变化
  onRowsSelectorChange: function (e) {
    if (!e.detail.visible) {
      this.setData({
        showRowsSelector: false
      })
    }
  },

  // 选择行数
  selectRows: function (e) {
    const rows = parseInt(e.currentTarget.dataset.rows)

    // 更新数据状态
    this.setData({
      selectedRows: rows
      // showRowsSelector: false // 选择后自动关闭选择器
    })

    // 直接调用确认函数，完成设置保存
    // this.confirmRowsSelection();
  },

  // 确认选择行数
  confirmRowsSelection: function () {
    // 更新后端设置
    updateSettings({
      bill_category_rows: this.data.selectedRows
    })

    // 保存完整的设置到缓存
    this.saveBookingPageSettings()

    // 关闭选择器
    this.closeRowsSelector()
  },

  // 修改selectMonthStartDay方法
  selectMonthStartDay() {
    // 获取当前月份信息
    const now = new Date()
    const currentMonth = now.getMonth()
    const currentYear = now.getFullYear()

    // 计算当前月份的天数
    const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate()

    // 计算最后一行需要显示的天数（29-31日）
    const daysInLastRow = []
    for (let i = 29; i <= daysInMonth; i++) {
      daysInLastRow.push(i)
    }

    // 获取当前月份和下一个月份的名称
    const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    const currentMonthName = months[currentMonth]

    // 使用当前页面上显示的selectedDay，而不是从monthStartDay读取
    const selectedDay = this.data.selectedDay || 1

    // 计算下一个月
    const nextMonth = (currentMonth + 1) % 12
    const nextYear = nextMonth === 0 ? currentYear + 1 : currentYear

    // 获取下一个月的天数
    const daysInNextMonth = new Date(nextYear, nextMonth + 1, 0).getDate()

    // 处理特殊情况：如果选择的日期超过下一个月的天数
    let nextMonthDay = selectedDay
    let nextMonthName = months[nextMonth]

    // 如果选择的日期超过下一个月的天数，显示下下个月的1日
    if (selectedDay > daysInNextMonth) {
      nextMonthDay = 1
      // 获取下下个月的名称
      nextMonthName = months[(nextMonth + 1) % 12]
    }

    // 生成账单周期示例文本
    const billCycleExample = `${currentMonthName}${selectedDay}日 - ${nextMonthName}${nextMonthDay}日`

    this.setData({
      showDatePickerPopup: true,
      daysInLastRow: daysInLastRow,
      currentMonthName: currentMonthName,
      nextMonthName: nextMonthName,
      selectedDay: selectedDay,
      billCycleExample: billCycleExample
    })
  },

  // 修改selectDay方法
  selectDay(e) {
    const day = e.currentTarget.dataset.day

    // 获取当前月份信息
    const now = new Date()
    const currentMonth = now.getMonth()
    const currentYear = now.getFullYear()

    // 计算下一个月
    const nextMonth = (currentMonth + 1) % 12
    const nextYear = nextMonth === 0 ? currentYear + 1 : currentYear

    // 获取下一个月的天数
    const daysInNextMonth = new Date(nextYear, nextMonth + 1, 0).getDate()

    // 处理特殊情况：如果选择的日期超过下一个月的天数
    let nextMonthDay = day
    let nextMonthName = this.data.nextMonthName
    let thirdMonthName = ''

    // 如果选择的日期超过下一个月的天数，显示下下个月的1日
    if (day > daysInNextMonth) {
      nextMonthDay = 1
      // 获取下下个月的名称
      const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
      thirdMonthName = months[(nextMonth + 1) % 12]
      nextMonthName = thirdMonthName
    }

    // 更新账单周期示例文本
    const billCycleExample = `${this.data.currentMonthName}${day}日 - ${nextMonthName}${nextMonthDay}日`

    this.setData({
      selectedDay: day,
      billCycleExample: billCycleExample
    })
  },

  // 获取账本列表
  fetchBookList: function () {
    this.setData({
      loading: true
    })

    getAccountBookList()
      .then((res) => {
        if (res.code === 1) {
          res.data.forEach((item) => {
            item.image = util.getImageUrl(item.image)
          })

          // 处理账本列表数据
          const bookList = res.data || []

          // 找到默认账本
          const defaultBook = bookList.find((book) => book.is_default === 1)

          this.setData({
            bookList: bookList,
            selectedBookId: defaultBook ? defaultBook.id : null
          })
        } else {
          wx.showToast({
            title: res.msg || '获取账本列表失败',
            icon: 'none'
          })
        }
      })
      .catch((err) => {
        wx.showToast({
          title: err.msg,
          icon: 'none'
        })
        console.error('获取账本列表失败:', err)
      })
      .finally(() => {
        this.setData({
          loading: false
        })
      })
  },

  // 选择默认查询账本
  selectDefaultBook: function () {
    // 设置默认选中的账本
    const bookList = this.data.bookList.map((book) => {
      return {
        ...book,
        selected: this.data.selectedBookIds ? this.data.selectedBookIds.includes(book.id) : book.name === this.data.defaultBook
      }
    })

    // 准备选中的账本ID列表
    const selectedBookIds = this.data.selectedBookIds || this.data.bookList.filter((book) => book.name === this.data.defaultBook).map((book) => book.id)

    this.setData({
      showBookSelector: true,
      bookList: bookList,
      selectedBookIds: selectedBookIds,
      isCardMode: true // 默认使用卡片模式
    })
  },

  // 关闭账本选择器
  closeBookSelector: function () {
    this.setData({
      showBookSelector: false
    })
  },

  // 选择账本（由组件触发）
  selectBook: function (e) {
    const bookId = e.detail.bookId
    const selectedBook = e.detail.book

    // 单选模式下，更新数据
    this.setData({
      defaultBook: selectedBook.name,
      allBooksSelected: false,
      selectedBookIds: [bookId]
    })

    // 调用API更新设置，将bookId作为数组元素传递
    updateSettings({ select_book_id: [bookId] })

    // 保存设置到缓存
    wx.setStorage({
      key: 'defaultBookSettings',
      data: {
        bookId: bookId,
        bookIds: [bookId],
        bookName: selectedBook.name,
        allBooksSelected: false
      }
    })

    // 关闭账本选择器
    this.closeBookSelector()
  },

  // 选择全部账本（由组件触发）
  selectAllBooks: function () {
    // 获取所有账本ID
    const allBookIds = this.data.bookList.map((book) => book.id)

    // 更新选中状态
    this.setData({
      allBooksSelected: !this.data.allBooksSelected,
      defaultBook: '全部账本',
      selectedBookIds: allBookIds
    })

    // 如果是单选模式下选择全部账本，直接更新数据
    if (!this.data.isMultiSelect) {
      // 更新设置
      updateSettings({
        select_book_id: allBookIds.length > 0 ? allBookIds : [0] // 传递所有账本ID数组，如果没有账本则传递[0]
      })

      // 保存设置到缓存
      wx.setStorage({
        key: 'defaultBookSettings',
        data: {
          bookIds: allBookIds,
          bookName: '全部账本',
          allBooksSelected: true
        }
      })

      // 关闭账本选择器
      this.closeBookSelector()
    }
  },

  // 处理账本选择状态变化（多选模式）
  toggleBookSelection: function (e) {
    const selectedIds = e.detail.selectedIds

    // 更新选中的账本ID
    this.setData({
      selectedBookIds: selectedIds
    })
  },

  // 添加新账本
  addNewBook: function () {
    wx.navigateTo({
      url: '/packageA/pages/myBooks/myBooks'
    })
  },
  // 打开记账页面设置弹窗
  goToBookingPageSettings: function () {
    this.setData({
      showBookingPagePopup: true
    })
  },

  // 关闭记账页面设置弹窗
  closeBookingPagePopup: function () {
    this.setData({
      showBookingPagePopup: false
    })
  },

  // 弹窗状态变化
  onBookingPagePopupChange: function (e) {
    if (!e.detail.visible) {
      this.setData({
        showBookingPagePopup: false
      })
    }
  },
  onBookSelectorChange: function (e) {
    if (!e.detail.visible) {
      this.setData({
        showBookSelector: false
      })
    }
  },
  onPopupChange: function (e) {
    if (!e.detail.visible) {
      this.setData({
        showDatePickerPopup: false
      })
    }
  },

  // 开关切换方法
  toggleShowAssetReminder: function (e) {
    const value = e.detail.value
    this.setData({
      showAssetReminder: value
    })
    updateSettings({ account_tip_switch: value ? 1 : 0 })
    this.saveBookingPageSettings()
  },

  toggleRememberCategoryAsset: function (e) {
    this.setData({
      rememberCategoryAsset: e.detail.value
    })
    this.saveBookingPageSettings()
  },

  toggleAllowFutureRecords: function (e) {
    this.setData({
      allowFutureRecords: e.detail.value
    })
    this.saveBookingPageSettings()
  },

  toggleCheckBalanceShortage: function (e) {
    this.setData({
      checkBalanceShortage: e.detail.value
    })
    this.saveBookingPageSettings()
  },

  toggleKeyboardVibration: function (e) {
    this.setData({
      keyboardVibration: e.detail.value
    })
    this.saveBookingPageSettings()
  },

  toggleIncomeAnimation: function (e) {
    this.setData({
      incomeAnimation: e.detail.value
    })
    this.saveBookingPageSettings()
  },
  // 打开资产账户选择器
  selectDefaultAsset: function () {
    this.setData({
      showAssetSelector: true
    })
  },

  // 关闭资产账户选择器
  closeAssetSelector: function () {
    this.setData({
      showAssetSelector: false
    })
  },

  // 处理弹窗状态变化
  onAssetPopupChange: function (e) {
    if (!e.detail.visible) {
      this.setData({
        showAssetSelector: false
      })
    }
  },

  // 选择"不选择具体账户"选项
  selectNoSpecificAccount: function () {
    // 重置所有账户的选中状态
    const accounts = this.data.assetAccounts.map((account) => {
      return {
        ...account,
        selected: false
      }
    })

    this.setData({
      assetAccounts: accounts,
      selectedAssetAccount: '不选择具体账户'
    })
  },

  // 选择特定资产账户
  selectAssetAccount: function (e) {
    const index = e.currentTarget.dataset.index
    const accounts = this.data.assetAccounts.map((account, idx) => {
      return {
        ...account,
        selected: idx === index
      }
    })

    this.setData({
      assetAccounts: accounts,
      selectedAssetAccount: accounts[index].name
    })
  },

  // 确认选择
  confirmAssetSelection: function () {
    // 这里可以添加保存选择的逻辑
    this.closeAssetSelector()
  },

  // 保存记账页面设置
  // 保存记账页面设置
  saveBookingPageSettings: function () {
    const settings = {
      showAssetReminder: this.data.showAssetReminder,
      rememberCategoryAsset: this.data.rememberCategoryAsset,
      allowFutureRecords: this.data.allowFutureRecords,
      checkBalanceShortage: this.data.checkBalanceShortage,
      keyboardVibration: this.data.keyboardVibration,
      incomeAnimation: this.data.incomeAnimation,
      categoryStyle: this.data.categoryStyle,
      categoryRows: this.data.selectedRows,
      dateType: this.data.dateType
    }

    wx.setStorage({
      key: 'bookingPageSettings',
      data: settings
    })
  },
  // 打开分类样式选择弹窗
  selectCategoryStyleType: function () {
    this.setData({
      showCategoryStylePopup: true
    })
  },

  // 关闭分类样式选择弹窗
  closeCategoryStylePopup: function () {
    this.setData({
      showCategoryStylePopup: false
    })
  },

  // 处理弹窗状态变化
  onCategoryStylePopupChange: function (e) {
    if (!e.detail.visible) {
      this.setData({
        showCategoryStylePopup: false
      })
    }
  },

  // 选择分类样式
  selectCategoryStyle: function (e) {
    const style = e.currentTarget.dataset.style
    let styleName = '列表翻页'

    if (style === 'card') {
      styleName = '卡片'
    }

    this.setData({
      categoryStyle: styleName,
      showCategoryStylePopup: false
    })

    // 更新后端设置
    updateSettings({
      bill_category_type: style
    })

    // 直接保存分类样式到单独的缓存中
    // wx.setStorage({
    //   key: 'categorySetting',
    //   data: {
    //     style: style,
    //     styleName: styleName
    //   }
    // });

    // 同时也保存到bookingPageSettings中
    this.saveBookingPageSettings()
  }
})
