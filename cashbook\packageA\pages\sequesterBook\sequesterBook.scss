// 变量定义
$bg-color: #f8f8f8;
$white: #fff;
$text-color: #333;
$text-light: #666;
$text-muted: #999;
$primary-color: #a2d7f2;
$border-radius: 16rpx;
$shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);

// 混合器
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

@mixin card-style {
  background-color: $white;
  border-radius: $border-radius;
  box-shadow: $shadow;
}

// 整体容器
.container {
  min-height: 100vh;
  background-color: $bg-color;
  @include flex-column;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;
  

  // 顶部导航栏
  .header {
    position: relative;
    height: 88rpx;
    @include flex-center;
    padding: 0 30rpx;
    background-color: $white;
    padding-top: env(safe-area-inset-top);

    .back-icon {
      position: absolute;
      left: 30rpx;
      height: 44rpx;
      width: 44rpx;
      @include flex-center;
      z-index: 1;

      image {
        width: 44rpx;
        height: 44rpx;
      }
    }

    .title {
      font-size: 36rpx;
      font-weight: bold;
      color: $text-color;
    }
  }

  // 提示区域
  .tip-container {
    margin: 30rpx;
    @include card-style;
    overflow: hidden;
    display: flex;
    padding: 30rpx 10px;

    .tip-line {
      width: 8rpx;
      background-color: $primary-color;
      margin-right: 20rpx;
      border-radius: 4rpx;
    }

    .tip-content {
      flex: 1;
      @include flex-column;
      padding-right: 30rpx;

      .tip-title-container {
        display: flex;
        align-items: center;
        margin-bottom: 12rpx;
      }

      .tip-title {
        font-size: 30rpx;
        font-weight: 500;
        color: $text-color;
      }

      .tip-text {
        font-size: 22rpx;
        color: $text-light;
        line-height: 1.6;
      }
    }
  }

  // 账本列表区域
  .book-list {
    flex: 1;
    @include flex-column;
    align-items: center;
    padding: 40rpx;
    padding-top: 0;

    // 空状态
    .empty-state {
      flex: 1;
      @include flex-column;
      align-items: center;
      justify-content: center;
      padding: 80rpx;
      margin-top: 60rpx;

      .empty-icon {
        width: 300rpx;
        height: 300rpx;
        margin-bottom: 40rpx;
        opacity: 0.85;
      }

      .empty-text {
        font-size: 28rpx;
        color: $text-muted;
        text-align: center;
        line-height: 1.5;
      }
    }
  }
} 

.title-line {
    width: 4px;
    height: 15px;
    border-radius: 2px;
    margin-right: 10px;
    background-image: linear-gradient(to bottom, #ff9999, white);
    background-size: 100% 100%;
  }