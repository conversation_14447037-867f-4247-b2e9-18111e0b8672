/**
 * 请求封装
 */

// 基础URL，根据实际环境配置
const BASE_URL = 'http://www.youcai.com/index.php/api/';

/**
 * 请求函数
 * @param {Object} options - 请求配置
 * @returns {Promise} - 返回Promise对象
 */
const request = (options) => {
  return new Promise((resolve, reject) => {
    // 获取token
    const token = wx.getStorageSync('token') || '';
    
    // 合并请求头
    const header = {
      'Content-Type': 'application/x-www-form-urlencoded',
      ...options.header
    };
    
    // 如果有token，添加到请求头
    if (token) {
      header['token'] = token;
    }
    
    // 发起请求
    wx.request({
      url: BASE_URL + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: header,
      success: (res) => {
        // 请求成功
        const { data } = res;
        
        // 根据业务状态码处理
        if (data.code === 1) {
          resolve(data);
        } else if (data.code === 401) {
          // token失效，需要重新登录
          wx.removeStorageSync('token');
          wx.removeStorageSync('userInfo');
          
          // 跳转到登录页
          wx.navigateTo({
            url: '/pages/login/login'
          });
          
          reject(data);
        } else {
          // 其他业务错误
          reject(data);
        }
      },
      fail: (err) => {
        // 请求失败
        reject(err);
      }
    });
  });
};

export default request;