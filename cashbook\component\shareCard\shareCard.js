Component({
  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    budgetData: {
      type: Object,
      value: {
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1,
        budgetAmount: 2000.00,
        spentAmount: 448.00,
        remainingAmount: 1552.00,
        remainingPercent: 77.6,
        remainingDays: 15,
        dailySpent: 26.35,
        dailyBudget: 64.52,
        remainingDailyAmount: 103.47
      }
    },
    buttonPosition: {
      type: String,
      value: 'right' // 'left'或'right'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    year: new Date().getFullYear(),
    month: new Date().getMonth() + 1,
    budgetAmount: 2000.00,
    spentAmount: 448.00,
    remainingAmount: 1552.00,
    remainingPercent: 77.6,
    remainingDays: 15,
    dailySpent: 26.35,
    dailyBudget: 64.52,
    remainingDailyAmount: 103.47,
    cardStyle: {
      backgroundColor: '#6b7c4e',
      textColor: '#ffffff'
    },
    
    formatPopupVisible: false,
    cardFormat: 'frog' // 默认样式：小青账
  },

  lifetimes: {
    attached() {
      // 优先从本地存储获取 cardColors
      let storedColors = wx.getStorageSync('cardColors');
      if (!storedColors) {
        storedColors = {
          theme1: {
            backgroundColor: '#becea9',
            textColor: '#ffffff'
          },
          theme2: {
            backgroundColor: '#586843',
            textColor: '#ffffff'
          }
        };
        wx.setStorageSync('cardColors', storedColors);
      }
      // 用 theme1 初始化 cardStyle
      this.setData({
        cardStyle: storedColors.theme1
      });
    }
  },

  observers: {
    'budgetData': function(budgetData) {
      if (budgetData) {
        this.setData({
          year: budgetData.year || this.data.year,
          month: budgetData.month || this.data.month,
          budgetAmount: budgetData.budgetAmount || this.data.budgetAmount,
          spentAmount: budgetData.spentAmount || this.data.spentAmount,
          remainingAmount: budgetData.remainingAmount || this.data.remainingAmount,
          remainingPercent: budgetData.remainingPercent || this.data.remainingPercent,
          remainingDays: budgetData.remainingDays || this.data.remainingDays,
          dailySpent: budgetData.dailySpent || this.data.dailySpent,
          dailyBudget: budgetData.dailyBudget || this.data.dailyBudget,
          remainingDailyAmount: budgetData.remainingDailyAmount || this.data.remainingDailyAmount
        });
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 关闭弹窗
     */
    onClose(e) {
      // TDesign的visible-change事件会传入一个参数e，包含visible属性
      // 如果直接点击关闭按钮，e可能为undefined，所以需要做兼容处理
      const visible = e && e.detail ? e.detail.visible : false;
      
      if (visible === false || e === undefined) {
        this.setData({
          visible: false
        });
        this.triggerEvent('close');
      }
    },

    /**
     * 更换颜色
     */
    onChangeColor() {
      // 获取本地存储的颜色
      const storedColors = wx.getStorageSync('cardColors') || {
        theme1: {
          backgroundColor: '#6b7c4e',
          textColor: '#ffffff'
        },
        theme2: {
          backgroundColor: '#5a7a94',
          textColor: '#ffffff'
        }
      };
      
      // 判断当前颜色是哪一种，然后切换到另一种
      const currentBgColor = this.data.cardStyle.backgroundColor;
      let newTheme;
      
      if (currentBgColor === storedColors.theme1.backgroundColor) {
        newTheme = storedColors.theme2;
      } else {
        newTheme = storedColors.theme1;
      }
      
      // 更新颜色
      this.setData({
        cardStyle: newTheme
      });
    },

    /**
     * 改变样式
     */
    onChangeFormat() {
      this.setData({
        formatPopupVisible: true
      });
    },

    /**
     * 关闭样式弹窗
     */
    onCloseFormatPopup(e) {
      // TDesign的visible-change事件会传入一个参数e，包含visible属性
      // 如果直接点击关闭按钮，e可能为undefined，所以需要做兼容处理
      const visible = e && e.detail ? e.detail.visible : false;
      
      if (visible === false || e === undefined) {
        this.setData({
          formatPopupVisible: false
        });
      }
    },

    /**
     * 选择样式
     */
    selectFormat(e) {
      const format = e.currentTarget.dataset.format;
      this.setData({
        cardFormat: format,
        formatPopupVisible: false
      });
      
      wx.showToast({
        title: '样式已更新',
        icon: 'success',
        duration: 1500
      });
    },

    /**
     * 保存图片
     */
    onSaveImage() {
      // 创建画布
      wx.showLoading({
        title: '生成图片中...',
      });

      // 在实际应用中，这里需要使用canvas或者后端服务创建图片
      setTimeout(() => {
        wx.hideLoading();
        wx.showToast({
          title: '图片保存成功',
          icon: 'success'
        });
      }, 1500);
    }
  }
}) 