/* 分类详情页面样式 */
.category-detail-container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding: 30rpx;
}

/* 顶部分类信息 */
.category-header {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  position: relative;
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
  overflow: hidden;
}

.category-icon image {
  width: 50rpx;
  height: 50rpx;
}

.category-name {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
  flex: 1;
}

.more-options {
  padding: 10rpx;
}

/* 提示文本 */
.tip-text {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 30rpx;
}

/* 子分类网格 */
.subcategory-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20rpx;
  padding: 10rpx 0;
}

/* 子分类项 */
.subcategory-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx;
}

.subcategory-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10rpx;
  overflow: hidden;
}

.subcategory-icon image {
  width: 60rpx;
  height: 60rpx;
}

.subcategory-name {
  font-size: 24rpx;
  color: #333;
  text-align: center;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 添加子分类按钮 */
.add-item .add-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10rpx;
}

/* 操作列表样式 */
.operation-list {
  padding: 10rpx 0;
}

.operation-item {
  display: flex;
  align-items: center;
  padding: 20rpx 10rpx;
  border-bottom: 1px solid #f0f0f0;
}

.operation-item:last-child {
  border-bottom: none;
}

.operation-item van-icon {
  margin-right: 20rpx;
}

.operation-item text {
  font-size: 28rpx;
  color: #333;
}

/* 自定义折叠面板样式 */
.van-collapse {
  margin-bottom: 20rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

/* 媒体查询，小屏幕设备显示4列 */
@media screen and (max-width: 375px) {
  .subcategory-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 媒体查询，超小屏幕设备显示3列 */
@media screen and (max-width: 320px) {
  .subcategory-grid {
    grid-template-columns: repeat(3, 1fr);
  }
} 