// component/categorySelector/categorySelector.js
const app = getApp();

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 分类数据
    categories: {
      type: Array,
      value: []
    },
    // 显示模式：list（列表）或 card（卡片）
    mode: {
      type: String,
      value: 'list'
    },
    // 是否显示
    show: {
      type: Boolean,
      value: false
    },
    // 主题颜色
    themeColor: {
      type: String,
      value: '#4BA3E1'
    },
    // 是否显示设置按钮
    showSettings: {
      type: Boolean,
      value: false
    },
    // 设置按钮图标
    settingsIcon: {
      type: String,
      value: '/static/icon/settings.png'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 默认分类数据，如果没有传入则使用这个
    defaultCategories: [
      { id: 1, name: '餐饮', subName: '', icon: '/static/icon/cy.png', popupTitle: '餐饮消费', popupContent: '记录您在餐厅、外卖等餐饮方面的支出' },
      { id: 2, name: '三餐', subName: '', icon: '/static/icon/sc.png', popupTitle: '三餐记录', popupContent: '记录您的早餐、午餐、晚餐支出' },
      { id: 3, name: '柴米油盐', subName: '', icon: '/static/icon/cmyy.png', popupTitle: '日常用品', popupContent: '记录您购买的调味品、厨房用品等支出' },
      { id: 4, name: '食材', subName: '', icon: '/static/icon/sc2.png', popupTitle: '食材采购', popupContent: '记录您购买的肉类、蔬菜、水果等食材支出' },
      { id: 5, name: '零食', subName: '', icon: '/static/icon/ls.png', popupTitle: '零食记录', popupContent: '记录您购买的零食、饼干、糖果等支出' },
      { id: 6, name: '奶茶', subName: '', icon: '/static/icon/nc.png', popupTitle: '奶茶饮品', popupContent: '记录您购买的奶茶、果汁等饮品支出' },
      { id: 7, name: '咖啡', subName: '', icon: '/static/icon/kf.png', popupTitle: '咖啡记录', popupContent: '记录您购买的咖啡、茶等饮品支出' }
    ]
  },

  lifetimes: {
    attached: function () {
      // 在组件实例进入页面节点树时执行
      // 获取全局主题颜色
      if (app && app.globalData && app.globalData.selectedColor) {
        this.setData({
          themeColor: app.globalData.selectedColor
        });
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 切换显示模式（列表/卡片）
    toggleMode: function () {
      const newMode = this.data.mode === 'list' ? 'card' : 'list';
      this.setData({
        mode: newMode
      });
      // 触发模式变化事件
      this.triggerEvent('modeChange', { mode: newMode });
    },

    // 关闭选择器
    closeSelector: function () {
      this.setData({
        show: false
      });
      // 触发关闭事件
      this.triggerEvent('close');
    },

    // 选择分类
    selectCategory: function (e) {
      const index = e.currentTarget.dataset.index;
      const category = this.data.categories.length > 0 ?
        this.data.categories[index] :
        this.data.defaultCategories[index];

      // 直接触发选择事件
      this.triggerEvent('select', { category });

      // 关闭选择器
      this.closeSelector();
    },

    // 添加新分类
    addCategory: function () {
      // 触发添加事件
      this.triggerEvent('add');
    },

    // 阻止冒泡
    preventBubble: function () {
      // 阻止点击内容区域时关闭弹窗
      return;
    },

    // 设置按钮点击事件
    onSettingsClick: function() {
      // 触发设置事件
      this.triggerEvent('settings');
    }
  }
})
