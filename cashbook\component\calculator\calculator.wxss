.box{
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background-color: #fff;
  z-index: 100; /* 确保计算器在最上层 */
  overflow-x: hidden; /* 隐藏横向滚动条 */
  box-sizing: border-box; /* 确保padding不会增加元素宽度 */
}
.header{
  display: flex; /* 改为flex布局，允许元素自动调整大小 */
  flex-direction: row; /* 正向排列，从左到右 */
  align-items: center;
  justify-content: space-between; /* 元素之间平均分布 */
  padding: 10px 5px; /* 添加左右padding，确保内容不贴边 */
  width: 100%;
  box-sizing: border-box; /* 确保padding不会增加元素宽度 */
  overflow: hidden; /* 隐藏溢出内容，防止滚动条 */
  position: relative; /* 为绝对定位提供参考 */
}

/* 输入容器样式 */
.input-container {
  flex: 1 1 auto; /* 可伸缩，可压缩，自动宽度 */
  /* min-width: 150px; 设置最小宽度，确保placeholder文字能够显示 */
  overflow: hidden;
  margin-right: 5px; /* 添加右边距 */
  padding-left: 5px; /* 添加左内边距 */
  transition: all 0.3s; /* 平滑过渡效果 */
  position: relative; /* 为绝对定位提供参考 */
  order: 1; /* 在flex布局中的顺序，确保显示在最左侧 */
}

/* 输入框样式 */
.input-container input {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis; /* 文本溢出显示省略号 */
  white-space: nowrap; /* 防止文本换行 */
}

/* 字符计数样式 */
.char-count {
  position: absolute;
  right: 5px;
  bottom: -18px;
  font-size: 12px;
  color: #999;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 0 5px;
  border-radius: 10px;
}

/* 图片按钮样式 */
.photo {
  min-width: 40px; /* 增加最小宽度，确保图标完整显示 */
  max-width: 60px; /* 最大宽度 */
  flex: 0 0 40px; /* 不增长，不收缩，固定宽度 */
  margin-right: 5px;
  white-space: nowrap;
  text-align: center; /* 居中显示 */
  overflow: hidden; /* 超出部分隐藏 */
  transition: all 0.3s; /* 平滑过渡效果 */
  display: flex;
  align-items: center;
  justify-content: center;
  order: 2; /* 在flex布局中的顺序，确保显示在中间 */
  background-color: #f5f5f5; /* 添加背景色，使图标区域更明显 */
  border-radius: 4px; /* 添加圆角 */
  padding: 0 5px; /* 添加内边距 */
}

/* 图片按钮文字样式 */
.photo text {
  margin-left: 2px;
  transition: all 0.3s; /* 平滑过渡效果 */
  font-size: 14px;
}

.header image{
  width: 24px;
  height: 24px;
  object-fit: contain; /* 确保图片完整显示 */
  margin: 0 auto; /* 居中显示 */
  display: block; /* 块级显示 */
}

/* 结果容器样式 */
.result-container {
  display: flex;
  justify-content: flex-end; /* 从右侧开始排列 */
  align-items: center;
  flex: 0 0 auto; /* 不增长，不收缩，自动宽度 */
  min-width: 80px; /* 设置最小宽度 */
  max-width: none; /* 不限制最大宽度，允许自由扩展 */
  overflow: visible; /* 允许内容溢出 */
  transition: all 0.3s; /* 平滑过渡效果 */
  box-sizing: border-box; /* 确保padding不会增加元素宽度 */
  order: 3; /* 在flex布局中的顺序，确保显示在最右侧 */
  margin-left: 5px; /* 添加左边距 */
}

/* 结果包装器 */
.result-wrapper {
  color: red;
  display: flex;
  flex-direction: row; /* 正常排列，货币符号在左侧 */
  align-items: center;
  justify-content: flex-start; /* 从左侧开始排列 */
  white-space: nowrap; /* 防止文本换行 */
  overflow: hidden; /* 超出部分隐藏 */
  width: 100%;
  position: relative; /* 为绝对定位的子元素提供参考 */
  box-sizing: border-box; /* 确保padding不会增加元素宽度 */
}

/* 货币符号样式 */
.currency-symbol {
  /* color: red; */
  font-weight: bold;
  font-size: 16px;
  margin-right: 4px; /* 增加与金额的间距 */
  display: inline-block; /* 确保符号正确显示 */
  position: relative; /* 允许微调位置 */
  top: 0; /* 微调垂直位置 */
}

/* 结果样式 */
.result {
  /* color: red; */
  font-weight: bold;
  white-space: nowrap; /* 防止文本换行 */
  overflow: visible; /* 允许内容溢出 */
  font-size: 16px;
  width: auto; /* 自动宽度 */
  min-width: 0; /* 允许元素被压缩到比内容更小 */
}

.body{
  display: grid;
  grid-template-columns: repeat(4,1fr);
  text-align: center;
  align-items: center;
  border-radius: 10px 10px 0 0;
  overflow: hidden;
  /* 背景颜色现在通过内联样式动态设置 */
  padding: 10px;
  gap: 5px; /* 按钮之间的间距 */
  width: 100%;
  box-sizing: border-box; /* 确保padding不会增加元素宽度 */
}
.bitem{
  /* 背景颜色现在通过内联样式动态设置 */
  color: #333; /* 深灰色文字 */
  padding: 15px 0;
  border-radius: 10px; /* 圆角按钮 */
  font-size: 18px;
  margin: 2px;
}
/* 底部横线样式 */
/* .bottom-line {
  width: 30%;
  height: 5px;
  background-color: #555;
  border-radius: 3px;
  margin: 10px auto;
} */