/* 容器 */
.container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background-color: #ffffff;
  padding-bottom: 30rpx;
}

/* 导航栏 */
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  /* 导航栏的基本高度，不包括状态栏 */
  display: flex;
  align-items: center;
  background-color: #ffffff;
  z-index: 100;
  box-shadow: none;
}

.nav-bar-left {
  width: 120rpx;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 24rpx;
}

.close-button {
  width: 68rpx;
  height: 68rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #ffffff;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1),
    0 10px 20px rgba(0, 0, 0, 0.08);
}

.nav-bar-title {
  flex: 1;
  text-align: center;
  font-size: 34rpx;
  font-weight: 500;
  color: #333333;
}

.nav-bar-right {
  width: 100rpx;
}

/* 内容区域 */
.content {
  padding: 0 30rpx;
  /* margin-top 将在 WXML 中通过内联样式动态设置 */
}

/* 筛选标题栏 */
.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f6f6f6;
}

.filter-title {
  font-size: 32rpx;
  color: #333333;
  font-weight: normal;
}

.filter-btn {
  background-color: #ff9999;
  color: #ffffff;
  font-size: 26rpx;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
}

/* 账单列表 */
.bill-list {
  padding: 10rpx 0;
}

/* 日期区域 */
.date-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 30rpx 0 20rpx;
}

.date-left {
  display: flex;
  align-items: center;
}

.date-text {
  font-size: 30rpx;
  color: #333333;
  margin-right: 10rpx;
}

.weekday-text {
  font-size: 28rpx;
  color: #999999;
  margin-right: 10rpx;
}

.date-icon {
  color: #cccccc;
}

.date-right {
  display: flex;
  align-items: center;
}

.expense-tag {
  font-size: 26rpx;
  color: #666666;
  padding-right: 10rpx;
}

/* 账单项 */
.bill-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f6f6f6;
}

.bill-left {
  margin-right: 20rpx;
}

.bill-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bill-icon image {
  width: 80%;
  height: 80%;
}

.bill-middle {
  flex: 1;
}

.bill-title {
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 8rpx;
}

.bill-accounts {
  font-size: 28rpx;
  color: #999999;
}

.account-arrow {
  margin: 0 10rpx;
  color: #cccccc;
}

.bill-right {
  text-align: right;
}

.bill-amount {
  font-size: 34rpx;
  color: #ff9999;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.bill-interest {
  font-size: 26rpx;
  color: #ff9999;
}

.oneColonm {
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  margin: 5px 10px;
  padding: 3px 0;
}

.time {
  background-color: #a1b584;
  padding: 3px 8px;
  border-radius: 15px;
  color: white;
  width: fit-content;
  font-size: 13px;
  justify-self: end;
}

/* 账单容器 */
.bill-container {
  margin: 0;
  padding-bottom: 120px;
  /* 增加底部内边距，避免内容被遮挡 */
  min-height: calc(100vh);
  /* 设置最小高度确保有足够的滚动空间 */
}