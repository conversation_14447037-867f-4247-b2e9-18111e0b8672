Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 控制弹窗是否显示
    show: {
      type: Boolean,
      value: false
    },
    // 账本列表
    bookList: {
      type: Array,
      value: []
    },
    // 是否多选模式
    isMultiSelect: {
      type: Boolean,
      value: false
    },
    // 是否卡片模式
    isCardMode: {
      type: Boolean,
      value: true
    },
    // 已选中的账本ID列表
    selectedBookIds: {
      type: Array,
      value: []
    },
    // 是否已选中全部账本
    allBooksSelected: {
      type: Boolean,
      value: false
    },
    // 主题颜色
    themeColor: {
      type: String,
      value: '#a5ddb9'
    },
    // 是否显示选择指示器（单选/多选圆点）
    showSelectionIndicator: {
      type: Boolean,
      value: true
    },
    // 是否显示选择模式切换（单选/多选切换）
    showSelectModeToggle: {
      type: Boolean,
      value: true
    },
    // 是否显示全部账本选项
    showAllBooksOption: {
      type: Boolean,
      value: true
    },
    // 是否显示添加按钮
    showAddButton: {
      type: Boolean,
      value: true
    },
    // 是否显示视图模式切换按钮
    showViewModeToggle: {
      type: Boolean,
      value: true
    },
    // 是否显示重新加载选项
    showReloadOption: {
      type: Boolean,
      value: true
    },
    // 弹窗标题
    title: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 内部处理用数据
    internalBookList: []
  },

  observers: {
    'bookList, selectedBookIds, allBooksSelected': function(bookList, selectedBookIds, allBooksSelected) {
      // 处理账本列表，设置选中状态
      const internalBookList = (bookList || []).map(book => {
        return {
          ...book,
          selected: selectedBookIds.includes(book.id)
        };
      });
      
      this.setData({
        internalBookList: internalBookList
      });
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 关闭账本选择器
     */
    closeBookSelector: function() {
      this.triggerEvent('close');
    },

    /**
     * 弹窗状态变化
     */
    onPopupChange: function(e) {
      // 直接传递 t-popup 组件的 visible-change 事件数据
      this.triggerEvent('visibleChange', e.detail);
    },

    /**
     * 切换视图模式（列表/卡片）
     */
    toggleViewMode: function() {
      const isCardMode = !this.data.isCardMode;
      this.setData({
        isCardMode: isCardMode
      });
      this.triggerEvent('viewModeChange', { isCardMode });
    },

    /**
     * 切换选择模式（单选/多选）
     */
    toggleSelectMode: function() {
      this.triggerEvent('selectModeChange', { isMultiSelect: !this.data.isMultiSelect });
    },

    /**
     * 选择账本（单选模式）
     */
    selectBook: function(e) {
      const bookId = e.currentTarget.dataset.id;
      
      // 如果是多选模式，切换选中状态
      if (this.data.isMultiSelect) {
        this.toggleBookSelection(e);
        return;
      }

      // 单选模式下，触发选择事件
      const selectedBook = this.data.internalBookList.find(book => book.id === bookId);
      if (selectedBook) {
        this.triggerEvent('select', { bookId, book: selectedBook });
      }
    },

    /**
     * 选择全部账本
     */
    selectAllBooks: function() {
      this.triggerEvent('selectAll');
    },

    /**
     * 切换单个账本选择状态（多选模式）
     */
    toggleBookSelection: function(e) {
      const bookId = e.currentTarget.dataset.id;
      
      // 更新内部账本列表中的选中状态
      const internalBookList = this.data.internalBookList.map(book => {
        if (book.id === bookId) {
          return {
            ...book,
            selected: !book.selected
          };
        }
        return book;
      });
      
      this.setData({
        internalBookList: internalBookList
      });
      
      // 获取选中的账本ID
      const selectedBooks = internalBookList.filter(book => book.selected);
      const selectedIds = selectedBooks.map(book => book.id);
      
      this.triggerEvent('selectionChange', { selectedIds });
    },

    /**
     * 确认账本选择（多选模式）
     */
    confirmBookSelection: function() {
      // 获取选中的账本ID
      const selectedBooks = this.data.internalBookList.filter(book => book.selected);
      const selectedIds = selectedBooks.map(book => book.id);
      
      this.triggerEvent('confirm', { 
        selectedIds,
        selectedBooks,
        allBooksSelected: this.data.allBooksSelected
      });
    },

    /**
     * 添加新账本
     */
    addNewBook: function() {
      this.triggerEvent('add');
    },

    /**
     * 重新加载账本列表
     */
    reloadBookList: function() {
      this.triggerEvent('reload');
    }
  }
}) 