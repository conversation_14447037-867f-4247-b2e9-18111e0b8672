// 分类详情页面
Page({
  /**
   * 页面的初始数据
   */
  data: {
    category: {}, // 当前分类信息
    subCategories: [], // 子分类列表
    activeNames: ['1'], // 默认展开的折叠面板，默认展开子分类列表
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 从路由参数获取分类ID
    const categoryId = options.id;
    const categoryType = options.type || 'expense'; // 默认为支出分类
    
    // 加载分类详情和子分类
    this.loadCategoryDetail(categoryId, categoryType);
  },

  /**
   * 加载分类详情和子分类
   */
  loadCategoryDetail: function (categoryId, categoryType) {
    // 显示加载中提示
    wx.showLoading({
      title: '加载中...',
      mask: true
    });
    
    // 模拟获取分类详情和子分类列表
    // 实际项目中应该调用API获取数据
    setTimeout(() => {
      // 模拟分类数据
      let category = {};
      let subCategories = [];
      
      if (categoryType === 'expense') {
        // 支出分类示例数据
        if (categoryId == 1) { // 餐饮分类
          category = { 
            id: 1, 
            name: '餐饮', 
            icon: '/static/icons/food.png' 
          };
          subCategories = [
            { id: 101, name: '三餐', icon: '/static/icons/meal.png' },
            { id: 102, name: '调味油盐', icon: '/static/icons/spice.png' },
            { id: 103, name: '食材', icon: '/static/icons/ingredient.png' },
            { id: 104, name: '零食', icon: '/static/icons/snack.png' },
            { id: 105, name: '奶茶', icon: '/static/icons/milk-tea.png' },
            { id: 106, name: '咖啡', icon: '/static/icons/coffee.png' }
          ];
        } else {
          category = { 
            id: categoryId, 
            name: '其他分类', 
            icon: '/static/icons/other.png' 
          };
          subCategories = [
            { id: 201, name: '子分类1', icon: '/static/icons/subcategory.png' },
            { id: 202, name: '子分类2', icon: '/static/icons/subcategory.png' }
          ];
        }
      } else {
        // 收入分类示例数据
        category = { 
          id: categoryId, 
          name: '收入分类', 
          icon: '/static/icons/income.png' 
        };
        subCategories = [
          { id: 301, name: '工资', icon: '/static/icons/salary.png' },
          { id: 302, name: '奖金', icon: '/static/icons/bonus.png' }
        ];
      }
      
      // 更新页面数据
      this.setData({
        category: category,
        subCategories: subCategories
      });
      
      // 隐藏加载提示
      wx.hideLoading();
    }, 500);
  },

  /**
   * 折叠面板状态变化处理
   */
  onCollapseChange(event) {
    this.setData({
      activeNames: event.detail
    });
  },

  /**
   * 显示更多选项
   */
  showOptions: function () {
    wx.showActionSheet({
      itemList: ['编辑分类', '删除分类'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 编辑分类
          this.editCategory();
        } else if (res.tapIndex === 1) {
          // 删除分类
          this.deleteCategory();
        }
      }
    });
  },

  /**
   * 编辑分类
   */
  editCategory: function () {
    wx.showToast({
      title: '编辑分类功能待实现',
      icon: 'none'
    });
  },

  /**
   * 删除分类
   */
  deleteCategory: function () {
    wx.showModal({
      title: '确认删除',
      content: '删除分类将同时删除所有子分类，是否继续？',
      confirmColor: '#ff0000',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '删除功能待实现',
            icon: 'none'
          });
        }
      }
    });
  },

  /**
   * 编辑子分类
   */
  editSubCategory: function (e) {
    const subCategoryId = e.currentTarget.dataset.id;
    wx.showToast({
      title: `编辑子分类 ID: ${subCategoryId}`,
      icon: 'none'
    });
  },

  /**
   * 添加子分类
   */
  addSubCategory: function () {
    wx.showToast({
      title: '添加子分类功能待实现',
      icon: 'none'
    });
  }
}); 