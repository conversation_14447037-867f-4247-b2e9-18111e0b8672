<wxs src="../wxs/utils.wxs" module="utils" />
<wxs src="./index.wxs" module="computed" />

<view class="custom-class {{ utils.bem('loading', { vertical }) }}">
  <view
    class="van-loading__spinner van-loading__spinner--{{ type }}"
    style="{{ computed.spinnerStyle({ color, size }) }}"
  >
    <view
      wx:if="{{ type === 'spinner' }}"
      wx:for="{{ array12 }}"
      wx:key="index"
      class="van-loading__dot"
    />
  </view>
  <view class="van-loading__text" style="{{ computed.textStyle({ textSize }) }}">
    <slot />
  </view>
</view>
