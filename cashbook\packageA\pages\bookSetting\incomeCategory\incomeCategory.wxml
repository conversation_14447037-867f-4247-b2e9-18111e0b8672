<!-- 收支分类页面 -->
<view class="category-container">
  <!-- 顶部导航栏 -->
  <view class="nav-bar" style="padding-top: {{statusBarHeight}}px; height: {{navBarHeight}}px;">
    <view class="back-icon" bindtap="goBack">
      <van-icon name="arrow-left" size="20px" color="#fff" />
    </view>
    <view class="tab-container">
      <view class="tab {{activeTab === 'expense' ? 'active' : ''}}" bindtap="switchTab" data-type="expense">
        支出分类
      </view>
      <view class="tab {{activeTab === 'income' ? 'active' : ''}}" bindtap="switchTab" data-type="income">
        收入分类
      </view>
    </view>
    <view class="book-selector" bindtap="switchBook">
      <van-icon name="browsing-history" size="18px" color="#fff" />
      <text>{{currentBook || '默认账本'}}</text>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="content-area" style="padding-top: calc({{statusBarHeight}}px + {{navBarHeight}}px + 20rpx); padding-bottom: 51px;">
    <!-- 分类列表 -->
    <view class="category-list">
      <block wx:if="{{categories.length > 0}}">
        <block wx:for="{{categories}}" wx:key="id">
          <view class="category-card">
            <!-- 分类项头部（可点击展开/折叠） -->
            <view class="category-item" catchtap="toggleCategory" data-id="{{item.id}}">
              <view class="item-left">
                <van-icon name="play" size="16px" color="#ccc" class="{{activeNames.indexOf(item.id) !== -1 ? 'arrow-down' : ''}}" />

                <view class="category-icon">
                  <image src="{{item.icon}}" mode="aspectFit"></image>
                </view>
                <view class="category-name">{{item.name}}</view>
              </view>
              <view class="item-right">
              </view>
            </view>
            
            <!-- 子分类内容（条件渲染） -->
            <block wx:if="{{activeNames.indexOf(item.id) !== -1}}">
              <view class="subcategory-container">
                <view class="subcategory-grid">
                  <block wx:for="{{item.subCategories || defaultSubCategories[item.id] || []}}" wx:key="id" wx:for-item="subItem">
                    <view class="subcategory-item" catchtap="editSubCategory" data-id="{{subItem.id}}">
                      <view class="subcategory-icon">
                        <image src="{{subItem.icon}}" mode="aspectFit"></image>
                      </view>
                      <view class="subcategory-name">{{subItem.name}}</view>
                    </view>
                  </block>
                  
                  <!-- 添加子分类按钮 -->
                  <view class="subcategory-item add-item" catchtap="addSubCategory" data-parent-id="{{item.id}}">
                    <view class="add-icon">
                      <van-icon name="plus" size="24px" color="#333" />
                    </view>
                    <view class="subcategory-name">添加子分类</view>
                  </view>
                </view>
              </view>
            </block>
          </view>
        </block>
      </block>
      <block wx:else>
        <view class="empty-tip">
          <image src="/static/images/empty-category.png" mode="aspectFit" class="empty-image"></image>
          <text class="empty-text">暂无分类，点击下方按钮添加</text>
        </view>
      </block>
    </view>

    <!-- 底部添加按钮 -->
    <view class="add-button" bindtap="addCategory">
      <text>添加分类</text>
    </view>
  </view>
</view> 