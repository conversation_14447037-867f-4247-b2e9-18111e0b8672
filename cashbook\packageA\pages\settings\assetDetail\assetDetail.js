// 资产详情页面逻辑
const app = getApp()
const util = require('../../../../utils/index.js')
import { getUserAccountDetail } from '../../../../api/account/index'

Page({
  data: {
    // 系统信息
    statusBarHeight: wx.getSystemInfoSync().statusBarHeight,
    navHeight: 44,

    // 资产信息
    asset: {
      id: '',
      name: '招商银行',
      type: '信用卡',
      cardNumber: '4321',
      icon: '/assets/icons/bank/zhaoshang.png',
      balance: '4,860.00',
      availableLimit: '5,140.00',
      totalLimit: '10,000.00',
      billDay: '1',
      repaymentDay: '10',
      nextBillMonth: '6',
      nextRepaymentMonth: '6',
      billStatus: '下期',
      daysToNextBill: '4.25',
      daysToRepayment: '13.25'
    },

    loading: false,

    // 进度条宽度百分比
    progressWidth: 52, // 基于已用额度计算: (总额度 - 可用额度) / 总额度 * 100

    // 主题颜色
    themeColor: '#8dc63f',

    // 年份选择
    selectedYear: 2025,
    isYearPickerOpen: false,

    // 当前账单
    currentBill: {
      month: '6',
      startDate: '5月2日',
      endDate: '6月1日',
      expense: '10.00',
      income: '0.00'
    },

    // 年份选项
    yearOptions: []
  },

  onLoad: function (options) {
    // 获取资产ID
    const assetId = options.id || ''

    // 设置状态栏和导航栏高度
    const systemInfo = wx.getSystemInfoSync()
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight,
      navHeight: 44
    })

    // 获取主题颜色
    this.getThemeColor()

    // 初始化年份选项
    this.initYearOptions()

    // 加载资产详情
    if (assetId) {
      this.setData({
        'asset.id': assetId
      })
      this.loadAssetDetail(assetId)
    }
  },

  onShow: function () {
    // 每次页面显示时刷新主题颜色
    this.getThemeColor()

    // 如果有资产ID，刷新资产详情
    if (this.data.asset.id) {
      this.loadAssetDetail(this.data.asset.id)
    }
  },

  // 初始化年份选项
  initYearOptions: function () {
    const yearOptions = [2025, 2024, 2023, 2022, 2021]

    this.setData({
      yearOptions: yearOptions,
      selectedYear: 2025
    })
  },

  // 获取主题颜色
  getThemeColor: function () {
    let themeColor = '#8dc63f' // 默认颜色 - 浅绿色

    if (app.globalData && app.globalData.selectedColor) {
      themeColor = app.globalData.selectedColor
    }

    this.setData({
      themeColor: themeColor
    })
  },

  // 加载资产详情
  loadAssetDetail: function (assetId) {
    this.setData({ loading: true })

    wx.showLoading({
      title: '加载中...'
    })

    // 调用API获取资产详情
    getUserAccountDetail({ user_account_id: assetId })
      .then((res) => {
        console.log('获取账户详情成功:', res)
        if (res.code === 1 && res.data) {
          // 处理API返回的数据
          const apiData = res.data

          // 格式化资产数据
          const formattedAsset = {
            id: apiData.id,
            name: apiData.name || '未命名账户',
            type: apiData.account_type === 'liabilities' ? '信用卡' : '借记卡',
            cardNumber: apiData.cardnum || '',
            icon: apiData.image || '/assets/icons/bank/default.png',
            balance: apiData.money || '0.00',
            username: apiData.username || '',
            billDay: apiData.bill_date || '',
            repaymentDay: apiData.repayment_date || '',
            nextBillMonth: new Date().getMonth() + 1,
            nextRepaymentMonth: new Date().getMonth() + 1,
            billStatus: '本期'
          }

          // 如果是信用卡或负债类型，添加额度信息
          if (apiData.account_type === 'liabilities' && apiData.debt) {
            const debt = parseFloat(apiData.debt) || 0
            const money = parseFloat(apiData.money) || 0
            const availableLimit = (debt - Math.abs(money)).toFixed(2)

            formattedAsset.availableLimit = availableLimit
            formattedAsset.totalLimit = apiData.debt

            // 计算进度条宽度
            const progressWidth = Math.round((Math.abs(money) / debt) * 100)
            this.setData({ progressWidth: progressWidth })
          }

          // 更新资产数据
          this.setData({
            asset: formattedAsset,
            loading: false
          })
        } else {
          wx.showToast({
            title: res.msg || '获取账户详情失败',
            icon: 'none'
          })
          this.setData({ loading: false })
        }

        wx.hideLoading()
      })
      .catch((err) => {
        console.error('获取账户详情失败:', err)
        wx.showToast({
          title: err.msg || '获取账户详情失败',
          icon: 'none'
        })
        this.setData({ loading: false })
        wx.hideLoading()
      })
  },

  // 返回上一页
  onBackTap: function () {
    wx.navigateBack()
  },

  // 显示操作菜单
  showOperationMenu: function () {
    wx.showActionSheet({
      itemList: ['隐藏资产', '删除资产'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 隐藏资产
          this.hideAsset()
        } else if (res.tapIndex === 1) {
          // 删除资产
          this.deleteAsset()
        }
      }
    })
  },

  // 隐藏资产
  hideAsset: function () {
    wx.showModal({
      title: '提示',
      content: '确定要隐藏该资产吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '已隐藏',
            icon: 'success'
          })
        }
      }
    })
  },

  // 删除资产
  deleteAsset: function () {
    wx.showModal({
      title: '提示',
      content: '确定要删除该资产吗？删除后无法恢复',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '已删除',
            icon: 'success',
            success: () => {
              setTimeout(() => {
                wx.navigateBack()
              }, 1500)
            }
          })
        }
      }
    })
  },

  // 编辑资产
  editAsset: function () {
    wx.navigateTo({
      url: '/packageA/pages/settings/createAccount/createAccount?id=' + this.data.asset.id + '&edit=true'
    })
  },

  // 切换年份选择器
  toggleYearPicker: function () {
    this.setData({
      isYearPickerOpen: !this.data.isYearPickerOpen
    })

    if (this.data.isYearPickerOpen) {
      this.showYearPicker()
    }
  },

  // 显示年份选择器
  showYearPicker: function () {
    wx.showActionSheet({
      itemList: this.data.yearOptions.map((year) => year + '年'),
      success: (res) => {
        this.setData({
          selectedYear: this.data.yearOptions[res.tapIndex],
          isYearPickerOpen: false
        })

        // 加载选中年份的账单数据
        this.loadYearBills(this.data.yearOptions[res.tapIndex])
      }
    })
  },

  // 加载年度账单
  loadYearBills: function (year) {
    // 这里应该调用API获取指定年份的账单数据
    // 暂时使用模拟数据
    wx.showLoading({
      title: '加载中...'
    })

    setTimeout(() => {
      // 模拟更新月份账单数据
      const monthBills = this.generateMonthBills(year)

      // 取第一个月份的账单作为当前显示的账单
      if (monthBills && monthBills.length > 0) {
        this.setData({
          currentBill: monthBills[0]
        })
      }

      wx.hideLoading()
    }, 300)
  },

  // 生成月份账单数据（模拟）
  generateMonthBills: function (year) {
    const bills = []

    // 为2025年生成特定数据，与图片一致
    if (year === 2025) {
      bills.push({
        month: '6',
        startDate: '5月2日',
        endDate: '6月1日',
        expense: '10.00',
        income: '0.00'
      })
    } else {
      // 为其他年份生成随机数据
      for (let i = 1; i <= 12; i++) {
        const monthBill = {
          month: i.toString(),
          startDate: `${i === 1 ? 12 : i - 1}月${i === 1 ? 2 : 1}日`,
          endDate: `${i}月1日`,
          expense: (Math.random() * 1000).toFixed(2),
          income: (Math.random() * 200).toFixed(2)
        }
        bills.push(monthBill)
      }
    }

    return bills.reverse()
  },

  // 查看月账单详情
  viewMonthBill: function () {
    // 跳转到月账单详情页面
    wx.navigateTo({
      url: '/packageA/pages/billDetail/billDetail?month=' + this.data.currentBill.month + '&year=' + this.data.selectedYear
    })
  },

  // 记一笔交易
  recordTransaction: function () {
    // 跳转到记账页面
    wx.navigateTo({
      url: '/pages/add/add?assetId=' + this.data.asset.id
    })
  },

  // 还款
  repayDebt: function () {
    // 跳转到还款页面
    wx.navigateTo({
      url: '/packageA/pages/repayment/repayment?assetId=' + this.data.asset.id + '&amount=' + this.data.asset.balance.replace(/,/g, '')
    })
  }
})
