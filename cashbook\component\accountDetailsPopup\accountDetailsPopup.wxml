<!-- components/accountDetailsPopup/accountDetailsPopup.wxml -->
<view class="custom-popup-container {{animating ? 'visible' : ''}}" wx:if="{{popupVisible}}" catchtouchmove="stopPropagation">
  <view class="custom-popup-mask" bindtap="onClickOverlay"></view>
  <view class="custom-popup-content custom-popup-{{position}} {{round ? 'custom-popup-round' : ''}}" style="max-height:{{maxHeight}}; display: flex; flex-direction: column;">
    <view class="header-with-actions">
      <view class="popup-close-btn" bindtap="onClose">
        <van-icon name="cross" size="20px" />
      </view>
      <view class="action-buttons">
        <view class="action-btn delete-btn" bindtap="onDelete">删除</view>
        <view class="action-btn refund1-btn" bindtap="onRefund" style="background-color: {{titleLineColor || '#a2b486'}}; color: white;">
          退款
        </view>
        <view class="action-btn edit-btn" bindtap="onEdit">修改</view>
      </view>
    </view>
    <scroll-view class="account-details-body" scroll-y="true" enhanced="true" show-scrollbar="false" bounces="true">
      <!-- 分类 -->
      <view class="detail-section">
        <view class="section-title-wrapper">
          <view class="title-line" style="background-image: linear-gradient(to bottom, {{titleLineColor || '#a2b486'}}, white);"></view>
          <view class="section-title">分类</view>
        </view>
        <view class="category-item" bindtap="onCategorySelected" data-name="{{categoryName}}" data-time="{{time}}">
          <view class="category-icon">
            <image src="{{categoryIcon}}" wx:if="{{categoryIcon}}"></image>
            <text class="category-icon-text" wx:else>{{categoryName[0]}}</text>
          </view>
          <text class="category-name">{{categoryName || '退款'}}</text>
          <van-icon name="arrow" />
        </view>
      </view>
      <!-- 账单 -->
      <view class="detail-section">
        <view class="section-title-wrapper">
          <view class="title-line" style="background-image: linear-gradient(to bottom, {{titleLineColor || '#a2b486'}}, white);"></view>
          <view class="section-title">账单</view>
        </view>
        <view class="detail-item">
          <text class="detail-label">金额</text>
          <text class="detail-value amount">¥ {{amount || '0.00'}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">时间</text>
          <text class="detail-value">{{datetime || ''}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">账本</text>
          <text class="detail-value">{{accountBook || '默认账本'}}</text>
        </view>
      </view>
      <!-- 资产 -->
      <view class="detail-section">
        <view class="section-title-wrapper">
          <view class="title-line" style="background-image: linear-gradient(to bottom, {{titleLineColor || '#a2b486'}}, white);"></view>
          <view class="section-title">资产</view>
        </view>
        <view class="detail-item">
          <text class="detail-label">资产账户</text>
          <text class="detail-value asset">{{assetAccount || '邮政'}}</text>
        </view>
      </view>
      <!-- 退款 -->
      <view class="detail-section">
        <view class="refund-header">
          <view class="refund-title-wrapper">
            <view class="title-line" style="background-image: linear-gradient(to bottom, {{titleLineColor || '#a2b486'}}, white);"></view>
            <text class="refund-title">退款账单</text>
          </view>
          <view class="refund-btn" bindtap="onRefund" style="background-color: {{titleLineColor || '#a2b486'}}; color: white;">
            退款
          </view>
        </view>
        <view class="no-refund-text">无退款账单</view>
      </view>
    </scroll-view>
    <slot></slot>
  </view>
</view>
<!-- <popup-dialog visible="{{showCategoryInitPopup}}" closeButtonPosition="left" title="收债2025-5(共1笔)" position="bottom" bind:close="closeCategoryInitPopup">
  <view class="more-menu-list">
    本月账单&&排序
    <view class="oneColonm">
      <view>账单明细</view>
      <view class="time" bindtap="toggleSort">{{sortType === 'amount' ? '按金额' : '按时间'}}</view>
    </view>
    账单内容区域
    <view class="bill-container">
      这会回陷入死循环
      <contentList list="{{categoryList}}" sortType="{{sortType}}" bind:itemTap="onContentItemTap" />
    </view>
  </view>
</popup-dialog> -->
