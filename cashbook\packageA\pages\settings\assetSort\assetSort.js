// 导入API接口
import { getUserAccounts, updateAccountSort } from '../../../../api/account/index'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    assetList: [], // 资产列表
    sortedList: [], // 排序后的列表
    themeColor: '#3cc51f', // 主题颜色
    isDragging: false, // 是否正在拖动
    currentIndex: -1, // 当前拖动项的索引
    isSaving: false, // 是否正在保存
    touchStartY: 0, // 触摸开始的Y坐标
    touchMoveY: 0 // 触摸移动的Y坐标
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 获取主题颜色
    this.getThemeColor()
    // 获取资产列表
    this.getAssetList()
  },

  /**
   * 获取主题颜色
   */
  getThemeColor: function () {
    // 从全局获取主题颜色
    const app = getApp()
    if (app && app.globalData && app.globalData.selectedColor) {
      this.setData({
        themeColor: app.globalData.selectedColor
      })
    }
  },

  /**
   * 获取资产列表
   */
  getAssetList: function () {
    wx.showLoading({
      title: '加载中...'
    })

    getUserAccounts({
      show_type: 'group',
      status: 'normal' // 只获取正常状态的资产（非隐藏）
    })
      .then((res) => {
        wx.hideLoading()
        if (res.code === 1 && res.data) {
          // 处理资产数据
          const assetList = this.processAssetData(res.data)
          this.setData({
            assetList: assetList,
            sortedList: [...assetList] // 复制一份作为排序后的列表
          })
        } else {
          wx.showToast({
            title: res.msg || '获取资产列表失败',
            icon: 'none'
          })
        }
      })
      .catch((err) => {
        wx.hideLoading()
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
        console.error('获取资产列表失败:', err)
      })
  },

  /**
   * 处理资产数据
   */
  processAssetData: function (data) {
    if (!data || !data.data) return []

    // 提取所有资产
    let allAssets = []

    // 遍历分组数据
    data.data.forEach((group) => {
      if (group.data && Array.isArray(group.data)) {
        // 处理每个资产项
        const assets = group.data.map((item) => {
          // 判断是否为负数
          const isNegative = item.account_type === 'liabilities' || parseFloat(item.money) < 0
          // 格式化金额，取绝对值
          const amount = Math.abs(parseFloat(item.money)).toFixed(2)

          return {
            id: item.id,
            name: item.name,
            subType: item.account_name,
            type: item.account_type === 'liabilities' ? 'credit' : 'debit',
            bankName: item.bank_name || '',
            cardNumber: item.cardnum ? item.cardnum.substr(-4) : '',
            amount: amount,
            isNegative: isNegative,
            repaymentDate: item.repayment_date || '',
            availableLimit: item.debt ? (parseFloat(item.debt) - Math.abs(parseFloat(item.money))).toFixed(2) : '',
            sortOrder: item.sort || 0
          }
        })

        allAssets = allAssets.concat(assets)
      }
    })

    // 按照原有排序顺序排序
    allAssets.sort((a, b) => a.sortOrder - b.sortOrder)

    return allAssets
  },

  /**
   * 长按开始拖动
   */
  onLongPress: function (e) {
    const index = e.currentTarget.dataset.index
    this.setData({
      isDragging: true,
      currentIndex: index
    })
  },

  /**
   * 触摸结束
   */
  onTouchEnd: function (e) {
    if (!this.data.isDragging) return

    this.setData({
      isDragging: false,
      currentIndex: -1
    })
  },

  /**
   * 拖动排序
   */
  onDrag: function (e) {
    if (!this.data.isDragging) return

    const { currentIndex } = this.data
    const targetIndex = e.currentTarget.dataset.index

    if (currentIndex !== targetIndex) {
      // 更新排序
      const sortedList = [...this.data.sortedList]
      const item = sortedList.splice(currentIndex, 1)[0]
      sortedList.splice(targetIndex, 0, item)

      this.setData({
        sortedList: sortedList,
        currentIndex: targetIndex
      })
    }
  },

  /**
   * 保存排序
   */
  saveSort: function () {
    if (this.data.isSaving) return

    this.setData({
      isSaving: true
    })

    wx.showLoading({
      title: '保存中...'
    })

    // 构建请求参数
    const sortData = this.data.sortedList.map((item, index) => {
      return {
        id: item.id,
        sort: index
      }
    })

    // 调用API保存排序
    updateAccountSort(sortData)
      .then((res) => {
        wx.hideLoading()

        if (res.code === 1) {
          wx.showToast({
            title: '保存成功',
            icon: 'success'
          })

          // 更新本地资产列表
          this.setData({
            assetList: this.data.sortedList,
            isSaving: false
          })

          // 返回上一页
          setTimeout(() => {
            wx.navigateBack()
          }, 1000)
        } else {
          wx.showToast({
            title: res.msg || '保存失败',
            icon: 'none'
          })
          this.setData({
            isSaving: false
          })
        }
      })
      .catch((err) => {
        wx.hideLoading()
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
        console.error('保存排序失败:', err)
        this.setData({
          isSaving: false
        })
      })
  }
})
