<!-- components/customPopup/customPopup.wxml -->
<!-- 添加账本的选择分类初始化的弹窗组件 -->
<view class="custom-popup-container {{animating ? 'visible' : ''}}" wx:if="{{popupVisible}}" catchtouchmove="stopPropagation">
  <view class="custom-popup-mask" bindtap="onClickOverlay"></view>
  <view class="custom-popup-content custom-popup-{{position}} {{round ? 'custom-popup-round' : ''}}">
    <view class="custom-popup-header {{closeButtonPosition == 'left' ? 'reverse-header' : ''}}">
      <view class="custom-popup-close" bindtap="onClose" wx:if="{{closeButtonPosition == 'left'}}">
        ×
      </view>
      <view class="custom-popup-title">{{title}}</view>
      <view class="custom-popup-close" bindtap="onClose" wx:if="{{closeButtonPosition == 'right'}}">
        ×
      </view>
    </view>
    <view class="custom-upload-button" bindtap="onCustomUpload" wx:if="{{showCustomUpload}}">
      <text>{{customUploadText}}</text>
    </view>
    <view class="custom-popup-body">
      <slot></slot>
    </view>
  </view>
</view>
