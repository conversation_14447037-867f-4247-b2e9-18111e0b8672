// 账本选择弹窗样式
.book-selector-popup {
    width: 100%;
    background-color: #fff;
    border-radius: 24rpx 24rpx 0 0;
    overflow: hidden;
    padding-bottom: env(safe-area-inset-bottom);
    display: flex;
    flex-direction: column;
    max-height: 550rpx;
    
    .popup-header {
      padding: 30rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1rpx solid #f5f5f5;
      flex-shrink: 0;
      height: 80rpx;
      box-sizing: border-box;
      
      .close-btn {
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .popup-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #333333;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
      }
      
      .header-actions {
        display: flex;
        align-items: center;
        margin-left: auto;
        
        .view-mode-switch {
          width: 80rpx;
          height: 80rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #f5f5f5;
          border-radius: 50%;
          margin-right: 20rpx;
        }
        
        .add-btn {
          padding: 10rpx 30rpx;
          background-color: #2c3e50;
          color: #fff;
          font-size: 28rpx;
          border-radius: 30rpx;
        }
      }
    }
    
    // 可滚动的内容区域容器
    .book-list-container {
      flex: 1;
      height: 0; // 设置高度为0，flex: 1会自动填充剩余空间
      min-height: 0; // 确保flex容器中可以滚动
      overflow-y: auto;
    }
    
    .book-list {
      padding: 20rpx;
      
      &.card-mode {
        padding: 20rpx 10rpx;
        
        .book-card-grid {
          display: flex;
          flex-wrap: wrap;
          
          .book-card {
            width: 47%;
            margin: 1.5%;
            position: relative;
            
            .book-card-wrap {
              position: relative;
              border-radius: 16rpx;
              overflow: hidden;
              box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
              
              .book-card-content {
                position: relative;
                width: 100%;
                padding-bottom: 70%;
                background-color: #f5f5f5;
                
                image {
                  position: absolute;
                  top: 0;
                  left: 0;
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                }
                
                .book-card-abbr {
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                  font-size: 48rpx;
                  color: #fff;
                  font-weight: bold;
                }
                
                .book-card-title-wrap {
                  position: absolute;
                  bottom: 0;
                  left: 0;
                  width: 100%;
                  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
                  padding: 10rpx 0;
                  
                  .book-card-title {
                    font-size: 24rpx;
                    color: #fff;
                    text-align: center;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    padding: 0 10rpx;
                  }
                }
              }
              
              .book-card-radio {
                position: absolute;
                top: 10rpx;
                right: 10rpx;
                width: 36rpx;
                height: 36rpx;
                border-radius: 50%;
                border: 2rpx solid rgba(255, 255, 255, 0.8);
                background-color: rgba(255, 255, 255, 0.3);
                
                &.selected {
                  background-color: #a5ddb9;
                  border-color: #a5ddb9;
                  
                  &:after {
                    content: '';
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 20rpx;
                    height: 20rpx;
                    border-radius: 50%;
                    background-color: #fff;
                  }
                }
              }
            }
            
            &.selected {
              .book-card-wrap {
                box-shadow: 0 0 0 2rpx #a5ddb9;
              }
            }
          }
        }
      }
      
      &.list-mode {
        .book-item {
          display: flex;
          align-items: center;
          padding: 20rpx;
          border-radius: 12rpx;
          margin-bottom: 16rpx;
          position: relative;
          background-color: #f9f9f9;
          
          &.selected {
            background-color: #f0f9f4;
          }
          
          .book-icon {
            width: 80rpx;
            height: 80rpx;
            border-radius: 50%;
            background-color: #a5ddb9;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 20rpx;
            overflow: hidden;
            
            image {
              width: 100%;
              height: 100%;
              border-radius: 50%;
              object-fit: cover;
            }
            
            text {
              color: #fff;
              font-size: 32rpx;
              font-weight: bold;
            }
          }
          
          .all-books-icon {
            background-color: #f5f5f5;
          }
          
          .book-info {
            flex: 1;
            
            .book-name {
              font-size: 30rpx;
              color: #333;
              margin-bottom: 6rpx;
              font-weight: 500;
            }
            
            .book-desc {
              font-size: 24rpx;
              color: #999;
            }
          }
          
          .book-select-indicator {
            margin-left: 20rpx;
            
            .select-circle {
              width: 40rpx;
              height: 40rpx;
              border-radius: 50%;
              border: 2rpx solid #a5ddb9;
              display: flex;
              align-items: center;
              justify-content: center;
              
              &::after {
                content: '';
                width: 24rpx;
                height: 24rpx;
                border-radius: 50%;
                background-color: #a5ddb9;
              }
            }
          }
        }
      }
    }
    
    // 全部账本选项 (卡片模式)
    .all-books-option {
      display: flex;
      align-items: center;
      padding: 20rpx 30rpx;
      border-top: 1rpx solid #f5f5f5;
      background-color: #fff;
      
      &:active {
        background-color: #f9f9f9;
      }
      
      .all-books-icon-container {
        width: 60rpx;
        height: 60rpx;
        background-color: #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8rpx;
        margin-right: 20rpx;
      }
      
      .all-books-info {
        flex: 1;
        
        .all-books-title {
          font-size: 28rpx;
          color: #333;
        }
        
        .all-books-desc {
          font-size: 24rpx;
          color: #999;
          margin-top: 4rpx;
        }
      }
    }
    
    .divider {
      height: 1rpx;
      background-color: #f5f5f5;
    }
    
    .reload-hint {
      text-align: center;
      padding: 20rpx 0;
      font-size: 26rpx;
      color: #a5ddb9;
    }
    
    .popup-footer {
      padding: 20rpx 30rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-top: 1rpx solid #f5f5f5;
      background-color: #fff;
      flex-shrink: 0;
      height: 100rpx;
      box-sizing: border-box;
      
      .select-mode-container {
        display: flex;
        background-color: #f5f5f5;
        border-radius: 30rpx;
        overflow: hidden;
        
        .select-mode {
          padding: 10rpx 30rpx;
          font-size: 28rpx;
          color: #666;
          
          &.active {
            background-color: #a5ddb9;
            color: #fff;
          }
        }
      }
      
      .confirm-btn {
        padding: 10rpx 40rpx;
        background-color: #a5ddb9;
        color: #fff;
        font-size: 28rpx;
        border-radius: 30rpx;
      }
    }
  }