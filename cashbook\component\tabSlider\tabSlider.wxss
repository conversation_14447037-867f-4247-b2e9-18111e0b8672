.tab-container {
  position: relative;
  width: 100%;
  background-color: #3498db;
  overflow: hidden;
  padding: 20rpx 0;
  height: 80rpx;
  display: flex;
  align-items: center;
}

.slider-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 0;
  padding: 10rpx 0;
  box-sizing: border-box;
}

.tabs-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 2;
}

.tab-item {
  padding: 10rpx 40rpx;
  color: rgba(255, 255, 255, 0.7);
  font-size: 30rpx;
  text-align: center;
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-item.active {
  color: #ffffff;
  font-weight: bold;
}

.slider {
  position: absolute;
  height: 70rpx;
  background-color: #404855;
  border-radius: 35rpx;
  top: 50%;
  transform: translateY(-50%);
  transition: left 0.1s, width 0.1s;
  z-index: 1;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
}
