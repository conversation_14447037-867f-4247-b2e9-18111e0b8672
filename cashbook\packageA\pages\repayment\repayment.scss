/* 信用卡还款页面样式 */

/* 页面容器 */
.page-container {
  height: 100vh;
  background-color: #f8f8f8;
}

/* 状态栏 */
.status-bar {
  width: 100%;
}

/* 导航栏 */
.navigation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  position: relative;

  .nav-left {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 40rpx;
  }

  .nav-title {
    position: absolute;
    left: 0;
    right: 0;
    text-align: center;
    font-size: 36rpx;
    font-weight: 500;
    color: #ffffff;
    pointer-events: none;
  }

  .nav-right {
    width: 60rpx;
    height: 60rpx;
  }
}

/* 总计卡片 */
.total-card {
  margin: 20rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  /* 标题区域 */
  .total-title {
    display: flex;
    align-items: center;
    padding: 24rpx;
    border-bottom: 0;

    .title-indicator {
      width: 6rpx;
      height: 32rpx;
      border-radius: 3rpx;
      margin-right: 12rpx;
    }

    text {
      font-size: 32rpx;
      font-weight: normal;
      color: #333333;
    }
  }

  /* 内容区域 */
  .total-content {
    padding: 0 24rpx 24rpx;
    position: relative;

    /* 信用卡插图 */
    .card-illustration {
      position: absolute;
      right: 24rpx;
      top: 60rpx;
      width: 240rpx;
      height: 140rpx;
    }

    /* 总负债标签 */
    .debt-label {
      font-size: 28rpx;
      color: #666666;
      margin-bottom: 10rpx;
    }

    /* 总负债金额 */
    .debt-amount {
      font-size: 72rpx;
      font-weight: normal;
      color: #333333;
      line-height: 1.2;
      margin-bottom: 30rpx;
    }

    /* 统计数据行 */
    .stats-row {
      display: flex;
      justify-content: space-between;
      padding-top: 20rpx;
      border-top: 1rpx solid #f0f0f0;
      width: 80%; /* 控制统计行的宽度，留出右侧图片空间 */

      .stats-item {
        display: flex;
        align-items: center;
        font-size: 28rpx;
        color: #666666;

        .stats-highlight {
          color: #333333;
          font-weight: 500;
        }

        .stats-value {
          margin-left: 10rpx;
          font-size: 28rpx;
          font-weight: 500;
          color: #333333;
        }
      }
    }
  }
}

/* 还款列表标题 */
.repayment-list-title {
  margin: 30rpx 30rpx 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

/* 还款列表 */
.repayment-list {
  .repayment-item {
    position: relative;
    margin-bottom: 20rpx;

    /* 还款按钮包装器 */
    .repay-button-wrapper {
      position: absolute;
      right: 30rpx;
      bottom: 30rpx;
      z-index: 2;

      /* 还款按钮 */
      .repay-button {
        width: 120rpx;
        height: 60rpx;
        border-radius: 30rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #ffffff;
        font-size: 28rpx;
        font-weight: normal;
        box-shadow: 0 6rpx 10rpx rgba(0, 0, 0, 0.1);
      }
    }
  }
}

/* 底部空白 */
.bottom-space {
  height: 40rpx;
}
