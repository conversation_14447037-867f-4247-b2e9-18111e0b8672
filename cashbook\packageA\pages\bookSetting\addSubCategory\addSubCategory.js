// packageA/pages/bookSetting/addSubCategory/addSubCategory.js
const app = getApp();
const util = require('../../../../utils/index.js');
// getCategoryList获取以及分类列表
// addCategory添加分类
// editCategory编辑分类

import { getCategoryList,addCategory,editCategory} from '../../../../api/category/index.js';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    parentCategory: '', // 父分类名称
    parentCategoryId: '', // 父分类ID
    parentCategoryIcon: '', // 父分类图标
    categoryName: '', // 子分类名称
    useTextAsIcon: false, // 是否使用文字作为图标
    selectedIcon: '', // 选中的图标
    selectedIconName: '', // 选中的图标名称
    bookId: '', // 账本ID
    categoryType: '', // 分类类型
    icons: [], // 图标列表
    categoryList: [], // 分类列表
    isTextIcon: false, // 是否是文字图标
    isEmojiIcon: false, // 是否是emoji图标
    textIconContent: '', // 文字图标内容
    currentCategoryIndex: 0, // 当前选中的分类索引
    isEditMode: false, // 是否是编辑模式
    isMainCategory: false, // 是否是主分类
    isEditSubCategory: false, // 是否是编辑子分类
    subCategoryParentId: '' // 子分类的父分类ID
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log(options);
    const { accountbook_id, type, id, isEdit, isMain, isEditSub, parentId } = options;
    
    this.setData({
      bookId: accountbook_id,
      categoryType: type,
      parentCategoryId: id,
      isEditMode: isEdit == 'true',
      isMainCategory: isMain == 'true',
      isEditSubCategory: isEditSub == 'true',
      subCategoryParentId: parentId || '' // 设置子分类的父分类ID
    });
    
    // 根据参数动态设置页面标题
    if (isEditSub === 'true') {
      wx.setNavigationBarTitle({
        title: '编辑子分类'
      });
    } else if (isEdit === 'true') {
      wx.setNavigationBarTitle({
        title: '编辑分类'
      });
    } else if (isMain === 'true') {
      wx.setNavigationBarTitle({
        title: '添加分类'
      });
    } else {
      wx.setNavigationBarTitle({
        title: '添加子分类'
      });
    }
    
    // 获取一级分类信息
    this.fetchCategoryInfo(accountbook_id, type, id, parentId);
    
    // 获取图标列表
    this.fetchIconList(type);
  },

  /**
   * 获取一级分类信息
   */
  fetchCategoryInfo: function(accountbook_id, type, id, parentId) {
    // 如果是添加主分类或编辑主分类，则不需要获取父分类信息
    if (this.data.isMainCategory || (this.data.isEditMode && !this.data.isEditSubCategory)) {
      // 如果是编辑主分类，获取主分类信息
      if (this.data.isEditMode && !this.data.isEditSubCategory) {
        console.log('设置编辑主分类');
        wx.showLoading({
          title: '加载中...',
        });
        
        // 设置请求参数
        const params = {
          accountbook_id,
          type
        };
        
        getCategoryList({
          ...params,
          pid: id // 直接使用id参数获取主分类信息
        }).then(res => {
          wx.hideLoading();
          
          if (res && res.code === 1 && res.data && res.data.length > 0) {
            const category = res.data[0];
            
            // 设置分类信息
            this.setData({
              categoryName: category.name,
              selectedIcon: util.getImageUrl(category.image) || '/static/icon/set.png'
            });
            
            // 如果是文字图标或emoji图标，设置相应的状态
            if (category.image && category.image.startsWith('text:')) {
              this.setData({
                useTextAsIcon: true,
                isTextIcon: true,
                textIconContent: category.image.substring(5)
              });
            } else if (category.image && category.image.startsWith('emoji:')) {
              this.setData({
                isEmojiIcon: true,
                textIconContent: category.image.substring(6)
              });
            }
          } else {
            wx.showToast({
              title: res.msg || '获取分类信息失败',
              icon: 'none'
            });
          }
        }).catch(err => {
          wx.hideLoading();
          console.error('获取分类信息失败:', err);
          
          wx.showToast({
            title: '网络请求失败',
            icon: 'none'
          });
        });
      }
      
      return;
    }
    
    wx.showLoading({
      title: '加载中...',
    });
    
    // 设置请求参数
    const params = {
      accountbook_id,
      type
    };
    
    // 如果是编辑子分类，需要获取子分类详情和其父分类信息
    if (this.data.isEditSubCategory) {
      // 先获取子分类详情
      getCategoryList({
        ...params,
        pid: id // 获取指定ID的子分类
      }).then(res => {
        if (res && res.code === 1 && res.data) {
          const subCategory = res.data[0].child[0];
          console.log(subCategory,'subCategory---------------------');
          
          // 设置子分类信息
          this.setData({
            categoryName: subCategory.name,
            selectedIcon: util.getImageUrl(subCategory.image) || '/static/icon/set.png'
          });
          
          // 如果是文字图标或emoji图标，设置相应的状态
          if (subCategory.image && subCategory.image.startsWith('text:')) {
            this.setData({
              useTextAsIcon: true,
              isTextIcon: true,
              textIconContent: subCategory.image.substring(5)
            });
          } else if (subCategory.image && subCategory.image.startsWith('emoji:')) {
            this.setData({
              isEmojiIcon: true,
              textIconContent: subCategory.image.substring(6)
            });
          }
          
          // 获取父分类信息
          if (parentId) {
            getCategoryList({
              ...params,
              pid: parentId // 使用传入的parentId获取父分类信息
            }).then(parentRes => {
              wx.hideLoading();
              
              if (parentRes && parentRes.code === 1 && parentRes.data && parentRes.data.length > 0) {
                const parentCategory = parentRes.data[0];
                
                // 设置父分类信息
                this.setData({
                  parentCategory: parentCategory.name,
                  parentCategoryIcon: util.getImageUrl(parentCategory.image) || '/static/icon/set.png'
                });
              }
            }).catch(err => {
              wx.hideLoading();
              console.error('获取父分类信息失败:', err);
            });
          } else {
            wx.hideLoading();
          }
        } else {
          wx.hideLoading();
          wx.showToast({
            title: res.msg || '获取子分类信息失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        wx.hideLoading();
        console.error('获取子分类信息失败:', err);
        
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
      });
    } 
    // 如果是添加子分类，获取父分类信息
    else {
      getCategoryList({
        ...params,
        pid: id
      }).then(res => {
        wx.hideLoading();
        
        if (res && res.code === 1 && res.data && res.data.length > 0) {
          const parentCategory = res.data[0];
          
          // 设置父分类信息
          this.setData({
            parentCategory: parentCategory.name,
            parentCategoryIcon: util.getImageUrl(parentCategory.image) || '/static/icon/set.png'
          });
        } else {
          wx.showToast({
            title: res.msg || '获取父分类信息失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        wx.hideLoading();
        console.error('获取父分类信息失败:', err);
        
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
      });
    }
  },

  /**
   * 获取图标列表
   */
  fetchIconList: function(type) {
    wx.showLoading({
      title: '加载图标...',
    });
    
    getCategoryList({
      type
    }).then(res => {
      wx.hideLoading();
      
      if (res && res.code === 1 && res.data) {
        console.log('分类列表数据:', res.data);
        
        // 处理分类列表数据，添加图标类型信息
        const processedData = res.data.map(category => {
          // 处理一级分类图标
          this.processIconType(category);
          
          // 处理子分类图标
          if (category.child && Array.isArray(category.child)) {
            category.child = category.child.map(subItem => {
              return this.processIconType(subItem);
            });
          }
          
          return category;
        });
        
        // 保存处理后的分类列表数据
        this.setData({
          categoryList: processedData,
          currentCategoryIndex: processedData.length > 0 ? 0 : null
        });
        
        // 提取所有图标数据，包括子分类的图标
        let iconList = [];
        
        processedData.forEach(category => {
          // 添加一级分类图标
          if (category.name && category.image) {
            iconList.push({
              url: category.image,
              name: category.name,
              pid: category.id,
              imageType: category.imageType,
              iconContent: category.iconContent
            });
          }
          
          // 添加子分类图标
          if (category.child && category.child.length > 0) {
            category.child.forEach(subCategory => {
              if (subCategory.name && subCategory.image) {
                iconList.push({
                  url: subCategory.image,
                  name: subCategory.name,
                  pid: subCategory.id,
                  parentId: category.id,
                  imageType: subCategory.imageType,
                  iconContent: subCategory.iconContent
                });
              }
            });
          }
        });
        
        // 更新图标列表
        this.setData({
          icons: iconList
        });
      } else {
        wx.showToast({
          title: res.msg || '获取图标列表失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      
      wx.showToast({
        title: '网络请求失败',
        icon: 'none'
      });
      
      console.error('获取图标列表失败:', err);
    });
  },

  /**
   * 处理图标类型
   */
  processIconType: function(item) {
    if (item.image) {
      // 检查是否是文字图标
      if (typeof item.image === 'string' && item.image.startsWith('text:')) {
        item.imageType = 'text';
        item.iconContent = item.image.substring(5); // 去掉'text:'前缀
      } 
      // 检查是否是emoji图标
      else if (typeof item.image === 'string' && item.image.startsWith('emoji:')) {
        item.imageType = 'emoji';
        item.iconContent = item.image.substring(6); // 去掉'emoji:'前缀
      }
      // 检查是否是base64格式图片
      else if (typeof item.image === 'string' && item.image.startsWith('data:image/')) {
        item.imageType = 'image';
        // base64格式图片不需要通过getImageUrl处理
      }
      // 普通图片
      else {
        item.imageType = 'image';
        item.image = util.getImageUrl(item.image);
      }
    }
    return item;
  },

  /**
   * 切换分类
   */
  switchCategory: function(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      currentCategoryIndex: index
    });
  },

  /**
   * 选择子分类图标
   */
  selectSubCategoryIcon: function(e) {
    const index = e.currentTarget.dataset.index;
    const currentCategory = this.data.categoryList[this.data.currentCategoryIndex];
    const selectedSubCategory = currentCategory.child[index];
    
    let iconUrl = selectedSubCategory.image;
    
    // 如果是文字或emoji图标，需要保留原始格式
    if (selectedSubCategory.imageType === 'text') {
      iconUrl = 'text:' + selectedSubCategory.iconContent;
    } else if (selectedSubCategory.imageType === 'emoji') {
      iconUrl = 'emoji:' + selectedSubCategory.iconContent;
    }
    
    this.setData({
      selectedIcon: iconUrl,
      selectedIconName: selectedSubCategory.name,
      categoryName: selectedSubCategory.name, // 自动填充分类名称
      useTextAsIcon: false, // 关闭文字作为图标
      isTextIcon: selectedSubCategory.imageType === 'text',
      isEmojiIcon: selectedSubCategory.imageType === 'emoji'
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 如果已经启用了文字图标，并且有分类名称，则更新图标
    if (this.data.useTextAsIcon && this.data.categoryName) {
      this.updateTextIcon(this.data.categoryName);
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 返回上一页
   */
  goBack: function() {
    wx.navigateBack();
  },

  /**
   * 处理输入字段变化
   */
  onInput: function(e) {
    const { value, field, reachedLimit } = e.detail;
    
    if (field === 'categoryName') {
      this.setData({
        categoryName: value
      });
      
      // 如果达到了输入限制，显示提示
      if (reachedLimit) {
        wx.showToast({
          title: '已达到输入上限',
          icon: 'none',
          duration: 1000
        });
      }
      
      // 如果启用了文字作为图标，更新图标
      if (this.data.useTextAsIcon) {
        this.updateTextIcon(value);
      }
    }
  },
  
  /**
   * 限制输入长度为4个汉字或10个英文
   */
  limitInputLength: function(text) {
    if (!text) return '';
    
    let charCount = 0;
    let result = '';
    
    for (let i = 0; i < text.length; i++) {
      const char = text[i];
      // 检查是否是汉字（Unicode 范围大致是 \u4e00-\u9fa5）
      const isChineseChar = /[\u4e00-\u9fa5]/.test(char);
      
      // 汉字计数为2.5，其他字符计数为1
      // 这样4个汉字等于10个英文字符
      charCount += isChineseChar ? 2.5 : 1;
      
      // 如果超过限制（4个汉字或10个英文字符），则停止添加
      if (charCount > 10) {
        break;
      }
      
      result += char;
    }
    
    return result;
  },
  
  /**
   * 更新文字图标
   */
  updateTextIcon: function(text) {
    if (!text || text.length === 0) {
      this.setData({
        selectedIcon: '',
        textIconContent: '',
        isTextIcon: false,
        isEmojiIcon: false
      });
      return;
    }
    
    // 获取第一个字符
    const firstChar = text.charAt(0);
    
    // 检查是否是emoji
    const isEmoji = this.isEmojiCharacter(firstChar);
    
    if (isEmoji) {
      // 如果是emoji，直接使用emoji作为图标
      this.generateEmojiIcon(firstChar);
    } else {
      // 否则使用首字作为图标
      this.generateTextIcon(firstChar);
    }
  },
  
  /**
   * 检查字符是否为emoji
   */
  isEmojiCharacter: function(char) {
    // 简单的emoji检测
    return /\p{Emoji}/u.test(char);
  },
  
  /**
   * 生成emoji图标
   */
  generateEmojiIcon: function(emoji) {
    // 生成一个临时的图标URL，用于显示emoji
    // 使用特殊标记，确保在WXML中能正确识别
    const iconValue = 'emoji:' + emoji;
    console.log('生成emoji图标:', iconValue);
    
    this.setData({
      selectedIcon: iconValue,
      textIconContent: emoji,
      isEmojiIcon: true,
      isTextIcon: false
    });
  },
  
  /**
   * 生成文字图标
   */
  generateTextIcon: function(char) {
    // 生成一个临时的图标URL，用于显示文字
    // 使用特殊标记，确保在WXML中能正确识别
    const iconValue = 'text:' + char;
    console.log('生成文字图标:', iconValue);
    
    this.setData({
      selectedIcon: iconValue,
      textIconContent: char,
      isTextIcon: true,
      isEmojiIcon: false
    });
  },

  /**
   * 处理输入框获取焦点
   */
  onInputFocus: function(e) {
    // 可以在这里添加获取焦点时的逻辑
  },
  
  /**
   * 处理输入框失去焦点
   */
  onInputBlur: function(e) {
    // 可以在这里添加失去焦点时的逻辑
  },

  /**
   * 处理名称输入
   */
  onNameInput: function(e) {
    this.setData({
      categoryName: e.detail.value
    });
  },

  /**
   * 处理开关变化
   */
  onSwitchChange: function(e) {
    const useTextAsIcon = e.detail.value;
    console.log('切换文字作为图标:', useTextAsIcon);
    
    this.setData({
      useTextAsIcon: useTextAsIcon
    });
    
    // 如果开启文字作为图标，且已有分类名称，则生成文字图标
    if (useTextAsIcon && this.data.categoryName) {
      console.log('开启文字图标，当前分类名称:', this.data.categoryName);
      this.updateTextIcon(this.data.categoryName);
    } else if (!useTextAsIcon) {
      // 如果关闭文字作为图标，清除文字图标标记
      console.log('关闭文字图标，清除图标标记');
      this.setData({
        isTextIcon: false,
        isEmojiIcon: false,
        textIconContent: '',
        selectedIcon: '' // 清空选中的图标
      });
    }
  },

  /**
   * 选择图标
   */
  selectIcon: function(e) {
    const index = e.currentTarget.dataset.index;
    const selectedIcon = this.data.icons[index];
    
    let iconUrl = selectedIcon.url;
    
    // 如果是文字或emoji图标，需要保留原始格式
    if (selectedIcon.imageType === 'text') {
      iconUrl = 'text:' + selectedIcon.iconContent;
    } else if (selectedIcon.imageType === 'emoji') {
      iconUrl = 'emoji:' + selectedIcon.iconContent;
    }
    
    this.setData({
      selectedIcon: iconUrl,
      selectedIconName: selectedIcon.name,
      categoryName: selectedIcon.name, // 自动填充分类名称
      useTextAsIcon: false, // 关闭文字作为图标
      isTextIcon: selectedIcon.imageType === 'text',
      isEmojiIcon: selectedIcon.imageType === 'emoji'
    });
  },

  /**
   * 跳转到自定义页面
   */
  onCustomize: function() {
    wx.navigateTo({
      url: '/packageA/pages/bookSetting/customCategory/customCategory'
    });
  },

  /**
   * 保存分类
   */
  saveCategory: function() {
    const { bookId, parentCategoryId, categoryName, selectedIcon, useTextAsIcon, categoryType, isTextIcon, isEmojiIcon, textIconContent, isMainCategory, isEditMode, isEditSubCategory, subCategoryParentId } = this.data;
    
    // 验证输入
    if (!categoryName || categoryName.trim() === '') {
      wx.showToast({
        title: '请输入分类名称',
        icon: 'none'
      });
      return;
    }

    // 如果不使用文字作为图标，则需要选择一个图标
    if (!useTextAsIcon && !selectedIcon) {
      wx.showToast({
        title: '请选择图标',
        icon: 'none'
      });
      return;
    }

    // 显示加载中
    wx.showLoading({
      title: '保存中...',
    });

    // 构建请求参数
    const params = {
      name: categoryName,
      type: categoryType
    };
    
    // 添加账本ID
    if (bookId) {
      params.accountbook_id = bookId;
    }
    
    // 根据是否使用文字作为图标，设置不同的参数
    if (useTextAsIcon) {
      params.letter_image = 1;
      
      // 如果是emoji，可能需要特殊处理
      if (isEmojiIcon && textIconContent) {
        params.emoji = textIconContent;
      }
    } else {
      params.letter_image = 0;
      params.image = selectedIcon;
    }
    
    // 根据不同的模式设置不同的参数
    if (isEditMode || isEditSubCategory) {
      // 编辑模式：设置category_id
      params.category_id = parentCategoryId;
      
      // 如果是编辑子分类，需要设置pid
      if (isEditSubCategory && subCategoryParentId) {
        params.pid = subCategoryParentId; // 使用传入的父分类ID
      }
      
      console.log('编辑分类参数:', params);
      
      // 调用编辑分类API
      editCategory(params).then(res => {
        wx.hideLoading();
        
        if (res && res.code === 1) {
          wx.showToast({
            title: '编辑成功',
            icon: 'success'
          });
          
          // 返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          wx.showToast({
            title: res.msg || '编辑失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        wx.hideLoading();
        
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
        
        console.error('编辑分类失败:', err);
      });
    } else {
      // 添加模式：设置pid
      if (isMainCategory) {
        params.pid = 0; // 添加主分类，pid为0
      } else {
        params.pid = parentCategoryId; // 添加子分类，pid为父分类ID
      }
      
      console.log('添加分类参数:', params);
      
      // 调用添加分类API
      addCategory(params).then(res => {
        wx.hideLoading();
        
        if (res && res.code === 1) {
          wx.showToast({
            title: '添加成功',
            icon: 'success'
          });
          
          // 返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          wx.showToast({
            title: res.msg || '添加失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        wx.hideLoading();
        
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
        
        console.error('添加分类失败:', err);
      });
    }
  }
})