/* packageA/pages/bookSetting/addSubCategory/addSubCategory.wxss */
.container {
  padding: 20px 30rpx 0px;
  background-color: #ffffff;
  min-height: 100vh;
  box-sizing: border-box;
  position: relative;
  padding-bottom: 120rpx;
}

/* 导航栏样式 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  position: relative;
  margin-bottom: 20rpx;
}

.back-icon {
  padding: 10rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  flex: 1;
}

.right-btn {
  display: flex;
  align-items: center;
}

.custom-btn {
  background-color: #fef9e6;
  color: #e6b322;
  border-radius: 30rpx;
  padding: 8rpx 20rpx;
  font-size: 26rpx;
}

/* 分类标题样式 */
.section-title {
  font-size: 28rpx;
  color: #666;
  margin: 20rpx 0;
}

/* 一级分类样式 */
.primary-category {
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  margin-top: 20rpx;
  position: relative;
}

.primary-category-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  position: absolute;
  top: -20rpx;
  left: 30rpx;
}

.primary-category-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f1f1f1;
  border-radius: 35px;
  padding: 15rpx 22rpx 15rpx 40rpx;
}

.primary-category-name {
  font-size: 36rpx;
  color: #333;
}

.primary-category-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
}

/* 输入区域样式 */
.input-section {
  margin-bottom: 20rpx;
}

.category-input {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  font-size: 32rpx;
}

/* 开关区域样式 */
.switch-section {
  background-color: #fff;
  border-radius: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.switch-title {
  display: flex;
  flex-direction: column;
}

.switch-subtitle {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 文字图标预览 */
.switch-preview {
  margin-right: 20rpx;
}

.text-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f1f1f1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  overflow: hidden;
}

/* 提示文本样式 */
.tips-text {
  padding: 20rpx 0;
  font-size: 24rpx;
  color: #999;
  line-height: 1.6;
  display: flex;
  flex-direction: column;
}

/* 图标选择区域 */
.category-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 120rpx;
}

.category-title {
  font-size: 32rpx;
  font-weight: bold;
  padding: 20rpx 10rpx;
  border-bottom: 1px solid #f0f0f0;
}

.icon-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx 0;
}

.icon-item {
  width: 20%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

.icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f7f7f7;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10rpx;
}

.icon-wrapper image {
  width: 50rpx;
  height: 50rpx;
}

.icon-name {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

/* 空提示样式 */
.empty-tip {
  padding: 40rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

/* 保存按钮样式 */
.save-button {
  position: fixed;
  bottom: 40rpx;
  left: 30rpx;
  right: 30rpx;
  background-color: #5fb2ff;
  color: white;
  text-align: center;
  padding: 24rpx 0;
  border-radius: 16rpx;
  font-size: 32rpx;
  z-index: 10;
}

/* 底部固定的自定义按钮 */
.custom-button-container {
  position: fixed;
  right: 30rpx;
  bottom: 120rpx;
  z-index: 100;
}

.custom-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  width: 100rpx;
  height: 100rpx;
  padding: 15rpx;
  box-sizing: border-box;
}

.custom-button-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 5rpx;
}

.custom-button-icon image {
  width: 100%;
  height: 100%;
}

.custom-button-text {
  font-size: 22rpx;
  color: #e6b322;
  font-weight: 500;
}



/* 分类标签选择 */
.category-tabs {
  margin-bottom: 20rpx;
  width: 100%;
}

.category-tabs-scroll {
  white-space: nowrap;
  width: 100%;
}

.category-tab {
  display: inline-block;
  padding: 16rpx 30rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  transition: all 0.3s;
}

.category-tab.active {
  background-color: #59b5dd;
  color: #fff;
  font-weight: 500;
}

/* 文本图标和emoji图标样式 */
.icon-wrapper .text-icon {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.icon-wrapper .emoji-icon {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
} 