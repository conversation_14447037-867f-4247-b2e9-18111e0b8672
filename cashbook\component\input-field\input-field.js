Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 输入框标签
    label: {
      type: String,
      value: '标签'
    },
    // 输入框的值
    value: {
      type: String,
      value: '',
      observer: function(newVal) {
        this.setData({
          fieldValue: newVal
        });
      }
    },
    // 占位符文本
    placeholder: {
      type: String,
      value: ''
    },
    // 字段名称
    fieldName: {
      type: String,
      value: ''
    },
    // 输入类型
    type: {
      type: String,
      value: 'text' // text, number, idcard, digit
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    },
    // 输入最大长度
    maxlength: {
      type: Number,
      value: -1 // -1表示不限制长度
    },
    // 右侧图标
    rightIcon: {
      type: String,
      value: '',
      observer: function(newVal) {
        this._processIconType(newVal);
      }
    },
    // 高亮颜色
    highlightColor: {
      type: String,
      value: '#a2b486'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    fieldValue: '', // 内部使用的值
    isFocused: false, // 是否聚焦
    rightIconType: '', // 图标类型：image, text, emoji
    rightIconContent: '' // 图标内容（文字或emoji）
  },

  // 组件在页面中挂载时执行
  attached: function() {
    this.setData({
      fieldValue: this.properties.value
    });
    
    // 处理初始图标
    if (this.properties.rightIcon) {
      this._processIconType(this.properties.rightIcon);
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 处理图标类型
    _processIconType: function(icon) {
      if (!icon) {
        this.setData({
          rightIconType: '',
          rightIconContent: ''
        });
        return;
      }
      
      // 检查是否是文字图标
      if (icon.slice(0, 5) === 'text:') {
        this.setData({
          rightIconType: 'text',
          rightIconContent: icon.slice(5)
        });
      }
      // 检查是否是emoji图标
      else if (icon.slice(0, 6) === 'emoji:') {
        this.setData({
          rightIconType: 'emoji',
          rightIconContent: icon.slice(6)
        });
      }
      // 普通图片图标
      else {
        this.setData({
          rightIconType: 'image',
          rightIconContent: ''
        });
      }
    },
    
    // 处理输入事件
    onInput(e) {
      const value = e.detail.value;
      
      // 如果设置了最大长度为10，并且placeholder包含"限4个汉字或10个英文"，则应用特殊限制
      if (this.properties.maxlength === 10 && this.properties.placeholder.includes('限4个汉字或10个英文')) {
        // 计算当前输入的汉字和英文字符数量
        let charCount = 0;
        let limitedValue = '';
        
        for (let i = 0; i < value.length; i++) {
          const char = value[i];
          // 检查是否是汉字（Unicode 范围大致是 \u4e00-\u9fa5）
          const isChineseChar = /[\u4e00-\u9fa5]/.test(char);
          
          // 汉字计数为2.5，其他字符计数为1
          charCount += isChineseChar ? 2.5 : 1;
          
          // 如果超过限制（4个汉字或10个英文字符），则停止添加
          if (charCount > 10) {
            break;
          }
          
          limitedValue += char;
        }
        
        // 如果限制后的值与原值不同，说明超出了限制
        if (limitedValue !== value) {
          // 可以在这里添加提示，但由于组件内不方便直接调用wx.showToast，
          // 所以我们在触发事件时添加一个标记，让父组件知道已经达到限制
          this.setData({
            fieldValue: limitedValue
          });
          
          // 向父组件传递输入值变化，并标记已达到限制
          this.triggerEvent('input', {
            value: limitedValue,
            field: this.properties.fieldName,
            reachedLimit: true
          });
          
          return;
        }
        
        this.setData({
          fieldValue: limitedValue
        });
        
        // 向父组件传递输入值变化
        this.triggerEvent('input', {
          value: limitedValue,
          field: this.properties.fieldName,
          reachedLimit: false
        });
      } else {
        // 常规处理
        this.setData({
          fieldValue: value
        });

        // 向父组件传递输入值变化
        this.triggerEvent('input', {
          value: value,
          field: this.properties.fieldName
        });
      }
    },

    onLoad(){
      this.setData({
        highlightColor: wx.getStorageSync('selectedColor')
      });
    },

    // 处理聚焦事件
    onFocus(e) {
      this.setData({
        isFocused: true
      });

      // 触发聚焦事件
      this.triggerEvent('focus', {
        field: this.properties.fieldName
      });
    },

    // 处理失焦事件
    onBlur(e) {
      this.setData({
        isFocused: false
      });

      // 触发失焦事件
      this.triggerEvent('blur', {
        value: this.data.fieldValue,
        field: this.properties.fieldName
      });
    }
  }
}) 