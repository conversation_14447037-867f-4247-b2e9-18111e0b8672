Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 输入框标签
    label: {
      type: String,
      value: '标签'
    },
    // 输入框的值
    value: {
      type: String,
      value: '',
      observer: function(newVal) {
        this.setData({
          fieldValue: newVal
        });
      }
    },
    // 占位符文本
    placeholder: {
      type: String,
      value: ''
    },
    // 字段名称
    fieldName: {
      type: String,
      value: ''
    },
    // 输入类型
    type: {
      type: String,
      value: 'text' // text, number, idcard, digit
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    },
    // 输入最大长度
    maxlength: {
      type: Number,
      value: -1 // -1表示不限制长度
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    fieldValue: '', // 内部使用的值
    isFocused: false, // 是否聚焦
    // 高亮颜色
    highlightColor: '#a2b486'
  },

  // 组件在页面中挂载时执行
  attached: function() {
    this.setData({
      fieldValue: this.properties.value
    });
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 处理输入事件
    onInput(e) {
      const value = e.detail.value;
      this.setData({
        fieldValue: value
      });
      // console.log(value,'value');
      

      // 向父组件传递输入值变化
      this.triggerEvent('input', {
        value: value,
        field: this.properties.fieldName
      });
    },

    onLoad(){
      this.setData({
        highlightColor: wx.getStorageSync('selectedColor')
      });
    },

    // 处理聚焦事件
    onFocus(e) {
      this.setData({
        isFocused: true
      });

      // 触发聚焦事件
      this.triggerEvent('focus', {
        field: this.properties.fieldName
      });
    },

    // 处理失焦事件
    onBlur(e) {
      this.setData({
        isFocused: false
      });

      // 触发失焦事件
      this.triggerEvent('blur', {
        value: this.data.fieldValue,
        field: this.properties.fieldName
      });
    }
  }
}) 