Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示弹窗
    show: {
      type: Boolean,
      value: false
    },
    // 弹窗标题
    title: {
      type: String,
      value: '帮助'
    },
    // 第一个部分的标题
    section1Title: {
      type: String,
      value: '借入/还债/债务削减'
    },
    // 第二个部分的标题
    section2Title: {
      type: String,
      value: '借出/收债/坏账损失'
    },
    // 次要颜色 - 用于标题线条的渐变色
    secondaryColor: {
      type: String,
      value: '#ff9f9f'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 标题线条的渐变色样式
    titleLineStyle: '',
    // 主题颜色
    themeColor: '#ff6b6b'
  },

  /**
   * 组件的生命周期
   */
  lifetimes: {
    attached: function () {
      // 从本地存储获取主题颜色
      const themeColor = wx.getStorageSync('themeColorValue') || this.data.themeColor;
      this.setData({ themeColor });

      // 组件创建时，根据主题颜色设置标题线条的渐变色样式
      this.updateTitleLineStyle();
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'secondaryColor': function (secondaryColor) {
      // 当次要颜色变化时，更新标题线条的渐变色样式
      this.updateTitleLineStyle();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    onLoad: function () {
      console.log('onLoad');
      const themeColor = wx.getStorageSync('themeColorValue') || this.data.themeColor;
      this.setData({ secondaryColor });
    },
    // 更新标题线条的渐变色样式
    updateTitleLineStyle: function () {
      const { themeColor } = this.data;
      const { secondaryColor } = this.properties;
      const titleLineStyle = `background: linear-gradient(to bottom, ${secondaryColor},#ffffff);`;
      this.setData({ titleLineStyle });
    },

    // 防止滑动穿透
    preventTouchMove: function () {
      return false;
    },

    // 关闭弹窗
    onClose: function () {
      this.setData({
        show: false
      });
      this.triggerEvent('close');
    },

    // 显示视频帮助
    showVideoHelp: function (e) {
      const type = e.currentTarget.dataset.type; // 'borrow' or 'lend'

      // 隐藏弹窗
      this.onClose();

      // 显示加载中提示
      wx.showToast({
        title: '加载视频中...',
        icon: 'loading',
        duration: 2000
      });

      console.log(`显示${type === 'borrow' ? '借入' : '借出'}视频介绍`);

      // 这里可以跳转到视频页面或者打开视频组件
      // 暂时只显示一个提示信息
      setTimeout(() => {
        wx.showToast({
          title: `${type === 'borrow' ? '借入' : '借出'}视频介绍`,
          icon: 'none',
          duration: 2000
        });
      }, 2000);

      this.triggerEvent('videoHelp', { type });
    },

    // 显示文档帮助
    showTextHelp: function (e) {
      const type = e.currentTarget.dataset.type; // 'borrow' or 'lend'

      // 隐藏弹窗
      this.onClose();

      console.log(`显示${type === 'borrow' ? '借入' : '借出'}文档介绍`);

      // 这里可以跳转到文档页面
      // 暂时只显示一个提示信息
      wx.showToast({
        title: `${type === 'borrow' ? '借入' : '借出'}文档介绍`,
        icon: 'none',
        duration: 2000
      });

      this.triggerEvent('textHelp', { type });
    }
  }
}) 