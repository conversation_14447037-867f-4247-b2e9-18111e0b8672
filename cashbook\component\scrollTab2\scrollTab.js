// component/scrollTab/scrollTab.js
const app = getApp();

Component({

  /**
   * 组件的属性列表
   */
  properties: {
        list:{
          type:Array
        },
        bgc:{
          type:String
        },
        isIcon:{
          type:Boolean,
          default:false
        },
        isSet:{
          type:Boolean,
          default:false
        },
        isback:{
          type:Boolean,
          default:false
        }
  },

  /**
   * 组件的初始数据
   */
  data: {
    scrollIntoViewId:'',
    cureentId:1,
    tabWidth:'',
    selectedColor: '#c0cead' // 默认颜色，将被app.globalData.selectedColor替换
  },

  ready(){
    // 获取全局主题颜色
    const selectedColor = app.globalData.selectedColor || this.data.selectedColor;

    this.setData({
      tabWidth: '100%', // 使用100%宽度，避免固定像素值
      selectedColor: selectedColor
    });
  },



  /**
   * 组件的方法列表
   */
  methods: {
    scrollToItem(e) {
      // 获取当前点击项的索引
      const index = e.currentTarget.id.split('-')[1]; // 从id属性中提取索引
      const id = e.currentTarget.dataset.id;
      const targetId = `item-${index}`;

      // 更新状态
      this.setData({
        scrollIntoViewId: targetId,
        cureentId: id
      });

      // 触发自定义事件，通知父组件
      this.triggerEvent('getId', id);
    },
    toback(){
       wx.navigateBack();
    },
    onSettingTap() {
      // 触发设置按钮点击事件
      this.triggerEvent('settingTap');
      console.log('设置按钮被点击');
      // 这里可以添加设置按钮的点击逻辑
      wx.showToast({
        title: '设置按钮被点击',
        icon: 'none',
        duration: 1500
      });
    }
  }
})