/* components/input-field/input-field.wxss */

.input-container {
  position: relative;
  margin-bottom: 30rpx;
}

.book-input.focus {
  border-bottom-color: var(--highlight-color, #a2b486);
}

.book-input {
  width: 100%;
  height: 90rpx;
  font-size: 32rpx;
  color: #181818;
  background-color: #f0f0f0;
  border-radius: 45rpx;
  padding: 0 40rpx;
  box-sizing: border-box;
  font-weight: 300;
}

.book-input.has-icon {
  padding-right: 90rpx;
}

.input-label {
  position: absolute;
  left: 20px;
  top: 25rpx;
  color: #666666;
  font-size: 32rpx;
  transition: all 0.2s;
  pointer-events: none;
  z-index: 0;
  transform-origin: left top;
}

.input-label.has-value,
.input-label.focus {
  transform: translateY(-24px) scale(0.8);
  color: var(--highlight-color, #a2b486);
}

.input-placeholder {
  position: absolute;
  left: 20px;
  top: 13px;
  color: #ccc;
  font-size: 32rpx;
  opacity: 0;
  transition: opacity 0.2s;
  pointer-events: none;
  z-index: 0;
}

.input-placeholder.show {
  opacity: 1;
}

.right-icon {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  overflow: hidden;
}

.right-icon image {
  width: 100%;
  height: 100%;
  display: block;
}

/* 文字图标样式 */
.text-icon-container {
  background-color: #f1f1f1;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.text-icon {
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Emoji图标样式 */
.emoji-icon-container {
  background-color: #f1f1f1;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.emoji-icon {
  font-size: 36rpx;
  text-align: center;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
} 