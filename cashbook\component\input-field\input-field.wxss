/* components/input-field/input-field.wxss */

.input-container {
  position: relative;
  margin-bottom: 30rpx;
}

.book-input.focus {
  border-bottom-color: var(--highlight-color, #a2b486);
}

.book-input {
  width: 100%;
  height: 90rpx;
  font-size: 32rpx;
  color: #181818;
  background-color: #f0f0f0;
  border-radius: 45rpx;
  padding: 0 40rpx;
  box-sizing: border-box;
  font-weight: 300;
}

.input-label {
  position: absolute;
  left: 20px;
  top: 25rpx;
  color: #666666;
  font-size: 32rpx;
  transition: all 0.2s;
  pointer-events: none;
  z-index: 0;
  transform-origin: left top;
}

.input-label.has-value,
.input-label.focus {
  transform: translateY(-24px) scale(0.8);
  color: var(--highlight-color, #a2b486);
}

.input-placeholder {
  position: absolute;
  left: 20px;
  top: 13px;
  color: #ccc;
  font-size: 32rpx;
  opacity: 0;
  transition: opacity 0.2s;
  pointer-events: none;
  z-index: 0;
}

.input-placeholder.show {
  opacity: 1;
} 