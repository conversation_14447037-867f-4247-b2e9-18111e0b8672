/* packageA/pages/bookSetting/billTransfer/billTransfer.wxss */

/* 整体容器 */
.transfer-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f8f8;
  position: relative;
}

/* 导航栏样式 */
.nav-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  position: relative;
}

.back-icon {
  position: absolute;
  left: 30rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-title {
  flex: 1;
  text-align: center;
  font-size: 34rpx;
  font-weight: bold;
}

/* 内容区域样式 */
.content-area {
  padding: 30rpx;
  flex: 1;
}

/* 标题样式 */
.section-title {
  font-size: 28rpx;
  color: #666666;
  margin: 20rpx 0 10rpx;
}

/* 账本卡片样式 - 新样式 */
.book-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 30rpx;
  background-color: #f0f0f0;
  border-radius: 35px;
  margin-bottom: 20rpx;
}

.book-info {
  flex: 1;
}

.book-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.target-book {
  color: #999999;
}

.book-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  background-color: #65b9e8;
  display: flex;
  align-items: center;
  justify-content: center;
}

.book-avatar image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.select-icon {
  display: flex;
  padding: 10rpx;
}

/* 提示文本 */
.hint-text {
  font-size: 26rpx;
  color: #999999;
  line-height: 1.5;
  margin-top: 10rpx;
}

/* 确认按钮 */
.confirm-button {
  background: linear-gradient(to right, #56b3dd, #59c1e8);
  color: #ffffff;
  font-size: 32rpx;
  text-align: center;
  padding: 25rpx;
  margin: 40rpx;
  border-radius: 50rpx;
  box-shadow: 0 6rpx 10rpx rgba(89, 181, 221, 0.2);
  position: absolute;
  bottom: 20rpx;
  left: 0;
  right: 0;
}

/* 旧的账本选择器样式 - 保留兼容性 */
.book-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.input-container {
  position: relative;
  width: 100%;
}

.input-label {
  position: absolute;
  left: 40rpx;
  top: 28rpx; /* 输入框高度的中心位置 */
  font-size: 30rpx;
  color: #999;
  transition: all 0.3s ease;
  pointer-events: none; /* 让标签不阻挡输入框的点击 */
  transform-origin: left top;
  z-index: 1;
}

.input-placeholder {
  position: absolute;
  left: 40rpx;
  top: 28rpx;
  font-size: 30rpx;
  color: #999;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.input-placeholder.show {
  opacity: 1;
}

.input-label.focus, 
.input-label.has-value {
  top: -16rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.book-input {
  width: 100%;
  height: 90rpx;
  font-size: 32rpx;
  color: #333;
  background-color: #f5f5f5;
  border-radius: 45rpx;
  padding: 0 40rpx;
  box-sizing: border-box;
  font-weight: 300;
}