.content{
  position: relative;
  background-color: #fbfbfb;
  width: 100%;
  overflow-x: hidden; /* 隐藏横向滚动条 */
  box-sizing: border-box; /* 确保padding不会增加元素宽度 */
}
.list {
  white-space: nowrap;
  box-sizing: border-box; /* 确保padding不会增加元素宽度 */
  overflow-x: hidden; /* 隐藏横向滚动条 */
  margin: 0 40px; /* 左右边距各为40px */
}
.back {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 99;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back image{
   width: 25px;
   height: 25px;
   object-fit: cover;
    transform: rotate(90deg);
}
.item {
  /* 修改为 inline-flex 布局，支持弹性布局属性 */
  display: inline-flex;
  /* 水平居中 */
  justify-content: center;
  /* 垂直居中 */
  align-items: center;
  margin: 0 10px;
  border-radius: 15px;
  padding: 4px 10px;
  text-align: center;
}

/* 确保最后一个元素有足够的右侧边距 */
.item:last-child {
  margin-right: 30px;
}
.itemActive{
  /* background-color: #c0cead; */
  /* color: #fff; */
}
.item image{
  width: 20px;
  height: 20px;
  object-fit: cover;
  padding-right: 5px;

}
.seting{
  position: absolute;
  right: 0px; /* 增加右侧距离，避开微信小程序自带按钮 */
  top: 50%;
  transform: translateY(-50%);
  z-index: 99;
  width: 40px; /* 设置固定宽度 */
  height: 40px; /* 设置固定高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8); /* 添加半透明背景 */
  border-radius: 20px; /* 圆角 */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* 添加阴影 */
}
.seting image{
  width: 20px;
  height: 20px;
}
.mr0{
  margin:0 45px 0 0!important; /* 增加右边距，避开设置按钮 */
  width: calc(100% - 45px) !important; /* 减去右侧设置按钮和微信自带按钮的宽度 */
  box-sizing: border-box !important;
  overflow-x: hidden !important;
}