Component({
  properties: {
    // 可以传入初始年份和月份
    initYear: {
      type: Number,
      value: new Date().getFullYear()
    },
    initMonth: {
      type: Number,
      value: new Date().getMonth() + 1
    },
    isShow:{
      type:Boolean,
      default:false
    }
  },
  data: {
    year: new Date().getFullYear(),
    month: new Date().getMonth() + 1,
    weekdays: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    calendarData: [],
    touchStartX: 0,
    touchEndX: 0,
    animationData: {}
  },

  lifetimes: {
    attached() {
      this.setData({
        year: this.properties.initYear,
        month: this.properties.initMonth
      });
      this.generateCalendar();
    }
  },
  methods: {
    closeDialog(){
     let aa= this.properties.isShow;
     this.setData({
       isShow:!aa
     })
    },
    // 生成日历数据
    generateCalendar() {
      const { year, month } = this.data;
      const firstDay = new Date(year, month - 1, 1).getDay();
      const lastDay = new Date(year, month, 0).getDate();
      const prevMonthLastDay = new Date(year, month - 1, 0).getDate();
      let calendarData = [];

      // 填充上个月的剩余天数
      for (let i = firstDay === 0 ? 6 : firstDay - 1; i > 0; i--) {
        calendarData.push({
          day: prevMonthLastDay - i + 1,
          isCurrentMonth: false
        });
      }

      // 填充本月的天数
      for (let i = 1; i <= lastDay; i++) {
        calendarData.push({
          day: i,
          isCurrentMonth: true
        });
      }

      // 填充下个月的开始天数
      const remainingCells = 42 - calendarData.length;
      for (let i = 1; i <= remainingCells; i++) {
        calendarData.push({
          day: i,
          isCurrentMonth: false
        });
      }

      this.setData({
        calendarData
      });
    },
    // 处理触摸开始事件
    handleTouchStart(e) {
      this.setData({
        touchStartX: e.touches[0].pageX
      });
    },
    // 处理触摸移动事件
    handleTouchMove(e) {
      this.setData({
        touchEndX: e.touches[0].pageX
      });
    },
    // 处理触摸结束事件
    handleTouchEnd() {
      const { touchStartX, touchEndX } = this.data;
      const threshold = 50; // 滑动阈值
      if (touchStartX - touchEndX > threshold) {
        // 向左滑动，切换到下一个月
        this.animateAndChangeMonth('next');
      } else if (touchEndX - touchStartX > threshold) {
        // 向右滑动，切换到上一个月
        this.animateAndChangeMonth('prev');
      }
    },
    // 执行动画并切换月份
    animateAndChangeMonth(direction) {
      const animation = wx.createAnimation({
        duration: 300,
        timingFunction: 'ease-out'
      });

      if (direction === 'next') {
        animation.translateX('-100%').step();
      } else {
        animation.translateX('100%').step();
      }

      this.setData({
        animationData: animation.export()
      });

      setTimeout(() => {
        if (direction === 'next') {
          this.nextMonth();
        } else {
          this.prevMonth();
        }

        const resetAnimation = wx.createAnimation({
          duration: 0,
          timingFunction: 'linear'
        });
        resetAnimation.translateX(0).step();
        this.setData({
          animationData: resetAnimation.export()
        });
      }, 300);
    },
    // 切换到上一个月
    prevMonth() {
      let { year, month } = this.data;
      month--;
      if (month < 1) {
        month = 12;
        year--;
      }
      this.setData({
        year,
        month
      });
      this.generateCalendar();
    },
    // 切换到下一个月
    nextMonth() {
      let { year, month } = this.data;
      month++;
      if (month > 12) {
        month = 1;
        year++;
      }
      this.setData({
        year,
        month
      });
      this.generateCalendar();
    }
  }
});