/* 资产排序页面样式 */
.asset-sort-container {
  background-color: #f8f8f8;
  min-height: 100vh;
  display: flex;
  flex-direction: column;

  // 提示文本
  .sort-tip {
    padding: 30rpx;
    font-size: 28rpx;
    color: #999;
  }

  // 资产列表
  .asset-list {
    flex: 1;

    movable-area {
      width: 100%;
      height: 100%;

      .movable-item {
        width: 100%;
      }
    }

    // 资产项
    .asset-item {
      display: flex;
      align-items: center;
      padding: 30rpx 40rpx;
      background-color: #fff;
      margin-bottom: 2rpx;

      // 左侧图标
      .asset-icon {
        width: 80rpx;
        height: 80rpx;
        border-radius: 10rpx;
        background-color: #f5f5f5;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20rpx;
      }

      // 中间信息
      .asset-info {
        flex: 1;
        display: flex;
        flex-direction: column;

        .asset-name {
          font-size: 32rpx;
          color: #333;
          margin-bottom: 8rpx;
        }

        .asset-type {
          font-size: 24rpx;
          color: #999;

          .asset-tag {
            display: inline-block;
            padding: 0 8rpx;
            border-radius: 4rpx;
            margin-right: 8rpx;

            &.credit {
              background-color: #fff0f0;
              color: #ff4d4f;
            }

            &.debit {
              background-color: #f6ffed;
              color: #52c41a;
            }

            &.online {
              background-color: #e6f7ff;
              color: #1890ff;
            }

            &.person {
              background-color: #fff7e6;
              color: #fa8c16;
            }
          }
        }
      }

      // 右侧金额
      .asset-amount {
        text-align: right;
        font-size: 32rpx;
        color: #333;
        min-width: 180rpx;

        &.negative {
          color: #ff4d4f;
        }

        .repayment-tag {
          font-size: 22rpx;
          color: #fff;
          padding: 2rpx 8rpx;
          border-radius: 4rpx;
          margin-top: 8rpx;
          display: inline-block;
        }

        .available-tag {
          font-size: 22rpx;
          color: #666;
          background-color: #f0f0f0;
          padding: 2rpx 8rpx;
          border-radius: 4rpx;
          margin-top: 8rpx;
          display: inline-block;
        }
      }
    }
  }

  // 底部保存按钮
  .save-button {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100rpx;
    line-height: 100rpx;
    text-align: center;
    color: #fff;
    font-size: 32rpx;
    border-radius: 0;
  }
}
