<!-- components/popupDialog/popupDialog.wxml -->
<!-- 账单明细的弹窗组件 -->
<view class="popup-dialog-container {{animating ? 'visible' : ''}}" wx:if="{{popupVisible}}" catchtouchmove="stopPropagation">
  <view class="popup-dialog-mask" bindtap="onClickOverlay"></view>
  <view class="popup-dialog-content popup-dialog-{{position}} {{round ? 'popup-dialog-round' : ''}}" style="max-height:{{maxHeight}};">
    <view class="popup-dialog-header {{closeButtonPosition == 'left' ? 'reverse-header' : ''}}">
      <view class="popup-dialog-close" bindtap="onClose" wx:if="{{closeButtonPosition == 'left'}}">
        <van-icon name="cross" />
      </view>
      <view class="popup-dialog-title">{{title}}</view>
      <view class="popup-dialog-close" bindtap="onClose" wx:if="{{closeButtonPosition == 'right'}}">
        <van-icon name="cross" />
      </view>
    </view>
    <!-- 可选的标题下方操作按钮区域 -->
    <view class="popup-dialog-actions" wx:if="{{showActions}}">
      <slot name="actions"></slot>
    </view>
    <view class="popup-dialog-body">
      <!-- 分类选项列表示例 -->
      <view class="more-menu-list" wx:if="{{showCategoryList}}">
        <view class="more-menu-item" wx:for="{{categoryList}}" wx:key="id" bindtap="selectCategory" data-id="{{item.id}}">
          <view class="menu-item-icon" wx:if="{{item.icon}}">
            <image src="{{item.icon}}" mode="aspectFit"></image>
          </view>
          <view class="menu-item-content">
            <view class="menu-item-title">{{item.name}}</view>
            <view class="menu-item-desc">{{item.desc}}</view>
          </view>
          <view class="menu-item-arrow">
            <van-icon name="arrow" />
          </view>
        </view>
      </view>
      <slot></slot>
    </view>
    <!-- 底部按钮区域 -->
    <view class="popup-dialog-footer" wx:if="{{showFooter}}">
      <view class="popup-dialog-btn popup-dialog-cancel-btn" bindtap="onCancel" wx:if="{{showCancelButton}}">
        {{cancelText}}
      </view>
      <view class="popup-dialog-btn popup-dialog-confirm-btn" bindtap="onConfirm" style="background-color: {{confirmButtonColor}};">
        {{confirmText}}
      </view>
    </view>
  </view>
</view>