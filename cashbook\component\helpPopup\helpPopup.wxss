/* components/helpPopup/helpPopup.wxss */

/* 弹窗样式 */
.help-popup {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 9999;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.help-popup.show {
  visibility: visible;
  opacity: 1;
}

.popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.popup-content {
  position: absolute;
  bottom: 20rpx;
  left: 20rpx;
  right: 20rpx;
  background-color: #ffffff;
  border-radius: 35px;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  max-height: 70vh;
  overflow-y: auto;
}

.help-popup.show .popup-content {
  transform: translateY(0);
}

.popup-header {
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  border-bottom: 1px solid #f0f0f0;
  height: 24px;
}

.popup-title {
  font-size: 18px;
  font-weight: bold;
  color: #000000;
  text-align: center;
}

.popup-close {
  position: absolute;
  left: 24px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 22px;
  color: #999999;
  line-height: 1;
  padding: 5px;
  background-color: #f2f2f2;
  border-radius: 50%;
}

.popup-body {
  padding: 20px 16px;
}

.help-section {
  margin-bottom: 30px;
}

.help-section-title {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  position: relative;
}

.title-line {
  width: 3px;
  height: 20px;
  /* 背景色会通过JS动态设置 */
  margin-right: 10px;
  border-radius: 3px;
}

.help-section-title text {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.help-buttons {
  display: flex;
  gap: 20px;
  padding-left: 15px;
}

.help-button {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background-color: #f5f5f5;
  border-radius: 20px;
}

.button-circle {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  /* 颜色会通过JS动态设置 */
  margin-right: 6px;
}

.help-button text {
  font-size: 14px;
  color: #333;
} 