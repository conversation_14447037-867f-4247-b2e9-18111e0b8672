// 数字滚动组件
Component({
  properties: {
    value: {
      type: Number,
      value: 0,
      observer: function (newVal, oldVal) {
        if (newVal !== oldVal) {
          this.animateNumber(oldVal, newVal);
        }
      }
    },
    // 动画持续时间
    duration: {
      type: Number,
      value: 1000
    },
    // 小数位数
    decimals: {
      type: Number,
      value: 2
    }
  },

  data: {
    displayValue: '0.00'
  },

  methods: {
    animateNumber(start, end) {
      const duration = this.data.duration;
      const decimals = this.data.decimals;
      const startTime = Date.now();

      const animate = () => {
        const now = Date.now();
        const progress = Math.min((now - startTime) / duration, 1);

        // 使用缓动函数使动画更自然
        const easeProgress = this.easeOutQuad(progress);
        const currentValue = start + (end - start) * easeProgress;

        this.setData({
          displayValue: currentValue.toFixed(decimals)
        });

        if (progress < 1) {
          setTimeout(animate, 16); // 约60fps
        }
      };

      animate();
    },

    // 缓动函数
    easeOutQuad(t) {
      return t * (2 - t);
    }
  }
}); 