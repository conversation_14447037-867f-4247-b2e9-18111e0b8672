<!-- 分类详情页面 -->
<view class="category-detail-container">
  <!-- 顶部分类信息 -->
  <view class="category-header">
    <view class="category-icon">
      <image src="{{category.icon}}" mode="aspectFit"></image>
    </view>
    <view class="category-name">{{category.name}}</view>
    <view class="more-options">
      <van-icon name="ellipsis" size="24px" color="#cccccc" bindtap="showOptions" />
    </view>
  </view>

  <!-- 提示文本 -->
  <view class="tip-text">点击折叠面板查看子分类，点击子分类图标编辑。</view>

  <!-- 使用折叠面板展示子分类 -->
  <van-collapse value="{{activeNames}}" bind:change="onCollapseChange">
    <van-collapse-item title="子分类列表" name="1">
      <!-- 子分类列表 -->
      <view class="subcategory-grid">
        <block wx:for="{{subCategories}}" wx:key="id">
          <view class="subcategory-item" bindtap="editSubCategory" data-id="{{item.id}}">
            <view class="subcategory-icon">
              <image src="{{item.icon}}" mode="aspectFit"></image>
            </view>
            <view class="subcategory-name">{{item.name}}</view>
          </view>
        </block>
        
        <!-- 添加子分类按钮 -->
        <view class="subcategory-item add-item" bindtap="addSubCategory">
          <view class="add-icon">
            <van-icon name="plus" size="24px" color="#333" />
          </view>
          <view class="subcategory-name">添加子分类</view>
        </view>
      </view>
    </van-collapse-item>

    <!-- 常用操作折叠面板 -->
    <van-collapse-item title="常用操作" name="2">
      <view class="operation-list">
        <view class="operation-item" bindtap="editCategory">
          <van-icon name="edit" size="20px" color="#2d8cf0" />
          <text>编辑分类</text>
        </view>
        <view class="operation-item" bindtap="deleteCategory">
          <van-icon name="delete" size="20px" color="#ed4014" />
          <text>删除分类</text>
        </view>
        <view class="operation-item" bindtap="addSubCategory">
          <van-icon name="plus" size="20px" color="#19be6b" />
          <text>添加子分类</text>
        </view>
      </view>
    </van-collapse-item>
  </van-collapse>
</view> 