// component/tabSlider/tabSlider.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    tabs: {
      type: Array,
      value: []
    },
    activeIndex: {
      type: Number,
      value: 0
    },
    // 滑动进度，范围0-1
    slideProgress: {
      type: Number,
      value: 0,
      observer: function(newVal) {
        this.updateSliderPosition(newVal);
      }
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    sliderLeft: 0,
    sliderWidth: 0,
    tabWidths: [],
    tabPositions: []
  },

  lifetimes: {
    attached: function() {
      this.initTabSizes();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    initTabSizes: function() {
      const query = this.createSelectorQuery();
      query.selectAll('.tab-item').boundingClientRect();
      query.exec(res => {
        if (res && res[0] && res[0].length > 0) {
          const tabWidths = res[0].map(item => item.width);
          const tabPositions = [];
          let position = 0;

          for (let i = 0; i < tabWidths.length; i++) {
            tabPositions.push(position);
            position += tabWidths[i];
          }

          // 计算滑块的初始宽度，稍微比tab项宽一些
          const initialWidth = tabWidths[0] * 1.1;

          this.setData({
            tabWidths,
            tabPositions,
            sliderWidth: initialWidth
          });

          // 初始化滑块位置
          this.updateSliderPosition(0);
        }
      });
    },

    updateSliderPosition: function(progress) {
      const { tabWidths, tabPositions } = this.data;
      if (tabWidths.length === 0 || tabPositions.length === 0) return;

      const currentIndex = Math.floor(progress);
      const nextIndex = Math.min(currentIndex + 1, tabWidths.length - 1);
      const progressDecimal = progress - currentIndex;

      // 计算滑块宽度 - 保持固定宽度，或者稍微调整
      const currentWidth = tabWidths[currentIndex] * 1.1; // 稍微比tab项宽一些
      const nextWidth = tabWidths[nextIndex] * 1.1;
      const sliderWidth = currentWidth + (nextWidth - currentWidth) * progressDecimal;

      // 计算滑块位置 - 居中对齐
      const currentPosition = tabPositions[currentIndex] + (tabWidths[currentIndex] - currentWidth) / 2;
      const nextPosition = tabPositions[nextIndex] + (tabWidths[nextIndex] - nextWidth) / 2;
      const sliderLeft = currentPosition + (nextPosition - currentPosition) * progressDecimal;

      this.setData({
        sliderLeft,
        sliderWidth
      });
    },

    onTabClick: function(e) {
      const index = e.currentTarget.dataset.index;
      this.triggerEvent('tabclick', { index });
    }
  }
})
