<!-- components/input-field/input-field.wxml -->
<view class="input-container">
  <input
    type="{{type}}"
    class="book-input {{isFocused ? 'focus' : ''}} {{rightIcon ? 'has-icon' : ''}}"
    bindinput="onInput"
    value="{{fieldValue}}"
    bindfocus="onFocus"
    bindblur="onBlur"
    placeholder=""
    disabled="{{disabled}}"
    maxlength="{{maxlength}}"
  />
  <view
    class="input-label {{fieldValue ? 'has-value' : ''}} {{isFocused ? 'focus' : ''}}"
    style="{{(isFocused || fieldValue) ? 'color:' + highlightColor + ';' : ''}}"
  >{{label}}</view>
  <view class="input-placeholder {{isFocused && !fieldValue ? 'show' : ''}}">{{placeholder}}</view>
  
  <!-- 处理不同类型的图标 -->
  <block wx:if="{{rightIcon}}">
    <!-- 根据图标类型显示不同内容 -->
    <view class="right-icon" wx:if="{{rightIconType === 'image'}}">
      <image src="{{rightIcon}}" mode="aspectFit"></image>
    </view>
    
    <view class="right-icon text-icon-container" wx:elif="{{rightIconType === 'text'}}">
      <view class="text-icon">{{rightIconContent}}</view>
    </view>
    
    <view class="right-icon emoji-icon-container" wx:elif="{{rightIconType === 'emoji'}}">
      <view class="emoji-icon">{{rightIconContent}}</view>
    </view>
  </block>
</view> 