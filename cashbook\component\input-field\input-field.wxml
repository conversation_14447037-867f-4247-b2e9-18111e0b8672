<!-- components/input-field/input-field.wxml -->
<view class="input-container">
  <input
    type="{{type}}"
    class="book-input {{isFocused ? 'focus' : ''}}"
    bindinput="onInput"
    value="{{fieldValue}}"
    bindfocus="onFocus"
    bindblur="onBlur"
    placeholder=""
    disabled="{{disabled}}"
    maxlength="{{maxlength}}"
  />
  <view
    class="input-label {{fieldValue ? 'has-value' : ''}} {{isFocused ? 'focus' : ''}}"
    style="{{(isFocused || fieldValue) ? 'color:' + highlightColor + ';' : ''}}"
  >{{label}}</view>
  <view class="input-placeholder {{isFocused && !fieldValue ? 'show' : ''}}">{{placeholder}}</view>
</view> 