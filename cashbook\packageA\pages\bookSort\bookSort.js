import { getAccountBookList, setBookSort } from '../../../api/book/index'
const util = require('../../../utils/index.js')
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    bookList: [], // 账本列表
    currentIndex: -1, // 当前移动的账本索引
    startY: 0, // 开始触摸的纵坐标
    moveY: 0, // 移动的距离
    itemHeight: 0, // 每个账本项的高度
    bookRects: [], // 各个账本项的位置信息
    sortChanged: false, // 排序是否已改变
    selectedColor: '', // 选中的颜色
    dragging: false, // 是否正在拖动中
    lastMoveTime: 0, // 上次移动的时间戳
    scrollTop: 0 // 滚动位置
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getBookList()
    this.setData({
      selectedColor: app.globalData.selectedColor
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // 页面渲染完成后，计算item高度和位置
    setTimeout(() => {
      this.getItemsInfo()
    }, 300)
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},

  // 获取账本列表
  getBookList: function () {
    wx.showLoading({
      title: '加载中...'
    })

    getAccountBookList()
      .then((res) => {
        wx.hideLoading()

        if (res && res.code === 1) {
          const bookList = res.data || []

          // 处理数据，为每个账本添加背景色
          bookList.forEach((item, index) => {
            // 设置图片路径
            item.image = item.image ? util.getImageUrl(item.image) : ''

            // 设置默认账本标识
            item.isDefault = item.is_default === '1'
          })

          this.setData({
            bookList: bookList
          })
        } else {
          wx.showToast({
            title: res.msg,
            icon: 'none'
          })
        }
      })
      .catch((err) => {
        wx.hideLoading()
        wx.showToast({
          title: err.msg,
          icon: 'none'
        })
        console.error('获取账本列表失败:', err)
      })
  },

  // 获取每个账本项的位置和尺寸信息
  getItemsInfo: function () {
    const query = wx.createSelectorQuery()
    query.selectAll('.book-item').boundingClientRect()
    query.select('.book-scroll-view').scrollOffset()
    query.exec((res) => {
      if (res && res[0] && res[0].length) {
        const rects = res[0]
        const scrollData = res[1] || { scrollTop: 0 }

        this.setData({
          itemHeight: rects[0].height,
          bookRects: rects,
          scrollTop: scrollData.scrollTop
        })
      }
    })
  },

  // 长按触发拖拽
  onLongPress: function (e) {
    const index = e.currentTarget.dataset.index

    // 震动反馈
    wx.vibrateShort()

    // 获取当前位置信息，包括滚动位置
    this.getItemsInfo()

    // 稍微延迟以确保我们有最新的位置信息
    setTimeout(() => {
      // 记录起始触摸位置
      this.startY = e.changedTouches[0].clientY
      this.lastY = this.startY

      // 设置当前拖拽项
      this.setData({
        currentIndex: index,
        moveY: 0,
        dragging: true
      })
    }, 50)
  },

  // 触摸移动 - 使用节流来减少抖动
  touchMove: function (e) {
    if (!this.data.dragging || this.data.currentIndex < 0) return

    const now = Date.now()

    // 节流: 限制处理频率为16ms (约60fps)
    if (now - this.data.lastMoveTime < 16) {
      return
    }

    const touch = e.changedTouches[0]
    const moveY = touch.clientY - this.startY
    const direction = touch.clientY > this.lastY ? 'down' : 'up'

    // 记录最后一次处理的时间和位置
    this.lastY = touch.clientY

    this.setData({
      moveY: moveY,
      lastMoveTime: now
    })

    // 用requestAnimationFrame确保平滑的视觉更新
    if (!this.animationFrame) {
      this.animationFrame = setTimeout(() => {
        this.checkPosition(moveY, direction)
        this.animationFrame = null
      }, 0)
    }
  },

  // 检查是否需要交换位置
  checkPosition: function (moveY, direction) {
    const { currentIndex, bookList, itemHeight, bookRects, scrollTop } = this.data

    if (!bookRects.length || currentIndex < 0) return

    // 当前项的信息
    const currentRect = bookRects[currentIndex]

    // 当前项的中心点位置，考虑滚动位置
    const currentItemCenter = currentRect.top + moveY + itemHeight / 2

    // 检查是否与其他项交换位置
    let targetIndex = -1

    // 根据移动方向优化检查顺序
    const startIndex = direction === 'down' ? currentIndex + 1 : currentIndex - 1
    const endIndex = direction === 'down' ? bookRects.length : -1
    const step = direction === 'down' ? 1 : -1

    for (let i = startIndex; i !== endIndex; i += step) {
      if (i >= 0 && i < bookRects.length && i !== currentIndex) {
        const rect = bookRects[i]

        // 计算中心点在另一项的位置范围内的缓冲区
        // 增加一个缓冲区，避免在边界附近频繁切换
        const buffer = itemHeight * 0.3

        if (direction === 'down' && currentItemCenter > rect.top + buffer && currentItemCenter < rect.top + rect.height) {
          targetIndex = i
          break
        } else if (direction === 'up' && currentItemCenter < rect.top + rect.height - buffer && currentItemCenter > rect.top) {
          targetIndex = i
          break
        }
      }
    }

    // 如果位置有变化，则交换元素
    if (targetIndex !== -1 && targetIndex !== currentIndex) {
      // 阻止动画期间的用户交互
      this.setData({ dragging: false })

      // 获取当前账本和目标账本
      const currentBook = bookList[currentIndex]

      // 创建新的账本列表
      const newBookList = [...bookList]

      // 移除当前账本
      newBookList.splice(currentIndex, 1)

      // 在新位置插入当前账本
      newBookList.splice(targetIndex, 0, currentBook)

      // 暂时隐藏拖动元素，避免重排时的跳动
      this.setData({
        moveY: 0,
        currentIndex: -1
      })

      // 使用setTimeout以允许DOM更新
      setTimeout(() => {
        // 更新数据
        this.setData({
          bookList: newBookList,
          sortChanged: true
        })

        // 给DOM时间更新后再重新获取位置信息
        setTimeout(() => {
          this.getItemsInfo()

          // 重新启用拖动，并更新当前索引
          this.setData({
            currentIndex: targetIndex,
            dragging: true
          })

          // 更新起始位置
          this.startY = this.lastY
        }, 50)
      }, 0)
    }
  },

  // 触摸结束
  touchEnd: function () {
    // 清除任何挂起的动画帧
    if (this.animationFrame) {
      clearTimeout(this.animationFrame)
      this.animationFrame = null
    }

    this.setData({
      currentIndex: -1,
      moveY: 0,
      dragging: false
    })
  },

  // 保存排序
  saveSort: function () {
    if (!this.data.sortChanged) {
      wx.showToast({
        title: '排序未改变',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    // 构建排序参数，格式为 sort[账本ID]: 排序值
    // 排序值越大越靠前
    const params = {}
    const bookList = this.data.bookList

    // 倒序分配排序值，确保列表前面的账本获得更大的排序值
    bookList.forEach((book, index) => {
      // 计算排序值：总数 - 当前索引，确保第一个元素得到最大值
      const sortValue = bookList.length - index
      params[`sort[${book.id}]`] = sortValue
    })

    wx.showLoading({
      title: '保存中...'
    })

    // 调用排序API
    setBookSort(params)
      .then((res) => {
        wx.hideLoading()

        if (res && res.code === 1) {
          wx.showToast({
            title: '保存成功',
            icon: 'success',
            success: () => {
              setTimeout(() => {
                wx.navigateBack()
              }, 1500)
            }
          })
        } else {
          wx.showToast({
            title: res?.msg || '保存失败',
            icon: 'none'
          })
        }
      })
      .catch((err) => {
        wx.hideLoading()
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
        console.error('保存排序失败:', err)
      })
  },

  // 返回上一页
  goBack: function () {
    wx.navigateBack()
  }
})
