const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 20,
    navHeight: 44,
    themeColor: '#8dc63f', // 默认主题色

    // 总计信息
    totalDebt: '¥ -5,260.00',
    debtAssetCount: 8,
    dueTodayCount: 0,

    // 还款列表数据
    repaymentList: [
      {
        id: '1234',
        icon: '/assets/icons/bank/zhaoshang.png',
        name: '招商银行',
        type: '信用卡',
        cardNumber: '4321',
        balance: '-4,860.00',
        availableLimit: '5,140.00',
        billDay: '1',
        nextBillMonth: '6',
        billStatus: '下期',
        daysToNextBill: '1.54',
        repaymentDay: '10',
        nextRepaymentMonth: '6',
        daysToRepayment: '10.54',
        progressWidth: 65,
        progressColor: '#3ebcf7'
      },
      {
        id: '5678',
        icon: '/assets/icons/bank/huabei.png',
        name: '花呗',
        type: '花呗',
        cardNumber: '',
        balance: '-400.00',
        availableLimit: '600.00',
        billDay: '1',
        nextBillMonth: '6',
        billStatus: '下期',
        daysToNextBill: '1.54',
        repaymentDay: '10',
        nextRepaymentMonth: '6',
        daysToRepayment: '10.54',
        progressWidth: 40,
        progressColor: '#3ebcf7'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 设置状态栏高度
    const systemInfo = wx.getSystemInfoSync()
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight,
      navHeight: 44
    })

    // 获取主题颜色
    this.getThemeColor()

    // 加载还款列表数据
    this.loadRepaymentData()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 刷新主题颜色
    this.getThemeColor()
  },

  /**
   * 获取主题颜色
   */
  getThemeColor: function () {
    let themeColor = '#8dc63f' // 默认颜色 - 浅绿色

    if (app.globalData && app.globalData.selectedColor) {
      themeColor = app.globalData.selectedColor
    }

    this.setData({
      themeColor: themeColor
    })
  },

  /**
   * 加载还款列表数据
   */
  loadRepaymentData: function () {
    // 这里可以调用API获取实际数据
    // 目前使用模拟数据
    wx.showLoading({
      title: '加载中...'
    })

    // 模拟API请求延迟
    setTimeout(() => {
      // 使用this.data.repaymentList中的模拟数据

      // 更新每个卡片的进度条颜色为主题色
      const repaymentList = this.data.repaymentList.map((item) => {
        return {
          ...item,
          progressColor: this.data.themeColor
        }
      })

      this.setData({
        repaymentList: repaymentList
      })

      wx.hideLoading()
    }, 500)
  },

  /**
   * 返回上一页
   */
  onBackTap: function () {
    wx.navigateBack()
  },

  /**
   * 处理还款操作
   */
  handleRepay: function (e) {
    const assetId = e.detail.assetId
    const asset = this.data.repaymentList.find((item) => item.id === assetId)

    if (asset) {
      // 跳转到还款详情页面
      wx.navigateTo({
        url: `/packageA/pages/repayment/repayDetail/repayDetail?assetId=${assetId}&amount=${asset.balance.replace(/,/g, '').replace('-', '')}`
      })
    }
  },

  /**
   * 处理编辑资产操作
   */
  handleEditAsset: function (e) {
    const assetId = e.detail.assetId
    wx.navigateTo({
      url: `/packageA/pages/settings/createAccount/createAccount?id=${assetId}&edit=true`
    })
  }
})
