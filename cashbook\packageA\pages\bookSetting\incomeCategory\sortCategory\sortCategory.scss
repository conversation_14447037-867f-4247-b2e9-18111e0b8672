/* packageA/pages/bookSetting/incomeCategory/sortCategory/sortCategory.scss */

.sort-container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 120rpx;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 0 30rpx;
  z-index: 100;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  .back-icon {
    padding: 10rpx;
  }

  .page-title {
    flex: 1;
    text-align: center;
    font-size: 34rpx;
    font-weight: 500;
    color: #333;
  }
}

/* 提示文本 */
.tip-text {
  margin-top: calc(44px + var(--status-bar-height));
  padding: 30rpx;
  font-size: 28rpx;
  color: #999;
  text-align: center;
}

/* 分类列表 */
.category-list {
  padding: 0 20rpx;

  .sort-list {
    width: 100%;
    position: relative;
  }

  .category-item {
    width: 100%;
    position: relative;
    z-index: 10;
    margin-bottom: 10rpx;

    .item-content {
      transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    &.long-pressing {
      .item-content {
        transform: scale(1.01);
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
        background-color: #f8f9fa;
      }
    }

    &.dragging {
      z-index: 100;

      .item-content {
        background-color: #fff;
        transform: scale(1.05);
        box-shadow: 0 12rpx 30rpx rgba(0, 0, 0, 0.2);
        border: 2rpx solid #59b5dd;
        border-radius: 16rpx;
      }
    }
  }
}

.item-content {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

  .drag-handle {
    padding: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .category-icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: 35%;
    background-color: #f5f5f5;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 20rpx;
    overflow: hidden;

    image {
      width: 48rpx;
      height: 48rpx;
    }
  }

  .category-name {
    font-size: 30rpx;
    color: #333;
    flex: 1;
  }
}

/* 文本图标 */
.text-icon {
  font-size: 32rpx;
  color: #333;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
}

/* emoji图标 */
.emoji-icon {
  font-size: 36rpx;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 保存按钮 */
.save-button {
  position: fixed;
  bottom: 40rpx;
  left: 40rpx;
  right: 40rpx;
  height: 90rpx;
  border-radius: 45rpx;
  background-color: #59b5dd;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(89, 181, 221, 0.3);
}