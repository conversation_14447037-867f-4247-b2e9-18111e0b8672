.confirm-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.confirm-popup-mask.visible {
  opacity: 1;
}

.confirm-popup-container {
  width: 90%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transform: scale(0.95);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.confirm-popup-container.visible {
  transform: scale(1);
  opacity: 1;
}

.confirm-popup-header {
  position: relative;
  padding: 40rpx 30rpx 20rpx;
  text-align: center;
  border-bottom: none;
}

.popup-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
  display: block;
}

.popup-close {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 38rpx;
  color: #333;
  line-height: 45rpx;
}

.confirm-popup-content {
  padding: 10rpx 40rpx 40rpx;
  color: #666;
  text-align: center;
  font-size: 30rpx;
  min-height: 40rpx;
  word-wrap: break-word;
  line-height: 1.6;
}

.confirm-popup-footer {
  display: flex;
  padding: 0 40rpx 40rpx;
  justify-content: space-between;
}

.popup-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  border-radius: 45rpx;
  margin: 0 15rpx;
  font-size: 32rpx;
}

.cancel-btn {
  color: #4fc08d;
  background-color: #f5f5f5;
  border: none;
}

.confirm-btn {
  color: #ffffff;
  background-color: #4fc08d;
  border: none;
  font-weight: 500;
}
