<!-- 页面容器，添加可滚动视图 -->
<scroll-view scroll-y="true" class="page-container">
  <!-- 顶部状态栏 -->
  <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>

  <!-- 顶部导航栏 -->
  <view class="navigation-bar" style="height: {{navHeight}}px;">
    <view class="nav-left" bindtap="onBackTap">
      <van-icon name="arrow-left" />
    </view>
    <view class="nav-title">资产详情</view>
    <view class="nav-right" bindtap="showOperationMenu">
      <text>操作</text>
    </view>
  </view>

  <!-- 使用资产详情卡片组件 -->
  <asset-detail-card asset="{{asset}}" progressWidth="{{progressWidth}}" themeColor="{{themeColor}}" bind:edit="onEditAsset"> </asset-detail-card>

  <!-- 提示信息 -->
  <view class="tip-section">
    <text>信用卡、花呗等会出现跨年的账单，当月账单下月才会出账单，如2022年出现2021年12月份账单。</text>
  </view>

  <!-- 年份选择器 -->
  <view class="year-selector">
    <view class="year-display" bindtap="toggleYearPicker">
      <text>{{selectedYear}}年</text>
      <van-icon name="arrow-down" class="{{isYearPickerOpen ? 'rotated' : ''}}" />
    </view>
    <view class="setting-icon">
      <van-icon name="setting-o" />
    </view>
  </view>

  <!-- 月份账单列表 -->
  <view class="month-bill-list">
    <view class="month-bill-item" bindtap="viewMonthBill">
      <view class="month-info">
        <view class="month-name">
          {{currentBill.month}}月
          <view class="month-icon">
            <van-icon name="clock-o" size="24rpx" />
          </view>
        </view>
        <view class="month-period">周期: {{currentBill.startDate}}-{{currentBill.endDate}}</view>
      </view>
      <view class="month-amount">
        <view class="expense">支出 ¥{{currentBill.expense}}</view>
        <view class="income">收入 ¥{{currentBill.income}}</view>
      </view>
    </view>
  </view>

  <!-- 底部占位，确保内容不被底部按钮遮挡 -->
  <view class="bottom-placeholder"></view>
</scroll-view>

<!-- 底部按钮区域，固定在底部 -->
<view class="bottom-buttons">
  <view class="record-btn" bindtap="recordTransaction">记一笔</view>
  <view class="repay-btn" bindtap="repayDebt" style="background-color: {{themeColor}};">还款</view>
</view>
