<!-- 页面容器，添加可滚动视图 -->
<scroll-view scroll-y="true" class="page-container">
  <!-- 顶部状态栏 -->
  <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>

  <!-- 顶部导航栏 -->
  <view class="navigation-bar" style="height: {{navHeight}}px;">
    <view class="nav-left" bindtap="onBackTap">
      <van-icon name="arrow-left" />
    </view>
    <view class="nav-title">资产详情</view>
    <view class="nav-right" bindtap="showOperationMenu">
      <text>操作</text>
    </view>
  </view>

  <!-- 卡片信息头部 -->
  <view class="asset-header">
    <view class="asset-title">
      <view class="asset-icon">
        <image src="{{asset.icon}}" mode="aspectFit"></image>
      </view>
      <view class="asset-name">{{asset.name}} | {{asset.type}} {{asset.cardNumber}}</view>
    </view>
    <view class="edit-btn" bindtap="editAsset">编辑</view>
  </view>

  <!-- 信用卡详情 -->
  <view class="asset-details">
    <!-- 欠款和额度区域 - 改为左右布局 -->
    <view class="balance-limit-section">
      <!-- 当前欠款 -->
      <view class="balance-section">
        <view class="balance-label">当前欠款(元)</view>
        <view class="balance-amount">
          <text>{{asset.balance}}</text>
        </view>
      </view>

      <!-- 可用额度 -->
      <view class="limit-section">
        <view class="limit-label">可用额度</view>
        <view class="limit-amount">{{asset.availableLimit}}</view>
        <view class="limit-progress">
          <view class="progress-bar" style="width: {{progressWidth}}%; background-color: {{themeColor}};"></view>
        </view>
      </view>
    </view>

    <!-- 账单信息区域 -->
    <view class="bill-info-section">
      <view class="bill-info-row">
        <view class="bill-info-item">
          <view class="bill-info-label">账单日期</view>
          <view class="bill-info-value">每月{{asset.billDay}}号</view>
        </view>
        <view class="bill-info-item center-align">
          <view class="bill-info-label">出账日期</view>
          <view class="bill-info-value">
            {{asset.nextBillMonth}}月{{asset.billDay}}日
            <text class="date-status">{{asset.billStatus}}</text>
          </view>
        </view>
        <view class="bill-info-item right-align">
          <view class="bill-info-label">出账天数</view>
          <view class="bill-info-value">剩{{asset.daysToNextBill}}天</view>
        </view>
      </view>

      <!-- 分隔线 -->
      <view class="divider"></view>

      <view class="bill-info-row">
        <view class="bill-info-item">
          <view class="bill-info-label">还款日</view>
          <view class="bill-info-value">每月{{asset.repaymentDay}}号</view>
        </view>
        <view class="bill-info-item center-align">
          <view class="bill-info-label">还款日期</view>
          <view class="bill-info-value">{{asset.nextRepaymentMonth}}月{{asset.repaymentDay}}日</view>
        </view>
        <view class="bill-info-item right-align">
          <view class="bill-info-label">免息天数</view>
          <view class="bill-info-value">剩{{asset.daysToRepayment}}天</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 提示信息 -->
  <view class="tip-section">
    <text>信用卡、花呗等会出现跨年的账单，当月账单下月才会出账单，如2022年出现2021年12月份账单。</text>
  </view>

  <!-- 年份选择器 -->
  <view class="year-selector">
    <view class="year-display" bindtap="toggleYearPicker">
      <text>{{selectedYear}}年</text>
      <van-icon name="arrow-down" class="{{isYearPickerOpen ? 'rotated' : ''}}" />
    </view>
    <view class="setting-icon">
      <van-icon name="setting-o" />
    </view>
  </view>

  <!-- 月份账单列表 -->
  <view class="month-bill-list">
    <view class="month-bill-item" bindtap="viewMonthBill">
      <view class="month-info">
        <view class="month-name">
          {{currentBill.month}}月
          <view class="month-icon">
            <van-icon name="clock-o" size="24rpx" />
          </view>
        </view>
        <view class="month-period">周期: {{currentBill.startDate}}-{{currentBill.endDate}}</view>
      </view>
      <view class="month-amount">
        <view class="expense">支出 ¥{{currentBill.expense}}</view>
        <view class="income">收入 ¥{{currentBill.income}}</view>
      </view>
    </view>
  </view>

  <!-- 底部占位，确保内容不被底部按钮遮挡 -->
  <view class="bottom-placeholder"></view>
</scroll-view>

<!-- 底部按钮区域，固定在底部 -->
<view class="bottom-buttons">
  <view class="record-btn" bindtap="recordTransaction">记一笔</view>
  <view class="repay-btn" bindtap="repayDebt" style="background-color: {{themeColor}};">还款</view>
</view>
