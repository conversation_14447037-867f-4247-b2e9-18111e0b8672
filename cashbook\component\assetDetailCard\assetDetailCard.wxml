<!-- 资产详情卡片组件 -->
<view class="asset-card">
  <!-- 卡片信息头部 -->
  <view class="asset-header">
    <view class="asset-title">
      <view class="asset-icon">
        <image src="{{asset.icon}}" mode="aspectFit"></image>
      </view>
      <view class="asset-name">{{asset.name}} | {{asset.type}} {{asset.cardNumber}}</view>
    </view>
    <view class="edit-btn" bindtap="editAsset">编辑</view>
  </view>

  <!-- 信用卡详情 -->
  <view class="asset-details">
    <!-- 欠款和额度区域 -->
    <view class="balance-limit-section">
      <!-- 当前欠款 -->
      <view class="balance-section">
        <view class="balance-label">当前欠款(元)</view>
        <view class="balance-amount">
          <text>{{asset.balance}}</text>
        </view>
      </view>

      <!-- 可用额度 -->
      <view class="limit-section">
        <view class="limit-label">可用额度</view>
        <view class="limit-amount">{{asset.availableLimit}}</view>
        <view class="limit-progress">
          <view class="progress-bar" style="width: {{progressWidth}}%; background-color: {{themeColor}};"></view>
        </view>
      </view>
    </view>

    <!-- 账单信息区域 -->
    <view class="bill-info-section">
      <view class="bill-info-row">
        <view class="bill-info-item">
          <view class="bill-info-label">账单日期</view>
          <view class="bill-info-value">每月{{asset.billDay}}号</view>
        </view>
        <view class="bill-info-item center-align">
          <view class="bill-info-label">出账日期</view>
          <view class="bill-info-value">
            {{asset.nextBillMonth}}月{{asset.billDay}}日
            <text class="date-status">{{asset.billStatus}}</text>
          </view>
        </view>
        <view class="bill-info-item right-align">
          <view class="bill-info-label">出账天数</view>
          <view class="bill-info-value">剩{{asset.daysToNextBill}}天</view>
        </view>
      </view>

      <!-- 分隔线 -->
      <view class="divider"></view>

      <view class="bill-info-row">
        <view class="bill-info-item">
          <view class="bill-info-label">还款日</view>
          <view class="bill-info-value">每月{{asset.repaymentDay}}号</view>
        </view>
        <view class="bill-info-item center-align">
          <view class="bill-info-label">还款日期</view>
          <view class="bill-info-value">{{asset.nextRepaymentMonth}}月{{asset.repaymentDay}}日</view>
        </view>
        <view class="bill-info-item right-align">
          <view class="bill-info-label">免息天数</view>
          <view class="bill-info-value">剩{{asset.daysToRepayment}}天</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 还款按钮 -->
  <view wx:if="{{showRepayButton}}" class="repay-button-wrapper">
    <view class="repay-button" style="background-color: {{themeColor}};" bindtap="repay">还款</view>
  </view>

  <!-- 自定义操作区域插槽 -->
  <slot name="actions"></slot>
</view>
