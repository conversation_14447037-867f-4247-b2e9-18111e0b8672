import request from '../request'

/**
 * 获取账户列表
 * @returns {Promise} 返回账户列表数据
 */
export function getAccounts() {
  return request({
    url: 'account/account',
    method: 'GET'
  })
}

/**
 * 获取账户分组列表
 * @returns {Promise} 返回账户分组列表数据
 */
export function getAccountGroups() {
  return request({
    url: 'account/group',
    method: 'POST'
  })
}

/**
 * 获取用户账户列表
 * @param {Object} params - 请求参数
 * @param {string} [params.show_type] - 显示类型，例如：group（分组）
 * @param {string} [params.status] - 状态，例如：normal（正常）、hidden（隐藏）
 * @returns {Promise} 请求结果，返回用户账户列表数据
 */
export function getUserAccounts(params = {}) {
  return request({
    url: 'account/user_account',
    method: 'POST',
    data: params
  })
}

/**
 * 获取账户详情
 * @param {Object} params - 请求参数
 * @param {string} params.user_account_id - 账户ID
 * @returns {Promise} 请求结果，返回账户详情数据
 */
export function getUserAccountDetail(params = {}) {
  return request({
    url: 'account/user_account_detail',
    method: 'POST',
    data: params
  })
}

/**
 * 更新账户信息
 * @param {Object} params - 请求参数
 * @param {string} params.id - 账户ID（必传）
 * @param {string} [params.account_id] - 账户分类ID
 * @param {string} [params.group_name] - 自定义分组名称
 * @param {string} [params.name] - 银行名称或自定义名称
 * @param {string} [params.image] - 银行LOGO或自定义图片
 * @param {string} [params.username] - 账户名称或备注信息
 * @param {string} [params.cardnum] - 银行卡号
 * @param {string} [params.money] - 账户余额或欠款
 * @param {string} [params.debt] - 信用额度
 * @param {string} [params.bill_date] - 账单日期
 * @param {string} [params.repayment_date] - 还款日期
 * @param {string} [params.include] - 是否计入总资产，0=否，1=是
 * @param {string} [params.status] - 类型：normal=显示，hidden=隐藏
 * @returns {Promise} 请求结果
 */
export function updateAccount(params) {
  return request({
    url: 'account/update_account',
    method: 'POST',
    data: params
  })
}

/**
 * 删除账户
 * @param {Object} params - 请求参数
 * @param {string} params.user_account_id - 账户ID
 * @param {string} [params.del_bill] - 是否删除账单，0=否，1=是
 * @returns {Promise} 请求结果
 */
export function deleteAccount(params) {
  return request({
    url: 'account/del_account',
    method: 'POST',
    data: params
  })
}

/**
 * 添加新账户
 * @param {Object} params - 请求参数
 * @param {string} params.account_id - 账户分类ID
 * @param {string} params.group_name - 自定义分组名称
 * @param {string} params.name - 银行名称或自定义名称
 * @param {string} params.image - 银行LOGO或自定义图片
 * @param {string} params.username - 账户名称或备注信息
 * @param {string} params.cardnum - 银行卡号，非必传
 * @param {string} params.money - 账户余额或欠款
 * @param {string} [params.debt] - 信用额度，非必传
 * @param {string} [params.bill_date] - 账单日期，非必传
 * @param {string} [params.repayment_date] - 还款日期，非必传
 * @param {string} [params.include] - 是否计入总资产，0=否，1=是
 * @param {string} [params.status] - 类型：normal=显示
 * @param {string} [params.bill] - 是否记一笔账单，0=否，1=是
 * @returns {Promise} 请求结果
 */
export function addAccount(params) {
  return request({
    url: 'account/add_account',
    method: 'POST',
    data: params
  })
}

/**
 * 获取银行列表
 * @param {Object} params - 请求参数
 * @param {string} [params.keyword] - 搜索关键词
 * @returns {Promise} 请求结果，返回银行列表数据
 */
export function getBankList(params = {}) {
  return request({
    url: 'account/bank',
    method: 'GET',
    data: params
  })
}

/**
 * 更新账户排序
 * @param {Array} sortData - 账户排序数据数组
 * @param {string} sortData[].id - 账户ID
 * @param {number} sortData[].sort - 排序顺序
 * @returns {Promise} 请求结果
 */
export function updateAccountSort(sortData) {
  return request({
    url: 'account/update_sort',
    method: 'POST',
    data: { sort_data: sortData }
  })
}

/**
 * 根据返回数据格式说明：
 * 接口返回数据包含：
 * - netassets: 净资产
 * - capital: 资金
 * - liabilities: 负债
 * - data: 账户分组数据
 *   - title: 分组标题
 *   - num: 分组编号
 *   - netassets: 分组净资产
 *   - data: 分组内账户列表
 *     - id: 账户ID
 *     - account_name: 账户名称
 *     - account_type: 账户类型（如liabilities表示负债）
 *     - account_category: 账户分类（如fund表示资金）
 *     - name: 账户显示名称
 *     - image: 账户图片
 *     - cardnum: 卡号
 *     - username: 用户名
 *     - money: 金额
 *     - repayment_date: 还款日期
 *     - debt: 债务金额
 *     - include: 是否计入统计（1表示计入）
 */
