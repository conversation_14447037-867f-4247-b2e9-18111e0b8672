<!-- components/helpPopup/helpPopup.wxml -->
<view class="help-popup {{show ? 'show' : ''}}" catchtouchmove="preventTouchMove">
  <view class="popup-mask" bindtap="onClose"></view>
  <view class="popup-content">
    <view class="popup-header">
      <view class="popup-close" bindtap="onClose">
        <van-icon name="cross" />
      </view>
      <view class="popup-title">{{title}}</view>
    </view>
    <view class="popup-body">
      <!-- 第一个部分 -->
      <view class="help-section">
        <view class="help-section-title">
          <view class="title-line" style="{{titleLineStyle}}"></view>
          <text>{{section1Title}}</text>
        </view>
        <view class="help-buttons">
          <view class="help-button" bindtap="showVideoHelp" data-type="borrow">
            <view class="button-circle" style="background-color: {{themeColor}};"></view>
            <text>视频介绍</text>
          </view>
          <view class="help-button" bindtap="showTextHelp" data-type="borrow">
            <view class="button-circle" style="background-color: {{themeColor}};"></view>
            <text>文档介绍</text>
          </view>
        </view>
      </view>
      <!-- 第二个部分 -->
      <view class="help-section">
        <view class="help-section-title">
          <view class="title-line" style="{{titleLineStyle}}"></view>
          <text>{{section2Title}}</text>
        </view>
        <view class="help-buttons">
          <view class="help-button" bindtap="showVideoHelp" data-type="lend">
            <view class="button-circle" style="background-color: {{themeColor}};"></view>
            <text>视频介绍</text>
          </view>
          <view class="help-button" bindtap="showTextHelp" data-type="lend">
            <view class="button-circle" style="background-color: {{themeColor}};"></view>
            <text>文档介绍</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>