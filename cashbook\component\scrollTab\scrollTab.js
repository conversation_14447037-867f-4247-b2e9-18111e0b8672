const app = getApp();
Component({

  /**
   * 组件的属性列表
   */
  properties: {
        list:{
          type:Array
        },
        bgc:{
          type:String
        },
        isIcon:{
          type:Boolean,
          default:false
        },
        isSet:{
          type:Boolean,
          default:false
        },
        isback:{
          type:Boolean,
          default:false
        },
        activeId: {
          type: Number,
          value: 1,
          observer: function(newVal) {
            // console.log('activeId changed:', newVal);
            // 当activeId变化时，更新组件内部的cureentId
            this.setData({
              cureentId: newVal
            });
            // 计算对应的index并滚动到该位置
            const list = this.data.list || [];
            const index = list.findIndex(item => item.id === newVal);
            if (index !== -1) {
              const targetId = `item-${index}`;
              this.setData({
                scrollIntoViewId: targetId
              });
            }
          }
        }
  },

  /**
   * 组件的初始数据
   */
  data: {
    scrollIntoViewId:'',
    cureentId:1,
    tabWidth:''
  },
  ready(){
    // 不再需要获取窗口信息，因为我们使用100%宽度

    // 确保cureentId与activeId一致
    if (this.properties.activeId) {
      this.setData({
        cureentId: this.properties.activeId
      });

      // 计算对应的index并滚动到该位置
      const list = this.data.list || [];
      const index = list.findIndex(item => item.id === this.properties.activeId);
      if (index !== -1) {
        const targetId = `item-${index}`;
        this.setData({
          scrollIntoViewId: targetId
        });
      }
    }

    this.setData({
      tabWidth: '100%' // 使用100%宽度，避免固定像素值
    });
    // console.log(app.globalData.selectedColor,'app.globalData.selectedColor');
  },



  /**
   * 组件的方法列表
   */
  methods: {
    scrollToItem(e) {
      const index = e.currentTarget.dataset.index;
      const id= e.currentTarget.dataset.id;
      const targetId = `item-${index}`;

      // console.log('scrollToItem, id:', id, 'index:', index);

      this.setData({
        scrollIntoViewId: targetId,
        cureentId: id
      });

      // 把id返給父組件
      this.triggerEvent('getId', id);
    },
    toback(){
       wx.navigateBack();
    },
    onSettingTap() {
      // 触发设置按钮点击事件
      this.triggerEvent('settingTap');
      console.log('设置按钮被点击');
      // 这里可以添加设置按钮的点击逻辑
      wx.showToast({
        title: '设置按钮被点击',
        icon: 'none',
        duration: 1500
      });
    }
  }
})