/* component/categorySelector/categorySelector.wxss */

/* 容器样式 */
.container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: flex-end; /* 底部对齐 */
  padding-bottom: 70rpx; /* 距离底部的距离 */
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.container.show {
  opacity: 1;
  visibility: visible;
}

/* 选择器内容区域 */
.selector-content {
  width: 95%; /* 宽度95% */
  background-color: #fff;
  border-radius:90rpx; /* 四周都是圆角 */
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15); /* 四周阴影 */
  display: flex;
  flex-direction: column;
  max-height: 80vh;
  transform: translateY(100%); /* 初始位置在屏幕下方 */
  transition: transform 0.3s ease;
}

.container.show .selector-content {
  transform: translateY(0); /* 显示时移动到正常位置 */
}

/* 顶部操作栏 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

/* 左侧区域 */
.header-left {
  display: flex;
  align-items: center;
}

/* 右侧区域 */
.header-right {
  display: flex;
  align-items: center;
}

/* 关闭按钮 - 使用伪元素实现X */
.close-btn {
  width: 70rpx;
  height: 70rpx;
  background-color: #f0f0f0;
  border-radius: 50%;
  position: relative;
  margin: 5rpx;
}

/* 关闭按钮的X形状 - 使用伪元素 */
.close-btn::before,
.close-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 26rpx;
  height: 3rpx;
  background-color: #666;
}

.close-btn::before {
  transform: translate(-50%, -50%) rotate(45deg);
}

.close-btn::after {
  transform: translate(-50%, -50%) rotate(-45deg);
}

/* 设置按钮样式 */
.settings-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #4BA3E1;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15rpx;
}

.settings-btn image {
  width: 36rpx;
  height: 36rpx;
}

.mode-switch {
  padding: 12rpx 26rpx;
  border-radius: 40rpx;
  color: #fff;
  font-size: 28rpx;
  margin-right: 15rpx;
  background-color: #4BA3E1; /* 默认蓝色，但会被themeColor覆盖 */
  min-width: 60rpx;
  text-align: center;
}

.add-btn {
  padding: 18rpx 26rpx;
  border-radius: 40rpx;
  background-color: #2D3142; /* 深蓝色/黑色 */
  color: #fff;
  font-size: 28rpx;
  min-width: 60rpx;
  text-align: center;
}

/* 列表模式样式 */
.category-list {
  max-height: 55vh; /* 减小高度，适应悬浮弹窗 */
  overflow-y: auto; /* 允许垂直滚动 */
  background-color: #fff;
  width: 100%;
}

/* 列表内容容器，用于提供内边距而不影响滚动容器 */
.list-container {
  padding: 0 20rpx;
}

.list-item {
  display: flex;
  align-items: center; /* 垂直居中对齐 */
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.list-item:last-child {
  border-bottom: none;
}

.item-icon {
  width: 80rpx;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 50%; /* 改为圆形 */
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
  flex-shrink: 0; /* 防止图标被压缩 */
}

.item-icon image {
  width: 50rpx;
  height: 50rpx;
}

.item-info {
  flex: 1;
  display: flex;
  align-items: center; /* 垂直居中对齐 */
}

.item-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500; /* 稍微加粗 */
}

/* 卡片模式样式 */
.category-grid {
  max-height: 55vh; /* 减小高度，适应悬浮弹窗 */
  overflow-y: auto; /* 允许垂直滚动 */
  background-color: #fff;
  width: 100%;
}

/* 网格外层容器，用于提供内边距而不影响滚动容器 */
.grid-wrapper {
  padding: 20rpx 20rpx 30rpx;
}

.grid-container {
  display: flex;
  flex-wrap: wrap;
}

.grid-item {
  width: 20%; /* 改为5列布局 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center; /* 垂直居中 */
  margin-bottom: 30rpx;
  box-sizing: border-box;
}

.grid-item .item-icon {
  width: 110rpx;
  height: 110rpx;
  margin-right: 0;
  margin-bottom: 10rpx;
  background-color: #f5f5f5;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.grid-item .item-icon image {
  width: 50rpx;
  height: 50rpx;
}

.grid-item .item-name {
  font-size: 26rpx;
  text-align: center;
  max-width: 100%; /* 限制最大宽度 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 显示省略号 */
  white-space: nowrap; /* 不换行 */
  color: #333;
  line-height: 1.2; /* 调整行高 */
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 20rpx; /* 固定高度，为弹窗底部提供一些内边距 */
  width: 100%;
  background-color: #fff;
}
