// packageA/pages/bookSetting/incomeCategory/sortCategory/sortCategory.js
// 分类排序
import { sortCategory, getCategoryList } from '../../../../../api/category/index.js';
const util = require('../../../../../utils/index.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 0, // 状态栏高度
    navBarHeight: 44, // 导航栏高度
    categories: [], // 分类列表
    accountbook_id: '', // 账本ID
    type: 'expenses', // 分类类型，默认支出
    pid: 0, // 父类ID，0表示主分类排序
    isDragging: false, // 是否正在拖动
    startY: 0, // 拖动开始的Y坐标
    currentIndex: -1, // 当前拖动的项索引
    sortChanged: false, // 排序是否改变
    moveY: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('排序页面参数:', options);
    
    // 获取系统信息
    this.getSystemInfo();
    
    // 获取参数
    const { accountbook_id = '', type = 'expenses', pid = '0' } = options;
    
    this.setData({
      accountbook_id,
      type,
      pid: parseInt(pid)
    });
    
    // 加载分类数据
    this.loadCategories();
  },

  /**
   * 获取系统信息，设置导航栏高度
   */
  getSystemInfo: function() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      // 状态栏高度
      const statusBarHeight = systemInfo.statusBarHeight;
      // 导航栏高度，默认44px
      const navBarHeight = 44;

      this.setData({
        statusBarHeight: statusBarHeight,
        navBarHeight: navBarHeight
      });
    } catch (e) {
      console.error('获取系统信息失败', e);
    }
  },

  /**
   * 加载分类数据
   */
  loadCategories: function() {
    wx.showLoading({
      title: '加载中...',
    });
    
    // 构建请求参数
    const params = {
      accountbook_id: this.data.accountbook_id,
      type: this.data.type
    };
    
    // 如果是子分类排序，需要传递pid
    if (this.data.pid !== 0) {
      params.pid = this.data.pid;
    }
    
    console.log('加载分类数据，请求参数:', params);
    
    // 调用接口获取分类数据
    getCategoryList(params).then(res => {
      wx.hideLoading();
      
      if (res && res.code === 1 && res.data) {
        console.log('获取分类数据成功:', res.data);
        
        // 处理分类数据
        let categories = [];
        
        if (this.data.pid === 0) {
          // 主分类排序
          categories = res.data.map((item, index) => {
            // 处理图标
            this.processIconType(item);
            
            // 添加排序索引
            item.sortIndex = index;
            return item;
          });
        } else {
          // 子分类排序，找到对应的父分类
          const parentCategory = res.data.find(item => item.id === this.data.pid);
          
          if (parentCategory && parentCategory.child && Array.isArray(parentCategory.child)) {
            categories = parentCategory.child.map((item, index) => {
              // 处理图标
              this.processIconType(item);
              
              // 添加排序索引
              item.sortIndex = index;
              return item;
            });
          }
        }
        
        // 更新分类数据
        this.setData({
          categories: categories
        });
      } else {
        wx.showToast({
          title: res.msg || '获取分类数据失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('获取分类数据失败:', err);
      
      wx.showToast({
        title: '网络请求失败',
        icon: 'none'
      });
    });
  },

  /**
   * 处理图标类型
   */
  processIconType: function(item) {
    if (item.image) {
      // 检查是否是文字图标
      if (typeof item.image === 'string' && item.image.startsWith('text:')) {
        item.imageType = 'text';
        item.iconContent = item.image.substring(5); // 去掉'text:'前缀
      } 
      // 检查是否是emoji图标
      else if (typeof item.image === 'string' && item.image.startsWith('emoji:')) {
        item.imageType = 'emoji';
        item.iconContent = item.image.substring(6); // 去掉'emoji:'前缀
      }
      // 检查是否是base64格式图片
      else if (typeof item.image === 'string' && item.image.startsWith('data:image/')) {
        item.imageType = 'image';
        // base64格式图片不需要通过getImageUrl处理
      }
      // 普通图片
      else {
        item.imageType = 'image';
        item.image = util.getImageUrl(item.image);
      }
    }
    return item;
  },

  /**
   * 触摸开始事件
   */
  touchStart: function(e) {
    const index = e.currentTarget.dataset.index;
    
    this.setData({
      isDragging: true,
      startY: e.touches[0].clientY,
      currentIndex: index,
      moveY: 0
    });
  },

  /**
   * 触摸移动事件
   */
  touchMove: function(e) {
    if (!this.data.isDragging) return;
    
    const moveY = e.touches[0].clientY - this.data.startY;
    this.setData({
      moveY: moveY
    });
  },

  /**
   * 触摸结束事件
   */
  touchEnd: function(e) {
    if (!this.data.isDragging) return;
    
    const currentIndex = this.data.currentIndex;
    const endY = e.changedTouches[0].clientY;
    const startY = this.data.startY;
    const moveDistance = endY - startY;
    
    // 计算移动了多少个位置（每个项高度约为80px）
    const moveItems = Math.round(moveDistance / 80);
    
    if (moveItems !== 0) {
      // 计算目标位置
      let targetIndex = currentIndex + moveItems;
      
      // 边界检查
      if (targetIndex < 0) targetIndex = 0;
      if (targetIndex >= this.data.categories.length) {
        targetIndex = this.data.categories.length - 1;
      }
      
      // 如果位置变化了，重新排序
      if (targetIndex !== currentIndex) {
        this.reorderCategories(currentIndex, targetIndex);
      }
    }
    
    this.setData({
      isDragging: false,
      currentIndex: -1,
      moveY: 0
    });
  },

  /**
   * 重新排序分类
   */
  reorderCategories: function(fromIndex, toIndex) {
    const categories = [...this.data.categories];
    
    // 移动元素
    const item = categories.splice(fromIndex, 1)[0];
    categories.splice(toIndex, 0, item);
    
    // 更新排序索引
    categories.forEach((item, index) => {
      item.sortIndex = index;
    });
    
    // 更新数据
    this.setData({
      categories: categories,
      sortChanged: true
    });
  },

  /**
   * 保存排序
   */
  saveSort: function() {
    if (!this.data.sortChanged) {
      wx.showToast({
        title: '排序未改变',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '保存中...',
    });
    
    // 构建请求参数
    const params = {
      accountbook_id: this.data.accountbook_id,
      pid: this.data.pid
    };
    
    // 添加排序参数
    this.data.categories.forEach((item, index) => {
      params[`sort[${item.id}]`] = this.data.categories.length - index; // 越大越靠前
    });
    
    console.log('保存排序，请求参数:', params);
    
    // 调用排序接口
    sortCategory(params).then(res => {
      wx.hideLoading();
      
      if (res && res.code === 1) {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });
        
        // 返回上一页
        setTimeout(() => {
          // 设置上一页需要刷新
          const pages = getCurrentPages();
          if (pages.length > 1) {
            const prevPage = pages[pages.length - 2];
            if (prevPage && prevPage.setData) {
              prevPage.setData({
                needReload: true
              });
            }
          }
          
          wx.navigateBack();
        }, 1500);
      } else {
        wx.showToast({
          title: res.msg || '保存失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('保存排序失败:', err);
      
      wx.showToast({
        title: '网络请求失败',
        icon: 'none'
      });
    });
  },

  /**
   * 返回上一页
   */
  goBack: function() {
    if (this.data.sortChanged) {
      wx.showModal({
        title: '提示',
        content: '排序已改变，是否保存？',
        cancelText: '不保存',
        confirmText: '保存',
        success: (res) => {
          if (res.confirm) {
            this.saveSort();
          } else {
            wx.navigateBack();
          }
        }
      });
    } else {
      wx.navigateBack();
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})