import request from '../request';

// 获取账本列表
export function getAccountBookList() {
  return request({
    url: '/book/list_accountbook',
    method: 'GET'
  });
}

// 设置默认账本
export function setDefaultBook(bookId) {
  return request({
    url: '/book/set_default_book',
    method: 'POST',
    data: {
      book_id: bookId
    }
  });
}
// 设置账本排序
export function setBookSort(data) {
  return request({
    url: 'book/sort_accountbook',
    method: 'POST',
    data: data
  });
}
// 设置添加账本
export function addAccountBook(data) {
  return request({
    url: 'book/add_accountbook',
    method: 'POST',
    data: data
  });
}
// 账本详情
export function getAccountBookDetail(data) {
  return request({
    url: 'book/accountbook',
    method: 'POST',
    data: data
  });
}
// 编辑账本
export function editAccountBook(data) {
  return request({
    url: 'book/edit_accountbook',
    method: 'POST',
    data: data
  });
}
// 删除账本
export function deleteAccountBook(data) {
  return request({
    url: 'book/del_accountbook',
    method: 'POST',
    data: data
  });
}
// 封存账单列表
export function getSequesterBookList(data) {
  return request({
    url: 'book/get_sequester_book_list',
    method: 'POST',
    data: data
  });
}
// 迁移账本
export function transferBook(data) {
  return request({
    url: 'book/bill_migrate_book',
    method: 'POST',
    data: data
  });
}
// 删除账本下的账单
export function deleteAccountBookBill(data) {
  return request({
    url: 'book/del_bill_accountbook',
    method: 'POST',
    data: data
  });
}