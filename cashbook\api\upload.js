// api/upload.js

// 导入基础URL（与request.js保持一致）
import request from './request';

// 从request.js中获取BASE_URL
const BASE_URL = 'http://www.youcai.com/index.php/api/';

// 请求拦截器
const uploadInterceptor = (options) => {
  // 处理URL
  if (options.url && !options.url.startsWith('http')) {
    options.url = BASE_URL + options.url;
  }
  
  // 处理header
  options.header = {
    ...options.header,
    'token': wx.getStorageSync('token') || ''
  };
  
  return options;
};

// 响应拦截器
const responseInterceptor = (res) => {
  if (res.statusCode === 200) {
    try {
      // 尝试解析JSON
      const data = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
      return data;
    } catch (e) {
      console.error('解析响应数据失败:', e);
      return res.data;
    }
  } else {
    console.error('上传请求出错:', res);
    return null;
  }
};

// 封装上传文件方法
const uploadFile = (options) => {
  // 合并默认配置和用户传入的配置
  const defaultOptions = {
    url: '',
    filePath: '',
    name: 'file',
    header: {},
    formData: {}
  };
  const mergedOptions = { ...defaultOptions, ...options };
  
  // 请求拦截器处理
  const interceptedOptions = uploadInterceptor(mergedOptions);
  
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      ...interceptedOptions,
      success: (res) => {
        const responseData = responseInterceptor(res);
        if (responseData) {
          resolve(responseData);
        } else {
          reject(new Error('上传出错'));
        }
      },
      fail: (err) => {
        console.error('上传失败:', err);
        reject(err);
      }
    });
  });
};

export default uploadFile;