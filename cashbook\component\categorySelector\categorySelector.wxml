<view class="container {{show ? 'show' : ''}}" bindtap="closeSelector">
  <view class="selector-content" catchtap="preventBubble">
    <!-- 顶部操作栏 -->
    <view class="header">
      <view class="header-left">
        <view class="close-btn" bindtap="closeSelector"></view>
      </view>
      <view class="header-right">
        <!-- 设置按钮，仅在showSettings为true时显示 -->
        <view class="settings-btn" bindtap="onSettingsClick" wx:if="{{showSettings}}">
          <image src="{{settingsIcon}}" mode="aspectFit" />
        </view>
        <view class="mode-switch" bindtap="toggleMode" style="background-color: {{themeColor}};">
          <text>{{mode === 'list' ? '卡片' : '列表'}}</text>
        </view>
        <view class="add-btn" bindtap="addCategory">
          <text>添加</text>
        </view>
      </view>
    </view>

    <!-- 列表模式 -->
    <scroll-view scroll-y="{{true}}" class="category-list" wx:if="{{mode === 'list'}}">
      <view class="list-container">
        <view
          class="list-item"
          wx:for="{{categories.length > 0 ? categories : defaultCategories}}"
          wx:key="index"
          bindtap="selectCategory"
          data-index="{{index}}"
        >
          <view class="item-icon">
            <image src="{{item.icon}}" mode="aspectFit" />
          </view>
          <view class="item-info">
            <text class="item-name">{{item.name}}</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 卡片模式 -->
    <scroll-view scroll-y="{{true}}" class="category-grid" wx:else>
      <view class="grid-wrapper">
        <view class="grid-container">
          <view
            class="grid-item"
            wx:for="{{categories.length > 0 ? categories : defaultCategories}}"
            wx:key="index"
            bindtap="selectCategory"
            data-index="{{index}}"
          >
            <view class="item-icon">
              <image src="{{item.icon}}" mode="aspectFit" />
            </view>
            <text class="item-name">{{item.name}}</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部安全区域 -->
    <view class="safe-area-bottom"></view>
  </view>
</view>

