// pages/bookSetting/bookSetting.js
import { getAccountBookDetail, setDefaultBook } from '../../../api/book/index';
import { 
  getAccountBookList, 
  editAccountBook, 
  deleteAccountBook,
  deleteAccountBookBill } from '../../../api/book/index';
const util = require('../../../utils/index.js');
Page({
  /**
   * 页面的初始数据
   */
  data: {
    defaultQueryBook: false,  // 默认查询账本开关状态
    defaultSelectedBook: true,  // 默认选择账本开关状态
    hideDefaultBook: false,    // 默认账本隐藏状态
    titleLineColor: '#a2b486',
    showDeletePopup: false,    // 是否显示删除确认弹窗
    showQueryBookPopup: false, // 是否显示设置默认查询账本弹窗
    showCloseQueryPopup: false, // 是否显示关闭默认查询账本弹窗
    showSelectedBookPopup: false, // 是否显示设置默认选择账本弹窗
    showCloseSelectedPopup: false, // 是否显示关闭默认选择账本弹窗
    showHideBookPopup: false,  // 是否显示隐藏账本弹窗
    showArchiveBookPopup: false, // 是否显示账本封存弹窗
    isArchived: false,         // 账本是否已封存
    currentBook: '默认账本',    // 当前选中的账本
    showAddBookPopup: false,   // 是否显示添加账本弹窗
    bookCoverImage: '',        // 账本封面图片
    bookName: '',              // 账本名称
    bookRemark: '',            // 账本备注
    selectedCategoryId: '',    // 选中的分类ID
    selectedCategoryName: '',  // 选中的分类名称
    selectedCategoryDesc: '',  // 选中的分类描述
    currentBookId: '',         // 当前账本ID
    selectedColor: '#a2b486',  // 选中的颜色
    is_default: 0,            // 是否是默认账本
    inputFocusStates: {        // 输入框焦点状态
      name: false,
      remark: false
    },
    showCoverSelectPopup: false, // 是否显示封面选择弹窗
    coverList: [               // 封面列表
      { url: 'http://www.youcai.com/uploads/20250515/289b4a0c8b7de41b385f5dc285f174f2.jpg' },
      { url: 'http://www.youcai.com/uploads/20250528/8b9b115f6b633bcd493b6c8244cb8a7c.jpg' },
      { url: 'http://www.youcai.com/uploads/20250528/8b9b115f6b633bcd493b6c8244cb8a7c.jpg' },
      { url: 'http://www.youcai.com/uploads/20250528/8b9b115f6b633bcd493b6c8244cb8a7c.jpg' },

    ],
    showCategoryInitPopup: false, // 是否显示分类初始化弹窗
    categoryList: [            // 分类列表
      { id: 0, name: '不需要分类', desc: '不需要分类，自行添加' },
      { id: 1, name: '默认类型', desc: '账号初始化相同类型分类模板' },
      { id: 2, name: '复制其他账本分类', desc: '选择其他账本分类' }
    ],
    showBookSelector: false, // 是否显示账本选择弹窗
    bookList: [],  // 账本列表数据
    showDeleteBookConfirmPopup: false, // 是否显示删除账本确认弹窗
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log(options, 'options');
    // 从本地存储获取开关状态
    this.getSettingsFromStorage();


    // 获取主题颜色
    const selectedColor = wx.getStorageSync('selectedColor');

    this.setData({
      titleLineColor: selectedColor,
      selectedColor: selectedColor,
      id: options.id
    });

    this.navigateToCoverModify();

    // 如果传入了id参数，预加载账本数据
    // if (options && options.id) {
    //   // 使用传入的id参数初始化账本编辑弹窗
    //   this.navigateToCoverModify(options.id);
    // }

    // 获取账本列表数据
    this.getAccountBooks();
  },

  closeAddBookPopup() {
    this.setData({
      showAddBookPopup: false,
      selectedCategoryName: '',
      selectedCategoryDesc: '',
      selectedCategoryId: 0
    });
  },


  onBookSelect(e) {
    console.log('选中账本:', e.detail);

    const bookId = e.detail.bookId;
    const selectedBook = this.data.bookList.find(item => item.id == bookId);

    if (selectedBook) {
      // 设置分类来源账本
      this.setData({
        selectedCategoryId: bookId,
        selectedCategoryName: '复制自: ' + (selectedBook.name || ''),
        selectedCategoryDesc: '使用该账本的分类配置'
      });

      // 关闭账本选择器
      this.closeBookSelector();

      // 关闭分类初始化弹窗
      this.closeCategoryInitPopup();

      // 提示用户选择成功
      wx.showToast({
        title: '已选择账本分类',
        icon: 'success'
      });
    }
  },

  // 关闭账本选择器
  closeBookSelector() {
    this.setData({
      showBookSelector: false
    });
  },

  /**
   * 从本地存储获取设置状态
   */
  getSettingsFromStorage: function () {
    const that = this;
    wx.getStorage({
      key: 'bookSettings',
      success(res) {
        if (res.data) {
          that.setData({
            defaultQueryBook: res.data.defaultQueryBook,
            defaultSelectedBook: res.data.defaultSelectedBook,
            hideDefaultBook: res.data.hideDefaultBook 
          });
        }
      }
    });
  },

  // 删除所有账单
  handleConfirm() {
    console.log('确定按钮点击');
    // 隐藏弹窗
    this.hideDeleteConfirmPopup();

    // 显示加载中
    wx.showLoading({
      title: '删除中...',
      mask: true
    });

    // 构建参数
    const params = {
      accountbook_id: this.data.id // 使用当前账本ID
    };

    // 调用删除账本所有账单API
    deleteAccountBookBill(params).then(res => {
      wx.hideLoading();

      if (res.code === 1) {
        // 显示成功提示
        wx.showToast({
          title: '删除成功',
          icon: 'success',
          duration: 2000
        });
      } else {
        wx.showToast({
          title: res.msg || '删除失败',
          icon: 'none'
        });
      }

      // 重置弹窗状态，确保下次可以再次显示
      this.setData({
        showDeletePopup: false
      });
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '网络请求失败',
        icon: 'none'
      });
      console.error('删除账本所有账单失败:', err);
      
      // 重置弹窗状态
      this.setData({
        showDeletePopup: false
      });
    });
  },

  /**
   * 保存设置到本地存储
   */
  saveSettingsToStorage: function () {
    wx.setStorage({
      key: 'bookSettings',
      data: {
        defaultQueryBook: this.data.defaultQueryBook,
        defaultSelectedBook: this.data.defaultSelectedBook,
        hideDefaultBook: this.data.hideDefaultBook
      }
    });
  },

  /**
   * 切换默认查询账本开关
   */
  toggleDefaultQueryBook: function (e) {
    // 不直接切换开关，而是显示确认弹窗
    if (e.detail.value) {
      // 如果用户尝试打开开关，先将开关置回关闭状态，然后显示弹窗
      this.setData({
        defaultQueryBook: false
      });

      // 显示设置弹窗来确认是否打开开关
      this.navigateToBookSetting();
    } else {
      // 如果用户尝试关闭开关，先将开关置回打开状态，然后显示关闭确认弹窗
      this.setData({
        defaultQueryBook: true,
        showCloseQueryPopup: true
      });
    }
  },

  /**
   * 显示设置默认查询账本弹窗
   */
  navigateToBookSetting: function () {
    this.setData({
      showQueryBookPopup: true
    });
  },

  /**
   * 隐藏设置默认查询账本弹窗
   */

  hideQueryBookPopup: function () {
    this.setData({
      showQueryBookPopup: false
    });
  },

  /**
   * 确认设置默认查询账本
   */
  confirmSetQueryBook: function () {
    // 隐藏弹窗
    this.hideQueryBookPopup();

    // 显示加载中
    wx.showLoading({
      title: '保存中...',
      mask: true
    });

    // 构建更新账本的参数
    const params = {
      accountbook_id: this.data.currentBookId,
      is_choose: 1  // 设置为默认查询账本
    };

    // 调用修改账本API
    editAccountBook(params).then(res => {
      wx.hideLoading();

      if (res.code === 1) {
        // 开启默认查询账本开关
        this.setData({
          defaultQueryBook: true
        });

        // 提示用户设置成功
        // wx.showToast({
        //   title: '设置成功，请重启应用',
        //   icon: 'none',
        //   duration: 2000
        // });
      } else {
        wx.showToast({
          title: res.msg || '设置失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      // wx.showToast({
      //   title: '网络错误，请重试',
      //   icon: 'none'
      // });
      console.error('设置默认查询账本失败:', err);
    });

    // 重置弹窗状态
    this.setData({
      showQueryBookPopup: false
    });
  },

  /**
   * 切换默认选择账本开关
   */
  toggleDefaultSelectedBook: function (e) {
    // 不直接切换开关，而是显示确认弹窗
    if (e.detail.value) {
      // 如果用户尝试打开开关，先将开关置回关闭状态，然后显示弹窗
      this.setData({
        defaultSelectedBook: false,
        showSelectedBookPopup: true
      });
    } else {
      // 如果用户尝试关闭开关，先将开关置回打开状态，然后显示关闭确认弹窗
      this.setData({
        defaultSelectedBook: true,
        showCloseSelectedPopup: true
      });
    }
  },

  /**
   * 显示设置默认选择账本弹窗
   */
  navigateToSelectedBookSetting: function () {
    this.setData({
      showSelectedBookPopup: true
    });
  },

  /**
   * 隐藏设置默认选择账本弹窗
   */
  hideSelectedBookPopup: function () {
    this.setData({
      showSelectedBookPopup: false
    });
  },

  /**
   * 确认设置默认选择账本
   */
  confirmSetSelectedBook: function () {
    // 隐藏弹窗
    this.hideSelectedBookPopup();

    // 显示加载中
    wx.showLoading({
      title: '保存中...',
      mask: true
    });

    // 构建更新账本的参数
    const params = {
      accountbook_id: this.data.currentBookId,
      is_select: 1  // 设置为默认选择账本
    };

    // 调用修改账本API
    editAccountBook(params).then(res => {
      wx.hideLoading();

      if (res.code === 1) {
        // 开启默认选择账本开关
        this.setData({
          defaultSelectedBook: true
        });

        // 提示用户设置成功
        // wx.showToast({
        //   title: '设置成功',
        //   icon: 'success',
        //   duration: 2000
        // });
      } else {
        // wx.showToast({
        //   title: res.msg || '设置失败',
        //   icon: 'none'
        // });
      }
    }).catch(err => {
      wx.hideLoading();
      // wx.showToast({
      //   title: '网络错误，请重试',
      //   icon: 'none'
      // });
      console.error('设置默认选择账本失败:', err);
    });

    // 重置弹窗状态
    this.setData({
      showSelectedBookPopup: false
    });
  },

  /**
   * 切换默认账本隐藏开关
   */
  toggleHideDefaultBook: function (e) {
    // 不直接切换开关，而是显示确认弹窗
    if (e.detail.value) {
      // 如果用户尝试打开开关，先将开关置回关闭状态，然后显示弹窗
      this.setData({
        hideDefaultBook: false,
        showHideBookPopup: true
      });
    } else {
      // 如果用户尝试关闭开关（取消隐藏），直接更新状态
      this.updateBookStatus('normal');
    }
  },

  /**
   * 显示删除确认弹窗
   */
  showDeleteConfirmPopup: function () {
    this.setData({
      showDeletePopup: true
    });
  },

  /**
   * 隐藏删除确认弹窗
   */
  hideDeleteConfirmPopup: function () {
    this.setData({
      showDeletePopup: false
    });
  },

  /**
   * 确认删除所有账单
   */
  confirmDeleteAll: function () {
    // 隐藏弹窗
    this.hideDeleteConfirmPopup();

    // 显示加载中
    wx.showLoading({
      title: '删除中...',
      mask: true
    });

    // 构建参数
    const params = {
      accountbook_id: this.data.id // 使用当前账本ID
    };

    // 调用删除账本所有账单API
    deleteAccountBookBill(params).then(res => {
      wx.hideLoading();

      if (res.code === 1) {
        // 显示成功提示
        wx.showToast({
          title: '删除成功',
          icon: 'success',
          duration: 2000
        });
      } else {
        wx.showToast({
          title: res.msg || '删除失败',
          icon: 'none'
        });
      }

      // 重置弹窗状态，确保下次可以再次显示
      this.setData({
        showDeletePopup: false
      });
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '网络请求失败',
        icon: 'none'
      });
      console.error('删除账本所有账单失败:', err);
      
      // 重置弹窗状态
      this.setData({
        showDeletePopup: false
      });
    });
  },

  /**
   * 处理确认弹窗的取消事件
   */
  handleCancel: function () {
    console.log('取消按钮点击');
    this.hideDeleteConfirmPopup();
  },

  /**
   * 导航到收支分类页面
   */
  navigateToCategory: function () {
    wx.navigateTo({
      url: '/packageA/pages/bookSetting/incomeCategory/incomeCategory?id=' + this.data.id
    });
  },

  /**
   * 导航到账单迁移页面
   */
  navigateToBillTransfer: function () {
    wx.navigateTo({
      url: '/packageA/pages/bookSetting/billTransfer/billTransfer?id=' + this.data.id
    });
  },

  /**
   * 导航到删除所有账单页面
   */
  navigateToDeleteAll: function () {
    wx.navigateTo({
      url: '/packageA/pages/deleteAllBills/deleteAllBills'
    });
  },

  /**
   * 导航到账本封面修改页面
   */
  navigateToCoverModify: function (showPopup = false) {

    // 只有在需要时才显示弹窗
    if (showPopup) {
      this.setData({
        showAddBookPopup: true
      });
    }

    this.setData({
      currentBookId: this.data.id
    });

    // 调用API获取账本详情
    getAccountBookDetail({
      accountbook_id: this.data.id
    }).then(res => {
      wx.hideLoading();

      if (res.code === 1 && res.data) {
        console.log(res.data, 'res.data');

        // 设置账本详情数据
        this.setData({
          // 图片，使用util.getImageUrl处理图片路径
          bookCoverImage: util.getImageUrl(res.data.image),
          // 名称
          bookName: res.data.name,
          // 备注
          bookRemark: res.data.notes,
          // category_id
          selectedCategoryId: res.data.category_id,
          // category_name
          selectedCategoryName: res.data.category_name,
          selectedCategoryDesc: res.data.category_desc,
          is_default: res.data.is_default,
          // 根据接口返回数据设置开关状态
          defaultQueryBook: res.data.is_choose == 1,  // 默认查询账本
          defaultSelectedBook: res.data.is_select == 1, // 默认选择账本
          inputFocusStates: {
            name: false,
            remark: false
          }
        });
      } else {
        // wx.showToast({
        //   title: res.msg || '获取账本详情失败',
        //   icon: 'none'
        // });
      }
    }).catch(err => {
      wx.hideLoading();
      // wx.showToast({
      //   title: '网络错误，请重试',
      //   icon: 'none'
      // });
      console.error('获取账本详情失败:', err);
    });
  },

  /**
   * 关闭添加账本弹窗
   */
  closeAddBookPopup: function () {
    this.setData({
      showAddBookPopup: false
    });
  },

  /**
   * 刷新账本封面
   */
  refreshBookCover: function () {
    // 随机获取封面图片URL或提供封面选择功能
    const coverImages = [
      'http://www.youcai.com/uploads/20250515/289b4a0c8b7de41b385f5dc285f174f2.jpg',
      'http://www.youcai.com/uploads/20250528/8b9b115f6b633bcd493b6c8244cb8a7c.jpg',
      'http://www.youcai.com/uploads/20250515/8fb0d764ca43bc86b3315feea2b09c90.jpg',
      'http://www.youcai.com/uploads/20250515/cfa742290e6837e1d4fbf6e000b64394.jpg'
    ];

    const randomIndex = Math.floor(Math.random() * coverImages.length);
    this.setData({
      bookCoverImage: util.getImageUrl(coverImages[randomIndex])
    });
  },

  /**
   * 处理输入框获得焦点
   */
  onInputFocus: function (e) {
    const field = e.currentTarget.dataset.field;
    const focusStates = this.data.inputFocusStates || {};
    focusStates[field] = true;

    this.setData({
      inputFocusStates: focusStates
    });
  },

  /**
   * 处理输入框失去焦点
   */
  onInputBlur: function (e) {
    const field = e.currentTarget.dataset.field;
    const focusStates = this.data.inputFocusStates || {};
    focusStates[field] = false;

    this.setData({
      inputFocusStates: focusStates
    });
  },

  /**
   * 处理账本名称输入
   */
  onBookNameInput: function (e) {
    this.setData({
      bookName: e.detail.value
    });
  },

  /**
   * 处理账本备注输入
   */
  onBookRemarkInput: function (e) {
    this.setData({
      bookRemark: e.detail.value
    });
  },

  /**
   * 打开分类初始化选择
   */
  initCategory: function () {
    // 获取分类列表数据
    this.getCategoryList();

    // 显示分类初始化弹窗
    this.setData({
      showCategoryInitPopup: true
    });
  },

  /**
   * 获取分类列表数据
   */
  getCategoryList: function () {
    // 显示加载中
    // wx.showLoading({
    //   title: '加载中...',
    //   mask: true
    // });

    // 从API获取分类列表
    // 这里假设有一个getCategories接口
    // 实际使用时替换为真实的API调用
    setTimeout(() => {
      // 模拟API返回数据
      const categoryList = [
        { id: 0, name: '不需要分类', desc: '不需要分类，自行添加' },
        { id: 1, name: '默认类型', desc: '账号初始化相同类型分类模板' },
        { id: 2, name: '复制其他账本分类', desc: '选择其他账本分类' }
      ];

      this.setData({
        categoryList: categoryList
      });

      wx.hideLoading();
    }, 500);

    // 实际API调用示例:
    /*
    wx.request({
      url: 'https://api.example.com/categories',
      method: 'GET',
      success: (res) => {
        if (res.statusCode === 200 && res.data) {
          this.setData({
            categoryList: res.data
          });
        }
        wx.hideLoading();
      },
      fail: (err) => {
        console.error('获取分类列表失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '获取分类列表失败',
          icon: 'none'
        });
      }
    });
    */
  },

  /**
   * 关闭分类初始化弹窗
   */
  closeCategoryInitPopup: function () {
    this.setData({
      showCategoryInitPopup: false
    });
  },

  /**
   * 选择分类
   */
  selectCategory: function (e) {
    const categoryId = e.currentTarget.dataset.id;
    const selectedCategory = this.data.categoryList.find(item => item.id == categoryId);

    if (selectedCategory) {
      this.setData({
        selectedCategoryId: selectedCategory.id,
        selectedCategoryName: selectedCategory.name,
        selectedCategoryDesc: selectedCategory.desc
      });

      if (categoryId == 2) {
        // 如果选择"复制其他账本分类"，刷新账本列表并显示账本选择器
        this.getAccountBooks();
        this.setData({
          showBookSelector: true
        });
      } else {
        // 关闭分类初始化弹窗
        this.closeCategoryInitPopup();
      }
    }
  },

  /**
   * 验证并保存新账本
   */
  validateAndSaveNewBook: function () {
    const { bookName, bookCoverImage, currentBookId } = this.data;

    if (!bookName) {
      wx.showToast({
        title: '请输入账本名称',
        icon: 'none'
      });
      return;
    }

    // 显示加载中
    wx.showLoading({
      title: '保存中...',
      mask: true
    });

    // 从完整URL中提取图片路径（如果需要的话）
    let imagePath = bookCoverImage;
    // 如果util提供了反向操作方法（从完整URL获取相对路径），可以在这里使用

    // 构建更新账本的参数
    const params = {
      accountbook_id: currentBookId,
      name: bookName,
      image: imagePath,
      notes: this.data.bookRemark || '',
      status: 'normal'  // 假设1表示正常状态
    };

    // 如果有选择分类，添加分类ID
    if (this.data.selectedCategoryId) {
      params.category_id = this.data.selectedCategoryId;
    }

    // 调用修改账本API
    editAccountBook(params).then(res => {
      wx.hideLoading();

      if (res.code === 1) {  // 假设API成功返回code为1
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });

        // 关闭弹窗
        this.closeAddBookPopup();

        // 更新页面数据或返回上一页
        // 可能需要刷新或更新父页面数据
        this.getAccountBooks();  // 刷新账本列表
      } else {
        wx.showToast({
          title: res.msg || '保存失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
      console.error('保存账本失败:', err);
    });
  },

  /**
   * 导航到账本修改页面
   */
  navigateToBookModify: function () {
    // 显示账本编辑弹窗
    this.setData({
      showAddBookPopup: true
    });
  },

  /**
   * 返回上一页
   */
  goBack: function () {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 隐藏关闭默认查询账本弹窗
   */
  hideCloseQueryPopup: function () {
    this.setData({
      showCloseQueryPopup: false
    });
  },

  /**
   * 确认关闭默认查询账本
   */
  confirmCloseQueryBook: function () {
    // 隐藏弹窗
    this.hideCloseQueryPopup();

    // 显示加载中
    wx.showLoading({
      title: '保存中...',
      mask: true
    });

    // 构建更新账本的参数
    const params = {
      accountbook_id: this.data.currentBookId,
      is_choose: 0  // 取消设置为默认查询账本
    };

    // 调用修改账本API
    editAccountBook(params).then(res => {
      wx.hideLoading();

      if (res.code === 1) {
        // 关闭默认查询账本开关
        this.setData({
          defaultQueryBook: false
        });

        // 提示用户设置成功
        wx.showToast({
          title: '已关闭默认查询，请重启应用',
          icon: 'none',
          duration: 2000
        });
      } else {
        wx.showToast({
          title: res.msg || '设置失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
      console.error('关闭默认查询账本失败:', err);
    });

    // 重置弹窗状态
    this.setData({
      showCloseQueryPopup: false
    });
  },

  /**
   * 隐藏关闭默认选择账本弹窗
   */
  hideCloseSelectedPopup: function () {
    this.setData({
      showCloseSelectedPopup: false
    });
  },

  /**
   * 确认关闭默认选择账本
   */
  confirmCloseSelectedBook: function () {
    // 隐藏弹窗
    this.hideCloseSelectedPopup();

    // 显示加载中
    wx.showLoading({
      title: '保存中...',
      mask: true
    });

    // 构建更新账本的参数
    const params = {
      accountbook_id: this.data.currentBookId,
      is_select: 0  // 取消设置为默认选择账本
    };

    // 调用修改账本API
    editAccountBook(params).then(res => {
      wx.hideLoading();

      if (res.code === 1) {
        // 关闭默认选择账本开关
        this.setData({
          defaultSelectedBook: false
        });

        // 提示用户设置成功
        wx.showToast({
          title: '已恢复跟随模式',
          icon: 'success',
          duration: 2000
        });
      } else {
        wx.showToast({
          title: res.msg || '设置失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
      console.error('关闭默认选择账本失败:', err);
    });

    // 重置弹窗状态
    this.setData({
      showCloseSelectedPopup: false
    });
  },

  /**
   * 显示封面选择弹窗
   */
  showCoverSelect: function () {
    this.setData({
      showCoverSelectPopup: true
    });
  },

  /**
   * 关闭封面选择弹窗
   */
  closeCoverSelectPopup: function () {
    this.setData({
      showCoverSelectPopup: false
    });
  },

  /**
   * 选择封面
   */
  selectCover: function (e) {
    const url = e.currentTarget.dataset.url;

    // 使用util.getImageUrl处理图片路径
    this.setData({
      bookCoverImage: util.getImageUrl(url)
    });

    // 关闭封面选择弹窗
    this.closeCoverSelectPopup();
  },

  /**
   * 上传自定义封面
   */
  uploadCustomCover: function () {
    const that = this;
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;

        // 显示加载中
        wx.showLoading({
          title: '上传中...',
          mask: true
        });

        // 在实际应用中，这里应该调用上传图片API
        // 模拟上传成功
        setTimeout(() => {
          wx.hideLoading();

          // 使用本地临时路径作为封面图片
          that.setData({
            bookCoverImage: tempFilePath
          });

          // 关闭封面选择弹窗
          that.closeCoverSelectPopup();
        }, 1000);
      }
    });
  },

  /**
   * 获取账本列表数据
   */
  getAccountBooks: function () {
    // 显示加载中
    // wx.showLoading({
    //   title: '加载中...',
    //   mask: true
    // });

    // 调用API获取账本列表
    getAccountBookList().then(res => {
      wx.hideLoading();

      if (res && res.code === 1) {
        const bookList = res.data || [];

        // 处理数据，为每个账本添加背景色
        bookList.forEach((item, index) => {
          // 设置图片路径
          item.image = item.image ? util.getImageUrl(item.image) : '';

          // 设置默认账本标识
          item.isDefault = item.is_choose == 1;
        });

        this.setData({
          bookList: bookList
        });
      } else {
        // API请求失败，使用示例数据
        console.error('获取账本列表失败:', res);
        wx.showToast({
          title: '获取账本列表失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '网络请求失败',
        icon: 'none'
      });
      console.error('获取账本列表失败:', err);
    });
  },

  /**
   * 显示账本封面修改弹窗
   */
  showCoverModify: function () {
    // 调用navigateToCoverModify并传递true参数，表示显示弹窗
    this.navigateToCoverModify(true);
  },

  /**
   * 隐藏确认隐藏账本弹窗
   */
  hideHideBookPopup: function () {
    this.setData({
      showHideBookPopup: false
    });
  },

  /**
   * 确认隐藏账本
   */
  confirmHideBook: function () {
    // 隐藏弹窗
    this.hideHideBookPopup();

    // 更新账本状态为隐藏
    this.updateBookStatus('hidden');
  },

  /**
   * 更新账本状态
   */
  updateBookStatus: function (status) {
    // 显示加载中
    wx.showLoading({
      title: '保存中...',
      mask: true
    });

    // 构建更新账本的参数
    const params = {
      accountbook_id: this.data.currentBookId,
      status: status  // 'hidden'表示隐藏，'normal'表示正常
    };

    // 调用修改账本API
    editAccountBook(params).then(res => {
      wx.hideLoading();

      if (res.code === 1) {
        // 更新开关状态
        this.setData({
          hideDefaultBook: status === 'hidden'
        });

        // 提示用户设置成功
        wx.showToast({
          title: status === 'hidden' ? '默认账本已隐藏' : '默认账本已显示',
          icon: 'success',
          duration: 1500
        });
      } else {
        wx.showToast({
          title: res.msg || '设置失败',
          icon: 'none'
        });

        // 恢复开关状态
        this.setData({
          hideDefaultBook: status !== 'hidden'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
      console.error('更新账本状态失败:', err);

      // 恢复开关状态
      this.setData({
        hideDefaultBook: status !== 'hidden'
      });
    });
  },

  /**
   * 打开账本封存弹窗
   */
  showArchiveBookPopup: function() {
    // 获取账本当前状态
    this.getBookArchiveStatus();

    // 显示弹窗
    this.setData({
      showArchiveBookPopup: true
    });
  },

  /**
   * 关闭账本封存弹窗
   */
  closeArchiveBookPopup: function() {
    this.setData({
      showArchiveBookPopup: false
    });
  },

  /**
   * 获取账本封存状态
   */
  getBookArchiveStatus: function() {
    // 显示加载中
    // wx.showLoading({
    //   title: '加载中...',
    //   mask: true
    // });

    // 调用API获取账本详情
    getAccountBookDetail({
      accountbook_id: this.data.id
    }).then(res => {
      wx.hideLoading();

      if (res.code === 1 && res.data) {
        // 设置账本封存状态
        this.setData({
          isArchived: res.data.status == 'store'
        });
      } else {
        wx.showToast({
          title: res.msg || '获取账本状态失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
      console.error('获取账本状态失败:', err);
    });
  },

  /**
   * 切换账本封存状态
   */
  toggleArchiveStatus: function(e) {
    const isArchived = e.detail.value;
    const status = isArchived ? 'store' : 'normal';
    
    // 更新账本状态
    this.updateArchiveStatus(status);
  },

  /**
   * 更新账本封存状态
   */
  updateArchiveStatus: function(status) {
    // 显示加载中
    wx.showLoading({
      title: '保存中...',
      mask: true
    });

    // 构建更新账本的参数
    const params = {
      accountbook_id: this.data.id,
      status: status  // 'store'表示封存，'normal'表示正常
    };

    // 调用修改账本API
    editAccountBook(params).then(res => {
      wx.hideLoading();

      if (res.code === 1) {
        // 更新开关状态
        this.setData({
          isArchived: status == 'store'
        });

        // 提示用户设置成功
        wx.showToast({
          title: status === 'store' ? '账本已封存' : '账本已恢复',
          icon: 'success',
          duration: 1500
        });
      } else {
        wx.showToast({
          title: res.msg || '设置失败',
          icon: 'none'
        });

        // 恢复开关状态
        this.setData({
          isArchived: status != 'store'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
      console.error('更新账本状态失败:', err);

      // 恢复开关状态
      this.setData({
        isArchived: status != 'store'
      });
    });
  },

  /**
   * 导航到账本排序页面
   */
  navigateToBookSort: function () {
    wx.navigateTo({
      url: '/packageA/pages/bookSort/bookSort'
    });
  },

  /**
   * 显示删除账本确认弹窗
   */
  showDeleteBookConfirm: function () {
    this.setData({
      showDeleteBookConfirmPopup: true
    });
  },

  /**
   * 隐藏删除账本确认弹窗
   */
  hideDeleteBookConfirmPopup: function () {
    this.setData({
      showDeleteBookConfirmPopup: false
    });
  },

  /**
   * 确认删除账本
   */
  confirmDeleteBook: function () {
    // 隐藏弹窗
    this.hideDeleteBookConfirmPopup();

    // 显示加载中
    wx.showLoading({
      title: '删除中...',
      mask: true
    });

    // 调用删除账本API
    deleteAccountBook({
      accountbook_id: this.data.id
    }).then(res => {
      wx.hideLoading();

      if (res.code === 1) {
        // 提示用户删除成功
        wx.showToast({
          title: '删除成功',
          icon: 'success',
          duration: 1500
        });

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack({
            delta: 2
          });
        }, 1500);
      } else {
        wx.showToast({
          title: res.msg || '删除失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
      console.error('删除账本失败:', err);
    });
  },
}); 