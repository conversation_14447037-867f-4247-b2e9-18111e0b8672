import request from '../request';

/**
 * 设置用户配置
 * @param {Object} params - 配置参数
 * @param {string} [params.start_days] - 每月开始记账日期
 * @param {Array} [params.select_book_id] - 默认查询账本ID
 * @param {string} [params.default_book_id] - 默认选择账本ID
 * @param {string} [params.account_tip_switch] - 未选择资产提示，0=否，1=是
 * @param {string} [params.account_id] - 默认选择账户ID
 * @param {string} [params.bill_category_type] - 记账分类样式，list=列表，card=卡片
 * @param {string} [params.bill_category_rows] - 分类显示行数
 * @param {string} [params.bill_date_type] - 记账时间类型，Ymd=年月日，YmdHi=年月日时分
 * @param {string} [params.bill_auto_deduction_switch] - 记账资产自动扣款，0=否，1=是
 * @param {string} [params.bill_many_book_switch] - 多账本，0=否，1=是
 * @param {string} [params.bill_reimburse_switch] - 报销，0=否，1=是
 * @param {string} [params.bill_tag_switch] - 根据标签区分账单，0=否，1=是
 * @param {string} [params.bill_discount_switch] - 支出账单优惠券，0=否，1=是
 * @param {string} [params.bill_not_included_switch] - 不计入收支预算，0=否，1=是
 * @param {string} [params.bill_model_switch] - 账单模版，0=否，1=是
 * @param {string} [params.bill_image_switch] - 账单图片，0=否，1=是
 * @returns {Promise} 请求结果
 */
export function setConfig(params) {
  return request({
    url: 'user/setconfig',
    method: 'POST',
    data: params
  });
}

