/* shareCard.wxss */

/* Remove van-popup override since we're using TDesign now */
/* Override TDesign popup styles if needed */
.t-popup {
  border-radius: 35px !important;
}

.t-popup__content {
  border-radius: 35px !important;
  overflow: hidden;
}

/* Format popup specific styling */
.format-popup {
  border-radius: 35px;
  overflow: hidden;
  background-color: #fff;
}

.share-container {
  padding: 30rpx 0;
  border-radius: 24rpx;
  overflow: hidden;
}

.share-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx 20rpx;
  position: relative;
}

.share-title {
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  flex: 1;
}

.close-btn {
  padding: 10rpx;
  background-color: #e1e1e1;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  position: absolute;
}

.btn-right {
  right: 30rpx;
}

.btn-left {
  left: 30rpx;
}

.share-card {
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* Card Header */
.card-header {
  background-color: #6b7c4e;
  padding: 30rpx;
  display: flex;
  align-items: center;
  position: relative;
}

.frog-avatar {
  width: 120rpx;
  height: 120rpx;
  margin-right: 20rpx;
}

.frog-avatar image {
  width: 100%;
  height: 100%;
}

.account-header {
  flex: 1;
  color: #fff;
}

.account-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.account-desc {
  font-size: 24rpx;
  opacity: 0.9;
}

.frog-badge {
  position: absolute;
  top: 20rpx;
  right: 30rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 30rpx;
  padding: 6rpx 16rpx;
}

.frog-badge text {
  color: #fff;
  font-size: 24rpx;
}

/* Card Content */
.card-content {
  background-color: #6b7c4e;
  padding: 30rpx;
  color: #fff;
}

.card-divider {
  text-align: center;
  color: #fff;
  opacity: 0.8;
  font-size: 24rpx;
  margin-bottom: 20rpx;
}

.budget-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 0 10rpx;
}

.budget-left {
  text-align: left;
  flex: 1;
}

.budget-title {
  font-size: 26rpx;
  color: #fff;
  opacity: 0.8;
  margin-bottom: 10rpx;
}

.budget-amount {
  font-size: 60rpx;
  font-weight: bold;
  color: #fff;
  line-height: 1.2;
}

.budget-right {
  width: 120rpx;
  height: 120rpx;
  position: relative;
}

.budget-circle-chart {
  width: 100%;
  height: 100%;
  position: relative;
}

.circle-progress {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.inner-circle {
  width: 74%;
  height: 74%;
  border-radius: 50%;
  position: absolute;
}

/* Budget Stats */
.budget-stats {
  background-color: transparent;
  border-radius: 16rpx;
  padding: 24rpx 0;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  border-bottom: 1px dashed rgba(255, 255, 255, 0.2);
  padding-bottom: 20rpx;
}

.stat-item {
  width: 25%;
  text-align: center;
  padding: 10rpx 0;
}

.stat-label {
  font-size: 24rpx;
  color: #fff;
  opacity: 0.8;
  margin-bottom: 8rpx;
}

.stat-value {
  font-size: 26rpx;
  color: #fff;
  font-weight: 500;
}

.stat-divider {
  height: 1px;
  background-color: rgba(255, 255, 255, 0.1);
  margin: 20rpx 0;
}

/* Daily Stats */
.daily-stats {
  padding: 10rpx 0;
}

.daily-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  justify-content: space-between;
}

.dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 10rpx;
}

.orange {
  background-color: #ff9f40;
}

.purple {
  background-color: #3b60ff;
}

.blue {
  background-color: #43c5ff;
}

.daily-label {
  font-size: 24rpx;
  color: #fff;
  flex: 1;
}

.daily-value {
  font-size: 26rpx;
  color: #fff;
}

/* Card Footer */
.card-footer {
  background-color: #5a6b3f;
  padding: 30rpx;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.frog-brand {
  display: flex;
  align-items: center;
}

.frog-avatar-mini {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}

.frog-mini {
  width: 40rpx;
  height: 40rpx;
}

.brand-name {
  font-size: 24rpx;
  font-weight: bold;
  color: #fff;
}

.brand-slogan {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 4rpx;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  padding: 20rpx 30rpx 0;
  justify-content: center;
  gap: 15rpx;
}

.action-btn {
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 30rpx;
  flex: 0 0 auto;
  background-color: #a9c389;
  color: #fff;
  border: none;
  box-shadow: none;
}

.change-color {
  background-color: #a9c389;
}

.format {
  background-color: #a9c389;
}

.save-image {
  background-color: #a9c389;
}

/* Format Popup Styles */
.format-popup {
  padding: 30rpx 0;
  background-color: #fff;
}

.format-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 45rpx 20rpx;
  position: relative;
}

.format-title {
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  flex: 1;
}

.format-options {
  padding: 0 20rpx;
}

.format-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 20rpx;
  border-bottom: 1px solid #f5f5f5;
}

.format-option:last-child {
  border-bottom: none;
}

.option-text {
  font-size: 30rpx;
  color: #333;
}

/* User Brand in Footer */
.user-brand {
  display: flex;
  padding: 10rpx 0;
}

.user-info {
  display: flex;
  align-items: center;
  width: 100%;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.user-details {
  flex: 1;
}

.user-name-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 4rpx;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
  margin-right: 10rpx;
}

.vip-badge {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 15rpx;
  padding: 2rpx 10rpx;
  font-size: 20rpx;
  color: #FFFFFF;
}

.user-record {
  font-size: 24rpx;
}