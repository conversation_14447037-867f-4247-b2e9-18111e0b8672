.calendar-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  position: fixed;
  width: 100%;
  bottom: 0;
  left: 0;
  z-index: 99999;
  border-radius: 15px 15px 0 0;
}

.current-date {
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 20px;
}

.year-control {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.year-text {
  font-size: 18px;
}

.btn-group {
  /* display: flex; */
  display: grid;
  grid-template-columns:  auto auto auto;
}

.btn {
  width: fit-content;
  padding: 8px 8px;
  /* background-color: #f0f0f0; */
  border-radius: 4px;
  margin-left: 10px;
  cursor: pointer;
  text-align: center;
}

.list-container {
  display: flex;
  flex-wrap: wrap;
}

.item {
  /* width: 100%; */
  width: fit-content;
  text-align: center;
  padding: 5px 10px;
  cursor: pointer;
  margin: 10px auto;
  border-radius: 10px;
}

.active {
  background-color: #c0d0ac;
  color: #fff;
}

.bottom-btn-group {
  justify-content: center;
  margin-top: 20px;
}

.myaddClass1 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);

  width: 100%;
}

.bg {
  background-color: #c0cfae;
  /* padding-top: 15px; */
}

/* 遮罩层 */
.mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9;
}