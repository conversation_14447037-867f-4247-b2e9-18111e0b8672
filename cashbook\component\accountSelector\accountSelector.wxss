/* component/accountSelector/accountSelector.wxss */

/* 容器样式 */
.container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 12500;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  /* 底部对齐 */
  padding-bottom: 100rpx;
  /* 距离底部的距离 */
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.container.show {
  opacity: 1;
  visibility: visible;
}

/* 选择器内容区域 */
.selector-content {
  width: 92%;
  /* 宽度92% */
  background-color: #fff;
  border-radius: 80rpx;
  /* 四周都是圆角 */
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
  /* 四周阴影 */
  display: flex;
  flex-direction: column;
  max-height: 80vh;
  transform: translateY(100%);
  /* 初始位置在屏幕下方 */
  transition: transform 0.3s ease;
}

.container.show .selector-content {
  transform: translateY(0);
  /* 显示时移动到正常位置 */
}

/* 顶部操作栏 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

/* 左侧区域 */
.header-left {
  display: flex;
  align-items: center;
}

/* 右侧区域 */
.header-right {
  display: flex;
  align-items: center;
}

/* 关闭按钮 - 使用伪元素实现X */
.close-btn {
  width: 70rpx;
  height: 70rpx;
  background-color: #f0f0f0;
  border-radius: 50%;
  position: relative;
  margin: 5rpx;
}

/* 关闭按钮的X形状 - 使用伪元素 */
.close-btn::before,
.close-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 26rpx;
  height: 3rpx;
  background-color: #666;
}

.close-btn::before {
  transform: translate(-50%, -50%) rotate(45deg);
}

.close-btn::after {
  transform: translate(-50%, -50%) rotate(-45deg);
}

/* 设置按钮样式 */
.settings-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #4BA3E1;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15rpx;
}

.settings-btn image {
  width: 36rpx;
  height: 36rpx;
}

.mode-switch {
  padding: 12rpx 26rpx;
  border-radius: 40rpx;
  color: #fff;
  font-size: 28rpx;
  margin-right: 15rpx;
  background-color: #4BA3E1;
  /* 默认蓝色，但会被themeColor覆盖 */
  min-width: 60rpx;
  text-align: center;
}

.add-btn {
  padding: 18rpx 26rpx;
  border-radius: 40rpx;
  background-color: #2D3142;
  /* 深蓝色/黑色 */
  color: #fff;
  font-size: 28rpx;
  min-width: 60rpx;
  text-align: center;
}

/* 账户列表样式 - 列表模式 */
.account-list {
  padding: 30rpx;
  overflow-y: auto;
  max-height: 60vh;
}

.account-item {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 30rpx;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 20rpx;
  position: relative;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 不选择具体账户项样式 */
.no-account-item {
  background-color: transparent;
  box-shadow: none;
  padding: 20rpx 0;
  border-top: 1rpx solid #f0f0f0;
}

.account-icon-left {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  position: relative;
  overflow: visible;
}

.account-icon-left image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.check-mark {
  position: absolute;
  bottom: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: #4cd964;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.check-mark image {
  width: 24rpx;
  height: 24rpx;
}

.account-info {
  flex: 1;
}

.account-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.account-subname {
  font-size: 24rpx;
  color: #999;
}

.account-amount {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-left: 20rpx;
}

/* 不选择具体账户样式 */
.no-account-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.no-account-icon image {
  width: 40rpx;
  height: 40rpx;
}

.no-account-info {
  flex: 1;
}

.no-account-name {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.no-account-desc {
  font-size: 24rpx;
  color: #999;
}

/* 账户网格样式 - 卡片模式 */
.account-grid {
  padding: 30rpx;
  overflow-y: auto;
  max-height: 60vh;
}

.grid-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.grid-item {
  width: 48%;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  height: 180rpx;
}

.grid-item-content {
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  position: relative;
}

.grid-amount {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  text-align: right;
  position: absolute;
  bottom: 24rpx;
  right: 24rpx;
}

.grid-info {
  margin-bottom: 10rpx;
  max-width: 70%;
}

.grid-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 6rpx;
}

.grid-subname {
  font-size: 24rpx;
  color: #999;
}

.grid-icon {
  width: 50rpx;
  height: 50rpx;
  position: absolute;
  top: 24rpx;
  right: 24rpx;
}

.grid-icon image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.check-mark-card {
  position: absolute;
  bottom: -6rpx;
  right: -6rpx;
  width: 32rpx;
  height: 32rpx;
  background-color: #4cd964;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.check-mark-card image {
  width: 20rpx;
  height: 20rpx;
}

/* Credit badge styles */
.credit-badge {
  position: absolute;
  left: 24rpx;
  bottom: 24rpx;
  display: flex;
  align-items: center;
  background-color: rgba(240, 240, 240, 0.8);
  border-radius: 30rpx;
  padding: 6rpx 12rpx;
  font-size: 22rpx;
}

.credit-type {
  color: #666;
}

.credit-score {
  margin-left: 6rpx;
  color: #ff5a5f;
  background-color: #ffebec;
  border-radius: 20rpx;
  padding: 2rpx 8rpx;
}

/* Selected card styling */
.grid-item.selected {
  border: 2rpx solid #4cd964;
}

/* Custom card styling for different types */
.grid-item.wechat {
  background-color: #f0fff0;
}

.grid-item.alipay {
  background-color: #f0f8ff;
}

.grid-item.bank-card {
  background-color: #fffaf0;
}

.grid-item.credit-card {
  background-color: #fff0f5;
}

/* 底部刷新按钮 */
.refresh-btn {
  padding: 30rpx 0;
  text-align: center;
  color: #4BA3E1;
  font-size: 28rpx;
  border-top: 1rpx solid #f0f0f0;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 20rpx;
  /* 固定高度，为弹窗底部提供一些内边距 */
  width: 100%;
  background-color: #fff;
}


.account-info .account-name {
  font-weight: bold;
}

/* 不选择具体账户卡片样式 */
.no-account-card {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: transparent;
  border-top: 1rpx solid #f0f0f0;
  margin-top: 20rpx;
}