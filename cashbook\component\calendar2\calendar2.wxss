.calendar-container {
  padding: 0 10px;
  box-sizing: border-box;
  border-radius: 15px;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 9999;
  background-color: #fbfbfb;
}
/* 遮罩层 */
.mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9;
}

.calendar-header {
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
}

.weekdays {
  display: flex;
  justify-content: space-around;
  margin-bottom: 10px;
}

.days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 5px;
}

.current-month {
  text-align: center;
  padding: 5px;
}

.other-month {
  text-align: center;
  padding: 5px;
  color: #ccc;
}

.mycard{
  background-color: #fff;
}

.dialogheader{
  display: grid;
  grid-template-columns: auto 1fr;
  align-items: center;
}
.close{
  place-self: start;
  width: 40px;
  height: 40px;
  background-color: #fff;
  border-radius: 50%;
  display: grid;
  align-items: center;
  justify-content: center;
  box-shadow: 2px 2px 1px 1px rgba(178, 171, 171, .4);
}
.dialogheader view:nth-child(2){
  justify-self: center;
  text-align: center;
  transform: translateX(-20px);
}