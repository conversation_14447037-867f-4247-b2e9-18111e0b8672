.custom-popup-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  pointer-events: none;
}

.custom-popup-container.visible {
  pointer-events: auto;
}

.custom-popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.custom-popup-container.visible .custom-popup-mask {
  opacity: 1;
}

.custom-popup-content {
  position: absolute;
  background-color: #fafafa;
  width: calc(100% - 60rpx);
  margin: 0 30rpx;
  z-index: 10000;
  transform: translateY(100%);
  transition: transform 0.35s cubic-bezier(0.33, 0.66, 0.66, 1);
  will-change: transform;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-height: 85vh;
}

.custom-popup-container.visible .custom-popup-content {
  transform: translateY(0);
}

.custom-popup-round {
  border-radius: 35px;
  overflow: hidden;
}

.custom-popup-bottom {
  bottom: 15px;
  left: 0;
  right: 0;
}

.custom-popup-center {
  top: 50%;
  left: 0;
  right: 0;
  transform: translate(0, -50%) scale(0.85);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.custom-popup-container.visible .custom-popup-center {
  transform: translate(0, -50%) scale(1);
  opacity: 1;
}

.custom-popup-top {
  top: 0;
  left: 0;
  right: 0;
  transform: translateY(-100%);
}

.custom-popup-container.visible .custom-popup-top {
  transform: translateY(0);
}

/* 新的顶部样式 */
.header-with-actions {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 30rpx 20rpx;
}

.popup-close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #737373;
  background-color: #e1e1e1;
  border-radius: 50%;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.action-btn {
  padding: 12rpx 20rpx;
  border-radius: 50rpx;
  font-size: 25rpx;
  font-weight: 500;
}

.delete-btn {
  color: #ff4242;
  border: 1px solid #ff4242;
}

.refund1-btn {
  background-color: #313447;
  color: #ffffff;
}

.edit-btn {
  background-color: #313447;
  color: white;
}

/* Account Details Specific Styles */
.account-details-body {
  padding: 0 30rpx 30rpx;
  margin-top: 10rpx;
  height: 370px;
  max-height: 370px;
  width: 100%;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
}

.detail-section {
  margin-bottom: 40rpx;
  padding-bottom: 20rpx;
  border-radius: 20px;
  background-color: #ffffff;
  padding: 10px 15px;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
}

.detail-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.section-title-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.title-line {
  width: 4px;
  height: 20px;
  border-radius: 2px;
  margin-right: 10px;
  background-image: linear-gradient(to bottom, #ff9999, white);
  background-size: 100% 100%;
}

.section-title {
  font-size: 28rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
}

.detail-label {
  font-size: 30rpx;
  color: #333;
}

.detail-value {
  font-size: 30rpx;
  color: #666;
}

.detail-value.amount {
  font-size: 32rpx;
  color: #4CAF50;
  font-weight: 500;
}

.detail-value.asset {
  color: #ff9999;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
}

.category-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.category-icon image {
  width: 40rpx;
  height: 40rpx;
}

.category-icon-text {
  font-size: 28rpx;
  color: #333;
}

.category-name {
  font-size: 30rpx;
  color: #333;
  flex: 1;
}

/* 退款样式 - 新样式 */
.refund-card {
  background-color: #ffffff;
  border-radius: 20px;
  margin: 0 30rpx 40rpx;
  padding: 30rpx;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.05);
  position: relative;
}

.refund-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.refund-title-wrapper {
  display: flex;
  align-items: center;
}

.refund-line {
  width: 8rpx;
  height: 32rpx;
  border-radius: 4rpx;
  margin-right: 12rpx;
}

.refund-title {
  font-size: 32rpx;
  color: #333;
}

.refund-btn {
  width: 90rpx;
  height: 45rpx;
  line-height: 45rpx;
  text-align: center;
  color: white;
  border-radius: 30rpx;
  font-size: 24rpx;
  box-shadow: 0 2rpx 6rpx rgba(248, 169, 179, 0.3);
}

.no-refund-text {
  font-size: 30rpx;
  color: #333;
  text-align: center;
  margin: 20rpx 0;
}

/* 分类选项列表样式 */
.more-menu-list {
  max-height: calc(70vh - 100rpx);
  overflow-y: auto;
  padding: 10rpx 0;
  background-color: #fff;
}

.more-menu-item {
  display: flex;
  align-items: center;
  padding: 25rpx 30rpx;
  border-bottom: 1px solid #f5f5f5;
}

.more-menu-item:nth-child(n+6) {
  background-color: #fff;
}

.more-menu-item:last-child {
  border-bottom: none;
}

.menu-item-icon {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
  border-radius: 50%;
  margin-right: 20rpx;
  font-size: 24rpx;
  color: #666;
}

.more-menu-item:nth-child(n+6) .menu-item-icon {
  background-color: #f9f9f9;
  color: #666;
}

.menu-item-icon image {
  width: 30rpx;
  height: 30rpx;
}

.menu-item-content {
  flex: 1;
}

.menu-item-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 400;
}

.more-menu-item:nth-child(n+6) .menu-item-title {
  color: #333;
}

.menu-item-desc {
  font-size: 22rpx;
  color: #999;
  margin-top: 4rpx;
}

.more-menu-item:nth-child(n+6) .menu-item-desc {
  color: #999;
}

.menu-item-arrow {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ccc;
}
.more-menu-item:nth-child(n+6) .menu-item-arrow {
  color: #ccc;
}

.oneColonm{
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  margin: 5px 10px;
  padding: 3px 0;
}

.time{
  background-color: #a1b584;
  padding: 3px 8px;
  border-radius: 15px;
  color: white;
  width: fit-content;
  font-size: 13px;
  justify-self: end;
}

.bill-container {
  margin: 0;
  padding-bottom: 120px; /* 增加底部内边距，避免内容被遮挡 */
  min-height: calc(100vh - 470px); /* 设置最小高度确保有足够的滚动空间 */
}
