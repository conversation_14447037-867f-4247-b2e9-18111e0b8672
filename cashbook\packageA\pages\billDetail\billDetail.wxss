.container {
  background-color: #ffffff;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 头部带操作按钮 */
.header-with-actions {
  position: relative;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.popup-close-btn {
  position: absolute;
  left: 30rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f0f0f0;
  color: #666;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.action-btn {
  padding: 12rpx 20rpx;
  border-radius: 50rpx;
  font-size: 25rpx;
  font-weight: 500;
}

.delete-btn {
  color: #ff4242;
  border: 1px solid #ff4242;
}

.refund-btn {
  background-color: #ff9999;
  color: #ffffff;
}

.edit-btn {
  background-color: #313447;
  color: white;
}

/* 详情内容区域 */
.detail-body {
  flex: 1;
  padding: 0 30rpx 30rpx;
  margin-top: 20rpx;
  height: calc(100vh - 100rpx);
}

/* 详情章节 */
.detail-section {
  margin-bottom: 40rpx;
  padding: 30rpx;
  border-radius: 20rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.title-line {
  width: 8rpx;
  height: 30rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
  background-image: linear-gradient(to bottom, #ff9999, white);
  background-size: 100% 100%;
}

.section-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

/* 分类项 */
.category-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
}

.category-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.category-icon image {
  width: 100%;
  height: 100%;
}

.category-name {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

/* 详情项 */
.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1px solid #f8f8f8;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
}

.detail-value.amount {
  font-size: 32rpx;
  color: #ff9999;
  font-weight: 500;
}

.detail-value.asset {
  color: #ff9999;
}

/* 退款部分 */
.refund-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.refund-title-wrapper {
  display: flex;
  align-items: center;
}

.refund-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.refund-btn {
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  background-color: #ff9999;
  color: white;
}

.no-refund-text {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 20rpx 0;
} 