/* component/assetList/assetList.wxss */

.asset-list-container {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin: 20rpx;
  padding: 30rpx 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 资产列表标题 */
.asset-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.asset-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.asset-actions {
  display: flex;
  align-items: center;
}

.asset-card-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 20rpx;
}

.asset-card-icon image {
  width: 32rpx;
  height: 32rpx;
}

/* 资产列表内容 */
.asset-content {
  display: flex;
  flex-direction: column;
}

/* 资产项 */
.asset-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  background-color: #fff;
  width: 100%;
}

/* 资产图标 */
.asset-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 10rpx;
  overflow: hidden;
  margin-right: 20rpx;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
}

.asset-icon image {
  width: 80%;
  height: 80%;
  object-fit: contain;
}

/* 资产信息 */
.asset-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.asset-name-row {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 6rpx;
}

.asset-name {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  color: #333333;
  font-weight: 500;
}

.asset-available-tag {
  display: inline-block;
  font-size: 24rpx;
  color: #666;
  background-color: #f2f2f2;
  padding: 0 6rpx;
  border-radius: 4rpx;
  margin-left: 8rpx;
}

.asset-detail {
  font-size: 24rpx;
  color: #999999;
  display: flex;
  align-items: center;
}

.card-type-tag {
  display: inline-block;
  padding: 0 6rpx;
  border-radius: 4rpx;
  margin-right: 8rpx;
  font-size: 24rpx;
}

/* 不同类型卡片的标签样式 */
.card-type-credit {
  background-color: #f2f2f2;
  color: #666;
}

.card-type-online {
  background-color: #f0f7ff;
  color: #4a8fe3;
}

.card-type-person {
  background-color: #fff7eb;
  color: #e59539;
}

/* 资产金额 */
.asset-amount {
  text-align: right;
  min-width: 160rpx;
}

.amount-text {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 6rpx;
}

.negative .amount-text {
  color: #333333;
}

.repayment-tag {
  font-size: 22rpx;
  color: #ffffff;
  padding: 2rpx 10rpx;
  border-radius: 4rpx;
  display: inline-block;
}

/* van-swipe-cell样式 */
.van-swipe-cell {
  background-color: #ffffff;
  margin-bottom: 1px;
  overflow: visible !important;
}

.van-swipe-cell__right {
  height: 100%;
  display: flex !important;
}


/* 右侧滑动按钮样式 */
.right-buttons {
  display: flex;
  height: 100%;
  align-items: center;
}

.hide-button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  width: 80rpx;
  color: #fff;
  font-size: 24rpx;
  background-color: #c8c8c8;
  border-radius: 14rpx;
  margin: 0 5rpx;
  padding: 10rpx;
}

.edit-button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  width: 80rpx;
  color: #fff;
  font-size: 24rpx;
  background-color: #8fd495;
  border-radius: 14rpx;
  margin: 0 5rpx;
  padding: 10rpx;
}

.delete-button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  width: 80rpx;
  color: #fff;
  font-size: 24rpx;
  background-color: #e95959;
  border-radius: 14rpx;
  margin: 0 5rpx;
  padding: 10rpx;
}

/* 查看隐藏资产按钮 */
.view-hidden-assets {
  display: flex;
  justify-content: center;
  padding: 20rpx 0;
  margin-top: 10rpx;
}

.hidden-assets-text {
  font-size: 28rpx;
  color: #8dc63f;
}

/* 删除资产弹层样式 */
.delete-popup-container {
  background-color: #fff;
  padding: 30rpx 0;
}

/* 删除选项样式 */
.delete-popup-option {
  padding: 30rpx 40rpx;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.delete-popup-option:after {
  content: '';
  position: absolute;
  left: 40rpx;
  right: 40rpx;
  bottom: 0;
  height: 1px;
  background-color: #f0f0f0;
}

.delete-popup-option:last-child:after {
  display: none;
}

.delete-popup-option-content {
  flex: 1;
}

.delete-popup-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.delete-popup-subtitle {
  font-size: 28rpx;
  color: #999;
}

.delete-popup-arrow {
  font-size: 36rpx;
  color: #ccc;
  margin-left: 20rpx;
}

/* 资产设置弹层样式 */
.settings-popup-container {
  background-color: #fff;
  padding: 0;
}

/* 设置选项样式 */
.settings-option {
  padding: 30rpx 40rpx;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
}

.settings-option:after {
  content: '';
  position: absolute;
  left: 40rpx;
  right: 40rpx;
  bottom: 0;
  height: 1px;
  background-color: #f5f5f5;
}

.settings-option:last-child:after {
  display: none;
}

.settings-option-left {
  flex: 1;
}

.settings-option-title {
  font-size: 34rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.settings-option-subtitle {
  font-size: 28rpx;
  color: #999;
}

.settings-option-right {
  display: flex;
  align-items: center;
  color: #999;
  font-size: 28rpx;
} 