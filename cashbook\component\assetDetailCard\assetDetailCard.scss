/* 资产详情卡片组件样式 */

/* 资产卡片 */
.asset-card {
  background-color: #ffffff;
  margin: 0 0 20rpx 0;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

/* 卡片信息头部 */
.asset-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: #ffffff;

  .asset-title {
    display: flex;
    align-items: center;

    .asset-icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 10rpx;
      overflow: hidden;
      margin-right: 20rpx;
      background-color: #f5f5f5;
      display: flex;
      justify-content: center;
      align-items: center;

      image {
        width: 80%;
        height: 80%;
        object-fit: contain;
      }
    }

    .asset-name {
      font-size: 32rpx;
      font-weight: 500;
      color: #333333;
    }
  }

  .edit-btn {
    font-size: 28rpx;
    color: #999999;
    padding: 8rpx 30rpx;
  }
}

/* 信用卡详情 */
.asset-details {
  background-color: #ffffff;
  padding: 20rpx 30rpx 30rpx;
}

/* 欠款和额度区域 */
.balance-limit-section {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;

  /* 当前欠款 */
  .balance-section {
    flex: 1;

    .balance-label {
      font-size: 28rpx;
      color: #666666;
      margin-bottom: 10rpx;
    }

    .balance-amount {
      font-size: 72rpx;
      font-weight: 500;
      color: #333333;

      text {
        font-family: Arial, sans-serif;
      }
    }
  }

  /* 可用额度 */
  .limit-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: flex-end;

    .limit-label {
      font-size: 28rpx;
      color: #666666;
      margin-bottom: 10rpx;
    }

    .limit-amount {
      font-size: 36rpx;
      color: #333333;
      margin-bottom: 8rpx;
    }

    .limit-progress {
      width: 100%;
      height: 10rpx;
      background-color: #f0f0f0;
      border-radius: 5rpx;
      overflow: hidden;

      .progress-bar {
        height: 100%;
        border-radius: 5rpx;
      }
    }
  }
}

/* 账单信息区域 */
.bill-info-section {
  .bill-info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10rpx;

    .bill-info-item {
      flex: 1;

      &.center-align {
        text-align: center;
      }

      &.right-align {
        text-align: right;
      }

      .bill-info-label {
        font-size: 28rpx;
        color: #666666;
        margin-bottom: 8rpx;
      }

      .bill-info-value {
        font-size: 30rpx;
        color: #333333;

        .date-status {
          display: inline-block;
          font-size: 22rpx;
          color: #666666;
          background-color: #f2f2f2;
          padding: 0 6rpx;
          border-radius: 4rpx;
          margin-left: 6rpx;
        }
      }
    }
  }

  .divider {
    height: 1rpx;
    background-color: #f0f0f0;
    margin: 20rpx 0;
  }
}

/* 还款按钮包装器 */
.repay-button-wrapper {
  position: absolute;
  right: 30rpx;
  bottom: -40rpx;
  z-index: 2;

  /* 还款按钮 */
  .repay-button {
    width: 120rpx;
    height: 70rpx;
    border-radius: 35rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #000;
    font-size: 30rpx;
    font-weight: normal;
    box-shadow: 0 6rpx 10rpx rgba(0, 0, 0, 0.1);
  }
}
