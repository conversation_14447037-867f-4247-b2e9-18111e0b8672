<view class="content">
  <!-- 返回 -->
  <view class="back" bind:tap="toback" wx:if="{{isback}}">
    <image src="/static/icon/arrDown.png" mode="" />
  </view>
  <!-- scroll-view  绑定两个属性 -->
  <scroll-view style="width: 100%;" class="list {{ !isback ?'mr0' :''}} " scroll-x="true" scroll-with-animation="{{true}}" scroll-into-view="{{scrollIntoViewId}}" enhanced="true" show-scrollbar="{{false}}">
    <!-- item 传递参数 绑定id（必须是字符串形式） 绑定事件， 拿到参数赋值给上面的scrollIntoViewId -->
    <view class="item {{item.id==cureentId ?'itemActive':''}}" style="{{item.id==cureentId ? 'background-color:' + selectedColor : ''}}" wx:for="{{list}}" id="{{'item-' + index}}" wx:key="index" data-id="{{item.id}}" bindtap="scrollToItem">
      <image wx:if="{{isIcon}}" src="{{item.icon}}" mode="" />
      {{item.name}}
    </view>
    <!-- 添加一个空的视图作为右侧边距 -->
    <view style="display: inline-block; width: 20px; height: 1px;"></view>
  </scroll-view>
  <view class="seting" wx:if="{{isSet}}" bindtap="onSettingTap">
    <image src="/static/icon/set.png" mode="" />
  </view>
</view>