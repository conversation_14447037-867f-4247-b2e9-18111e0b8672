// 收支分类页面
Page({
  /**
   * 页面的初始数据
   */
  data: {
    activeTab: 'expense', // 当前激活的标签: expense（支出）或 income（收入）
    categories: [], // 分类列表
    selectedBook: '默认账本', // 选中的账本
    currentBook: '默认账本', // 当前显示的账本名称
    statusBarHeight: 0, // 状态栏高度
    navBarHeight: 44, // 导航栏高度
    activeCategory: null, // 当前展开的分类ID，默认为null（全部折叠）
    
    // 演示数据
    expenseCategories: [
      { id: 1, name: '餐饮', icon: '/static/icons/food.png', isExpanded: false },
      { id: 2, name: '柴米油盐', icon: '/static/icons/grocery.png', isExpanded: false },
      { id: 3, name: '物业水电', icon: '/static/icons/utilities.png', isExpanded: false },
      { id: 4, name: '住宿', icon: '/static/icons/accommodation.png', isExpanded: false },
      { id: 5, name: '娱乐', icon: '/static/icons/entertainment.png', isExpanded: false },
      { id: 6, name: '美妆', icon: '/static/icons/beauty.png', isExpanded: false },
      { id: 7, name: '旅游', icon: '/static/icons/travel.png', isExpanded: false },
      { id: 8, name: '水果', icon: '/static/icons/fruits.png', isExpanded: false },
      { id: 9, name: '任天野', icon: '/static/icons/games.png', isExpanded: false }
    ],
    incomeCategories: [
      { id: 101, name: '工资', icon: '/static/icons/salary.png', isExpanded: false },
      { id: 102, name: '奖金', icon: '/static/icons/bonus.png', isExpanded: false },
      { id: 103, name: '兼职', icon: '/static/icons/part-time.png', isExpanded: false },
      { id: 104, name: '理财', icon: '/static/icons/investment.png', isExpanded: false },
      { id: 105, name: '其他收入', icon: '/static/icons/other-income.png', isExpanded: false }
    ],
    // 默认子分类数据（实际应该从后端获取）
    defaultSubCategories: {
      // 餐饮分类的子分类
      1: [
        { id: 11, name: '三餐', icon: '/static/icons/meal.png' },
        { id: 12, name: '调味油盐', icon: '/static/icons/spice.png' },
        { id: 13, name: '食材', icon: '/static/icons/ingredient.png' },
        { id: 14, name: '零食', icon: '/static/icons/snack.png' },
        { id: 15, name: '奶茶', icon: '/static/icons/milk-tea.png' },
        { id: 16, name: '咖啡', icon: '/static/icons/coffee.png' }
      ],
      // 其他默认子分类
      2: [
        { id: 21, name: '调料', icon: '/static/icons/spice.png' },
        { id: 22, name: '大米', icon: '/static/icons/rice.png' },
        { id: 23, name: '油', icon: '/static/icons/oil.png' }
      ],
      // 收入分类的子分类
      101: [
        { id: 1011, name: '月薪', icon: '/static/icons/salary.png' },
        { id: 1012, name: '年薪', icon: '/static/icons/annual-salary.png' }
      ]
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 获取系统信息
    this.getSystemInfo();
    
    // 根据参数设置初始标签
    if (options.type && options.type === 'income') {
      this.setData({
        activeTab: 'income'
      });
    }
    
    // 加载分类数据
    this.loadCategories();
  },

  /**
   * 获取系统信息，设置导航栏高度
   */
  getSystemInfo: function() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      // 状态栏高度
      const statusBarHeight = systemInfo.statusBarHeight;
      // 导航栏高度，默认44px
      const navBarHeight = 44;
      
      this.setData({
        statusBarHeight: statusBarHeight,
        navBarHeight: navBarHeight
      });
    } catch (e) {
      console.error('获取系统信息失败', e);
    }
  },

  /**
   * 加载分类数据
   */
  loadCategories: function () {
    // 根据当前标签加载相应分类
    let categoriesData = this.data.activeTab === 'expense' 
      ? JSON.parse(JSON.stringify(this.data.expenseCategories))
      : JSON.parse(JSON.stringify(this.data.incomeCategories));
    
    // 确保所有分类的初始状态为折叠
    categoriesData = categoriesData.map(item => {
      item.isExpanded = false;
      return item;
    });
    
    // 更新分类数据，确保所有分类初始状态为折叠
    this.setData({
      categories: categoriesData,
      activeCategory: null
    });
  },

  /**
   * 切换分类的展开/折叠状态
   */
  toggleCategory: function(e) {
    // 获取被点击的分类ID
    const categoryId = e.currentTarget.dataset.id;
    
    // 打印日志，用于调试
    console.log('当前点击的分类ID:', categoryId);
    
    // 获取当前分类列表的副本
    const categories = JSON.parse(JSON.stringify(this.data.categories));
    
    // 遍历分类列表，设置展开/折叠状态
    categories.forEach(item => {
      if (item.id === categoryId) {
        // 如果是点击的分类，切换其展开状态
        item.isExpanded = !item.isExpanded;
        console.log(`分类 ${item.id} 展开状态: ${item.isExpanded}`);
      } else {
        // 其他分类全部折叠（手风琴效果）
        item.isExpanded = false;
      }
    });
    
    // 更新页面数据
    this.setData({
      categories: categories,
      activeCategory: categories.find(item => item.isExpanded)?.id || null
    });
    
    console.log('更新后的分类列表:', this.data.categories);
    console.log('当前展开的分类ID:', this.data.activeCategory);
  },

  /**
   * 切换标签（支出/收入）
   */
  switchTab: function (e) {
    const tabType = e.currentTarget.dataset.type;
    if (this.data.activeTab !== tabType) {
      this.setData({
        activeTab: tabType
      });
      
      // 切换标签后重新加载分类
      this.loadCategories();
    }
  },

  /**
   * 切换账本
   */
  switchBook: function () {
    wx.showActionSheet({
      itemList: ['默认账本', '生活账本', '工作账本'],
      success: (res) => {
        if (!res.cancel) {
          const selectedBook = ['默认账本', '生活账本', '工作账本'][res.tapIndex];
          this.setData({
            selectedBook: selectedBook,
            currentBook: selectedBook
          });
          // 切换账本后重新加载分类
          this.loadCategories();
        }
      }
    });
  },

  /**
   * 编辑子分类
   */
  editSubCategory: function (e) {
    const subCategoryId = e.currentTarget.dataset.id;
    wx.showToast({
      title: `编辑子分类 ID: ${subCategoryId}`,
      icon: 'none'
    });
    
    // 阻止事件冒泡，避免触发父元素的点击事件
    e.stopPropagation();
  },
  
  /**
   * 添加子分类
   */
  addSubCategory: function (e) {
    const parentId = e.currentTarget.dataset.parentId;
    wx.showToast({
      title: `为分类 ID: ${parentId} 添加子分类`,
      icon: 'none'
    });
    
    // 阻止事件冒泡，避免触发父元素的点击事件
    e.stopPropagation();
  },
  
  /**
   * 删除分类
   */
  deleteCategory: function (categoryId) {
    wx.showModal({
      title: '提示',
      content: '确定要删除该分类吗？删除后无法恢复。',
      success: (res) => {
        if (res.confirm) {
          // 这里仅是演示，实际应该调用API删除
          let categories = [...this.data.categories];
          categories = categories.filter(item => item.id !== categoryId);
          
          // 更新分类列表
          this.setData({
            categories: categories
          });
          
          // 同时更新源数据
          const sourceKey = this.data.activeTab === 'expense' ? 'expenseCategories' : 'incomeCategories';
          const sourceData = [...this.data[sourceKey]].filter(item => item.id !== categoryId);
          
          const updateData = {};
          updateData[sourceKey] = sourceData;
          
          this.setData(updateData);
          
          wx.showToast({
            title: '分类已删除',
            icon: 'success'
          });
        }
      }
    });
  },
  
  /**
   * 添加分类
   */
  addCategory: function () {
    wx.navigateTo({
      url: `/packageA/pages/categoryEdit/categoryEdit?type=${this.data.activeTab}`
    });
  },
  
  /**
   * 返回上一页
   */
  goBack: function () {
    wx.navigateBack();
  }
});
