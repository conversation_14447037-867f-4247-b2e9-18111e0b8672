/**
 * 基础接口部分相关API
 */

import request from '../request';

/**
 * 帮助引言
 */
export function getIntroduction(data) {
  return request({
    url: 'base/introduction',
    method: 'POST',
    data: data
  });
}
/**
 * 文章分类
 */
export function getArticle_category(data) {
  return request({
    url: 'base/article_category',
    method: 'POST',
    data: data
  });
}
/**
 * 文章列表
 */
export function getArticlelist(data) {
  return request({
    url: 'base/articlelist',
    method: 'POST',
    data: data
  });
}
/**
 * 文章详情
 */
export function getArticle(data) {
  return request({
    url: 'base/article',
    method: 'POST',
    data: data
  });
}
/**
 * 视频列表
 */
export function getVideos(data) {
  return request({
    url: 'base/videos',
    method: 'POST',
    data: data
  });
}
/**
 * 客服信息
 */
export function getKefu(data) {
  return request({
    url: 'base/kefu',
    method: 'POST',
    data: data
  });
}
/**
 * 协议列表
 */
export function getAgreementList(data) {
  return request({
    url: 'base/pactlist',
    method: 'POST',
    data: data
  });
}
/**
 * 协议内容
 */
export function getAgreementDetail(data) {
  return request({
    url: 'base/pact',
    method: 'POST',
    data: data
  });
}
/**
 * 收支分类getCategory
 */
export function getCategory(data) {
  return request({
    url: 'base/category',
    method: 'POST',
    data: data
  });
}
/**
 * 预算类型getBudgetType
 */
export function getBudgetType(data) {
  return request({
    url: 'base/budget_type',
    method: 'POST',
    data: data
  });
}
