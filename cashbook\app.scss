@import './styles/font.scss';

page {
  background-color: #fbfbfb;
}
.icon2 image {
  width: 20px;
  height: 20px;
  object-fit: cover;
}

/* 描述 */
.description2 {
  padding: 0px;
  color: #b6b6b6;
  margin: 10px 0;
}
.title2 {
  margin: 10px 0;
  font-weight: 700;
  font-size: 40rpx;
}
.avator {
  display: grid;
  align-items: center;
  justify-content: center;
}
.avator image {
  width: 30px;
  height: 30px;
}
.avator2 {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  background-color: #f2f2f2;
}
/* 遮罩层 */
.mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99;
}

/* 卡片-占据文档流（无定位） */
.card3 {
  background-color: #fff;
  border-radius: 15px;
  padding: 10px;
  /* border: 1px solid; */
  margin: 10px 0;
  box-sizing: border-box;
}

.content {
  /* margin: 0 10px ; */
  padding-bottom: 100px;
}

.botomBtns {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  width: fit-content;
  display: grid;
  grid-template-columns: 1fr 1fr;
  margin: auto;
  border-radius: 20px;
}
.botomBtns view {
  padding: 5px 10px;
  border-radius: 20px;
}

.active {
  background-color: #c0cfb0;
  color: #fff;
}

.nav {
  height: 100px;
  position: relative;
  z-index: 9;
}

.pd-tb {
  padding: 10px 10px 40px 10px;
}
.cr {
  color: red;
}
.brr {
  border-radius: 10px !important;
}
.c0 {
  color: #000;
}

.botmBtn {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  background: linear-gradient(to right, #d8e1ce, #ccd9bf, #c1d0af);
  text-align: center;
  padding: 10px 0;
  font-size: 18px;
  border-radius: 15px;
}

/* 带背景的图片  */
.avatorBg {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #f7f4eb;
  margin: auto;
  display: grid;
  text-align: center;
  align-items: center;
  justify-content: center;
}
/* 弹框 */
.dialog {
  position: fixed;
  bottom: 10px;
  left: 0;
  width: 100%;
  background-color: #fbfbfb;
  padding: 10px 0;
  font-size: 36rpx;
  /* height: 250px; */
  z-index: 999;
  border-radius: 15px;
}

.dialogheader {
  display: grid;
  grid-template-columns: auto 1fr;
  align-items: center;
}
.dialogheader view:nth-child(2) {
  justify-self: center;
  text-align: center;
  transform: translateX(-20px);
}

/* 关闭按钮 */
.close {
  place-self: start;
  width: 40px;
  height: 40px;
  background-color: #fff;
  border-radius: 50%;
  display: grid;
  align-items: center;
  justify-content: center;
  box-shadow: 2px 2px 1px 1px rgba(178, 171, 171, 0.4);
}
.fs12 {
  font-size: 12px;
}
.fs13 {
  font-size: 13px;
}
.fs14 {
  font-size: 14;
}
.fs16 {
  font-size: 16;
}

.one {
  display: grid;
  grid-template-columns: auto 1fr;
  background-color: #f3f3f3;
  padding: 10px;
  /* border: 1px solid; */
  border-radius: 15px;
}
.onepic image {
  width: 20px;
  height: 20px;
  padding-right: 10px;
}

/* 头像 内容 箭头 栅格布局分类三块 */
.gridTmeplte {
  display: grid;
  grid-template-columns: auto auto 1fr;
  gap: 10px;
  align-items: center;
}
.gridTmeplte view:nth-child(3) {
  justify-self: end;
}
.noMP {
  margin: 0 !important;
  padding: 0 !important;
}
.m10 {
  margin: 10px 0;
}
.col2 {
  background-color: #f6f6f6 !important;
}

/* 转账模块样式  */
.oneClonm {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 10px;
  margin: 10px 0;
}
.oneClonm view {
  border-radius: 30px;
  background-color: #f3f3f3;
  /* padding: 10px; */
}
.zczh {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 20px;
  color: #a4a4a4;
}
.zczh image {
  width: 20px;
  height: 20px;
  object-fit: cover;
}

.tranfs {
  background-color: #f3f3f3;
  text-align: center;
  padding: 5px 10px;
  width: fit-content;
  margin: auto;
  border-radius: 15px;
}
.tranfs image {
  width: 20px;
  height: 20px;
  transform: translateY(3px);
  padding-right: 3px;
}
/* 底部按钮 占据文档流 没定位等 */
.botmBtn2 {
  width: 100%;
  background: linear-gradient(to right, #d8e1ce, #ccd9bf, #c1d0af);
  text-align: center;
  padding: 10px 0;
  font-size: 18px;
  border-radius: 15px;
}

/* 编辑管理 */
.manager .mitem {
  border-bottom: 1px solid rgb(167, 161, 161);
  margin: 10px 0;
  padding: 10px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
}
.manager .mitem view:nth-child(2) {
  align-self: center;
  justify-self: end;
}

/* 栅格左右分栏，左侧带有伪元素，右侧带有颜色 */
.header3 {
  position: relative;
  display: grid;
  grid-template-columns: 1fr 1fr;
}
.header3 view:nth-child(1) {
  padding-left: 10px;
}
.header3 view:nth-child(1)::before {
  content: '';
  position: absolute;
  top: 5px;
  left: 0px;
  width: 5px;
  height: 70%;
  background: linear-gradient(to bottom, #aee05d, #cae4a0, #fff);
  border-radius: 8px;
}
.header3 view:nth-child(2) {
  justify-self: end;
  width: fit-content;
  background-color: #f5f5f5;
  border-radius: 15px;
  padding: 5px 10px;
  color: #818181;
}

/* 输入框样式，可移动placehoder */
.editInput {
  position: relative;
  margin: 10px 0;
}
.editInput input {
  background-color: #f6f6f6;
  padding: 10px 10px;
  border-radius: 10px;
  position: relative;
}
.editInput .EIpicRight {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 30px;
  height: 30px;
}
.editInput .EIpicLeft {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 30px;
  height: 30px;
}

.customInfo {
  position: absolute;
  top: 50%;
  left: 10px;
  transform: translateY(-50%);
  font-size: 14px;
  color: #a1a1a1;
}
.myanimation {
  animation: aa 1s ease-in-out 0s 1 normal forwards;
}
@keyframes aa {
  from {
    top: 50%;
  }
  to {
    top: 0;
  }
}

.progress {
  display: grid;
  grid-template-columns: 1fr 1fr;
  font-size: 14px;
}
.progress view:nth-child(2) {
  justify-self: end;
}
.col1 {
  background-color: #f1e1c2 !important;
}
