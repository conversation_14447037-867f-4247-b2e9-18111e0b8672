Component({
  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false,
      observer: function(newVal) {
        if (newVal) {
          this.showPicker();
        } else {
          this.hidePicker();
        }
      }
    },
    // 选择模式：month-显示月份选择，year-仅显示年份选择
    mode: {
      type: String,
      value: 'month' // 默认为月份选择模式
    },
    // 初始年份
    initYear: {
      type: Number,
      value: new Date().getFullYear()
    },
    // 初始月份
    initMonth: {
      type: Number,
      value: new Date().getMonth() + 1 // JavaScript 月份是 0-11
    },
    // 最小年份
    minYear: {
      type: Number,
      value: 2000
    },
    // 最大年份
    maxYear: {
      type: Number,
      value: 2100
    },
    // 年份范围（向前后各展示多少年）
    yearRange: {
      type: Number,
      value: 5
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    currentYear: new Date().getFullYear(),
    currentMonth: new Date().getMonth() + 1,
    selectedYear: null,
    selectedMonth: null,
    // 滑动相关数据
    startY: 0,
    touchY: 0,
    touchTime: 0,
    isTouching: false,
    // 当前系统时间
    systemYear: new Date().getFullYear(),
    systemMonth: new Date().getMonth() + 1,
    // 年份swiper的当前索引
    yearSwiperIndex: 5, // 默认在中间位置
    // 年份数组
    yearItems: [],
    // 动画相关
    animationVisible: false,  // 控制动画中的可见性
    animationClass: ''       // 控制动画类名
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 设置初始值
      const month = this.properties.initMonth;
      const year = this.properties.initYear;
      
      // 获取当前系统时间
      const now = new Date();
      const systemYear = now.getFullYear();
      const systemMonth = now.getMonth() + 1;
      
      this.setData({
        currentYear: year,
        currentMonth: month,
        selectedYear: year,
        selectedMonth: month,
        systemYear,
        systemMonth
      });
      
      // 初始化年份项和月份项
      this.initYearItems();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 显示日期选择器动画
    showPicker() {
      // 先设置为可见
      this.setData({
        animationVisible: true,
      });

      // 等一小段时间再添加动画类，这样可以触发过渡效果
      setTimeout(() => {
        this.setData({
          animationClass: 'show'
        });
      }, 50);

      // 初始化年份月份选项
      this.initYearItems();
    },

    // 隐藏日期选择器动画
    hidePicker() {
      // 先移除动画类，触发关闭动画
      this.setData({
        animationClass: ''
      });

      // 等动画完成后再隐藏组件
      setTimeout(() => {
        this.setData({
          animationVisible: false
        });
      }, 300); // 动画持续时间
    },
    
    // 初始化年份项
    initYearItems() {
      const { currentYear, minYear, maxYear, yearRange } = this.data;
      
      // 计算要显示的年份范围
      const startYear = Math.max(minYear, currentYear - yearRange);
      const endYear = Math.min(maxYear, currentYear + yearRange);
      
      const yearItems = [];
      
      for (let year = startYear; year <= endYear; year++) {
        yearItems.push({
          year,
          months: this.generateMonthItems(year)
        });
      }
      
      // 计算当前年份的索引
      const yearSwiperIndex = yearItems.findIndex(item => item.year === currentYear);
      
      this.setData({
        yearItems,
        yearSwiperIndex: yearSwiperIndex >= 0 ? yearSwiperIndex : 0
      });
    },
    
    // 生成指定年份的月份项
    generateMonthItems(year) {
      const { currentYear, currentMonth, systemYear, systemMonth } = this.data;
      
      const months = [];
      
      for (let i = 1; i <= 12; i++) {
        let disabled = false;
        
        // 如果是过去的年份，所有月份都可选
        if (year < systemYear) {
          disabled = false;
        }
        // 如果是当前年份，当前月份及之前的月份可选，下个月可选，再之后不可选
        else if (year === systemYear) {
          disabled = i > systemMonth + 1;
        }
        // 如果是未来的年份，所有月份都不可选
        else {
          disabled = true;
        }
        
        months.push({
          month: i,
          disabled: disabled,
          active: year === currentYear && i === currentMonth
        });
      }
      
      return months;
    },
    
    // 选择月份
    selectMonth(e) {
      const month = parseInt(e.currentTarget.dataset.month);
      const year = parseInt(e.currentTarget.dataset.year);
      
      // 检查是否可选
      const yearItems = this.data.yearItems;
      const yearItem = yearItems.find(item => item.year === year);
      
      if (!yearItem) return;
      
      const monthItem = yearItem.months.find(m => m.month === month);
      
      if (monthItem && !monthItem.disabled) {
        // 更新所有月份的active状态
        const updatedYearItems = yearItems.map(yearItem => {
          return {
            ...yearItem,
            months: yearItem.months.map(monthItem => {
              return {
                ...monthItem,
                active: yearItem.year === year && monthItem.month === month
              };
            })
          };
        });
        
        this.setData({
          currentYear: year,
          currentMonth: month,
          selectedYear: year,
          selectedMonth: month,
          yearItems: updatedYearItems
        });
      } else {
        wx.showToast({
          title: '不能选择未来的月份',
          icon: 'none'
        });
      }
    },
    
    // 选择年份 (年份选择模式)
    selectYear(e) {
      const year = parseInt(e.currentTarget.dataset.year);
      
      // 更新所有年份的active状态
      const updatedYearItems = this.data.yearItems.map(item => {
        return {
          ...item,
          active: item.year === year
        };
      });
      
      this.setData({
        currentYear: year,
        selectedYear: year,
        yearItems: updatedYearItems
      });
    },
    
    // swiper变化事件
    onSwiperChange(e) {
      const current = e.detail.current;
      const { yearItems } = this.data;
      
      // 如果年份数组为空或索引无效，则退出
      if (!yearItems.length || current >= yearItems.length) return;
      
      const newYear = yearItems[current].year;
      
      this.setData({
        currentYear: newYear,
        yearSwiperIndex: current
      });
      
      // 如果滑动到了边缘，可以考虑重新生成年份列表
      if (current === 0 || current === yearItems.length - 1) {
        this.initYearItems();
      }
    },
    
    // 上一年
    prevYear() {
      const { yearSwiperIndex } = this.data;
      if (yearSwiperIndex > 0) {
        this.setData({
          yearSwiperIndex: yearSwiperIndex - 1
        });
      } else {
        // 如果已经是第一项，可以重新生成年份列表
        this.setData({
          currentYear: this.data.currentYear - 1
        }, () => {
          this.initYearItems();
        });
      }
    },
    
    // 下一年
    nextYear() {
      const { yearSwiperIndex, yearItems } = this.data;
      if (yearSwiperIndex < yearItems.length - 1) {
        this.setData({
          yearSwiperIndex: yearSwiperIndex + 1
        });
      } else {
        // 如果已经是最后一项，可以重新生成年份列表
        this.setData({
          currentYear: this.data.currentYear + 1
        }, () => {
          this.initYearItems();
        });
      }
    },
    
    // 取消选择
    onCancel() {
      // 触发退出动画
      this.hidePicker();
      
      // 延迟触发取消事件，让动画有时间完成
      setTimeout(() => {
        this.triggerEvent('cancel');
      }, 300);
    },
    
    // 确认选择
    onConfirm() {
      const { selectedYear, selectedMonth, mode } = this.data;
      
      // 检查是否已选择月份
      if (mode === 'month' && !selectedMonth) {
        wx.showToast({
          title: '请选择月份',
          icon: 'none'
        });
        return;
      }
      
      // 关闭选择器
      this.hidePicker();
      
      // 如果是仅年份模式，只传年份
      if (mode === 'year') {
        // 构造1月1日到12月31日的年份日期范围
        const startDate = new Date(selectedYear, 0, 1);
        const endDate = new Date(selectedYear, 11, 31);
        
        // 格式化日期范围
        const dateRange = `${selectedYear}年1月1日-${selectedYear}年12月31日`;
        
        this.triggerEvent('confirm', {
          year: selectedYear,
          date: `${selectedYear}年`,
          startDate,
          endDate,
          dateRange
        });
      } else {
        // 月份模式，返回详细日期信息
        const daysInMonth = new Date(selectedYear, selectedMonth, 0).getDate();
        
        // 构造日期范围
        const startDate = new Date(selectedYear, selectedMonth - 1, 1);
        const endDate = new Date(selectedYear, selectedMonth - 1, daysInMonth);
        
        // 格式化日期范围
        const dateRange = `${selectedYear}年${selectedMonth}月1日-${selectedYear}年${selectedMonth}月${daysInMonth}日`;
        
        this.triggerEvent('confirm', {
          year: selectedYear,
          month: selectedMonth,
          date: `${selectedYear}年${selectedMonth}月`,
          startDate,
          endDate,
          dateRange
        });
      }
    },
    
    // 触摸开始
    touchStart(e) {
      this.setData({
        startY: e.touches[0].clientY,
        touchY: e.touches[0].clientY,
        touchTime: e.timeStamp,
        isTouching: true
      });
    },
    
    // 触摸移动
    touchMove(e) {
      if (!this.data.isTouching) return;
      
      const currentY = e.touches[0].clientY;
      const moveY = currentY - this.data.touchY;
      
      this.setData({
        touchY: currentY
      });
      
      // 上下滑动判断逻辑
      if (Math.abs(moveY) > 10) {
        if (moveY > 0) {
          // 下滑，前一年
          this.prevYear();
        } else {
          // 上滑，后一年
          this.nextYear();
        }
        
        // 防止连续触发，设置一个小延迟
        this.setData({
          isTouching: false
        });
        
        setTimeout(() => {
          this.setData({
            isTouching: true
          });
        }, 200);
      }
    },
    
    // 触摸结束
    touchEnd(e) {
      this.setData({
        isTouching: false
      });
    }
  }
})