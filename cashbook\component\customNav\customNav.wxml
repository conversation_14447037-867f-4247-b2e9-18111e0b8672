<view class="box" >
  <view class="rightMoudls" wx:if="{{isClose}}" bind:tap="back">
    <view class="">x</view>
  </view>
  <view class="leftMouds" wx:else="">
    <view bind:tap="back" class="icon2">
      <image src="/static/icon/backArr.png" mode="" />
    </view>
  </view>
  <!-- 数组，标题 -->
  <view class="" style="margin: auto;" wx:if="{{isTitle}}">{{isTitle}}</view>
  <view class="list" wx:else="" >
    <view bind:tap="toggele" data-id="{{item.id}}" class="item {{item.id==activeId ?'itemActive' :''}}" wx:for="{{list}}">
      {{item.name}}
    </view>
  </view>
  <!-- 右侧模块 -->
  <view class="{{isIcon ?'rightMoudls' :''}}" wx:if="{{isRight}}">
  <!-- 显示图标 -->
    <view wx:if="{{isIcon}}" bind:tap="showCalendar" class="icon2">
      <image src="{{IconPath}}" mode="" />
    </view>
    <!-- 显示文本 -->
    <view wx:else="" bind:tap="sendText">{{rightText}}</view>
  </view>

</view>