// api/user/index.js
import request from '../request'

// 获取用户信息
const getUserInfo = () => {
  return request({
    url: 'user/index',
    method: 'POST'
  })
}

// 更新用户信息
const updateUserInfo = (data) => {
  return request({
    url: 'user/profile',
    method: 'POST',
    data
  })
}

// 更新用户头像
const updateAvatar = (data) => {
  return request({
    url: 'user/avatar',
    method: 'POST',
    data
  })
}
//购物清单列表

const shoppinglist = (data) => {
  return request({
    url: 'shopping/list_shopping',
    method: 'POST',
    data
  })
}

const detaillist = (data) => {
  return request({
    url: 'shopping/detail_list',
    method: 'POST',
    data
  })
}

const listgoods = (data) => {
  return request({
    url: 'shopping/list_goods',
    method: 'POST',
    data
  })
}

const addlist = (data) => {
  return request({
    url: 'shopping/add_list',
    method: 'POST',
    data
  })
}
const editlist = (data) => {
  return request({
    url: 'shopping/edit_list',
    method: 'POST',
    data
  })
}

export { getUserInfo, updateUserInfo, updateAvatar, shoppinglist, detaillist, listgoods, addlist, editlist }
