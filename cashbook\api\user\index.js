// api/user/index.js
import request from '../request';

// 获取用户信息
const getUserInfo = () => {
  return request({
    url: 'user/index',
    method: 'POST'
  });
};

// 更新用户信息
const updateUserInfo = (data) => {
  return request({
    url: 'user/profile',
    method: 'POST',
    data
  });
};

// 更新用户头像
const updateAvatar = (data) => {
  return request({
    url: 'user/avatar',
    method: 'POST',
    data
  });
};

export {
  getUserInfo,
  updateUserInfo,
  updateAvatar
};
