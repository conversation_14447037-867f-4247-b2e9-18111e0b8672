Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 资产信息
    asset: {
      type: Object,
      value: {
        id: '',
        icon: '/assets/icons/bank/default.png',
        name: '未命名银行',
        type: '信用卡',
        cardNumber: '',
        balance: '0.00',
        availableLimit: '0.00',
        billDay: '1',
        nextBillMonth: '1',
        billStatus: '',
        daysToNextBill: '0',
        repaymentDay: '1',
        nextRepaymentMonth: '1',
        daysToRepayment: '0'
      }
    },
    // 进度条宽度百分比
    progressWidth: {
      type: Number,
      value: 0
    },
    // 主题颜色
    themeColor: {
      type: String,
      value: '#8dc63f'
    },
    // 是否显示还款按钮
    showRepayButton: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {},

  /**
   * 组件的方法列表
   */
  methods: {
    // 编辑资产
    editAsset: function () {
      this.triggerEvent('edit', { assetId: this.properties.asset.id })
    },
    // 还款
    repay: function () {
      this.triggerEvent('repay', { assetId: this.properties.asset.id })
    }
  }
})
