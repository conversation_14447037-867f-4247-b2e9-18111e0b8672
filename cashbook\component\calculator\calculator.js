// component/calculator/calculator.js
const app = getApp();

Component({

  /**
   * 组件的属性列表
   */
  properties: {
    // 当前选中的tab ID
    currentTab: {
      type: Number,
      value: 1, // 默认为"支出"
      observer: function(newVal) {
        // 当tab变化时，更新scrollTap2的选项
        this.updateScrollTapOptions(newVal);
      }
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    list: [
      '1', '2', '3', '删除', '4', '5', '6', '-', '7', '8', '9', '+', '在记', '0', '.', '保存'
    ],
    result: '0.00',
    message: '',
    clikcList: [],
    // 标记是否刚完成计算，用于处理连续计算的问题
    justCalculated: false,
    // 主题颜色
    themeColor: '',
    // 计算表达式 - 用于显示计算过程
    expression: '',
    // 存储实际计算值
    currentValue: 0,
    // 当前运算符
    currentOperator: '',
    // 是否开始输入新数字
    startNewNumber: true,
    // 输入容器样式
    inputContainerStyle: '',
    // 结果容器样式
    resultContainerStyle: '',
    // 图片按钮样式
    photoStyle: '',
    // 输入容器可见性
    inputVisible: true,
    // 图片文字可见性
    photoTextVisible: true,
    // 输入框的值
    inputValue: '',
    // 结果文本颜色
    resultTextColor: '#FF6B6B', // 默认为支出的红色
    sclist:[
      {id:1,name:'账户',icon:'/static/icon/card.png'},
      {id:2,name:'报销',icon:'/static/icon/card.png'},
      {id:3,name:'4月21日',icon:'/static/icon/card.png'},
      {id:4,name:'优惠',icon:'/static/icon/card.png'},
      {id:5,name:'不计入',icon:'/static/icon/card.png'},
      {id:7,name:'优惠',icon:'/static/icon/card.png'},
      {id:6,name:'不计入',icon:'/static/icon/card.png'},
    ],
  },

  pageLifetimes: {
    show: function () {
      // 页面显示时的逻辑
      // 获取全局主题颜色
      if (app && app.globalData && app.globalData.selectedColor) {
        this.setData({
          themeColor: app.globalData.selectedColor
        });
      }
    }
  },

  lifetimes: {
    attached: function() {
      // 在组件实例进入页面节点树时执行
      // 获取全局主题颜色
      if (app && app.globalData && app.globalData.selectedColor) {
        this.setData({
          themeColor: app.globalData.selectedColor
        });
      }

      // 初始化scrollTap2选项和结果文本颜色
      this.updateScrollTapOptions(this.properties.currentTab);

      // 监听表达式变化，动态调整布局
      this.observer = this.createIntersectionObserver({
        thresholds: [0, 0.5, 1]
      });

      // 延迟执行，确保组件已渲染
      setTimeout(() => {
        this.adjustLayout();
      }, 100);
    },

    detached: function() {
      // 组件销毁时，清理监听器
      if (this.observer) {
        this.observer.disconnect();
      }
    }
  },
  methods: {
    // 更新scrollTap2选项
    updateScrollTapOptions: function(tabId) {
      // 根据当前选中的tab更新scrollTap2的选项和结果文本颜色
      let newOptions = [];
      let newColor = '';

      if (tabId === 2) { // 收入
        // 只显示"账户"、"日期"和"不计入"这三个选项
        newOptions = [
          {id: 1, name: '账户', icon: '/static/icon/card.png'},
          {id: 3, name: '4月21日', icon: '/static/icon/card.png'},
          {id: 5, name: '不计入', icon: '/static/icon/card.png'}
        ];
        // 收入使用绿色
        newColor = '#4CAF50'; // 绿色
      } else if (tabId === 3 || tabId === 4) { // 转账或借还
        // 只显示"日期"选项
        newOptions = [
          {id: 3, name: '4月21日', icon: '/static/icon/card.png'}
        ];
        // 转账和借还使用蓝色
        newColor = '#000'; // 蓝色
      } else { // 支出或其他
        // 显示所有选项
        newOptions = [
          {id: 1, name: '账户', icon: '/static/icon/card.png'},
          {id: 2, name: '报销', icon: '/static/icon/card.png'},
          {id: 3, name: '4月21日', icon: '/static/icon/card.png'},
          {id: 4, name: '优惠', icon: '/static/icon/card.png'},
          {id: 5, name: '不计入', icon: '/static/icon/card.png'},
          {id: 7, name: '优惠', icon: '/static/icon/card.png'},
          {id: 6, name: '不计入', icon: '/static/icon/card.png'}
        ];
        // 支出使用红色
        newColor = '#FF6B6B'; // 红色
      }

      // 更新数据
      this.setData({
        sclist: newOptions,
        resultTextColor: newColor
      });
    },

    // 动态调整布局
    adjustLayout: function() {
      // 获取当前表达式长度
      const exprLength = this.data.expression ? this.data.expression.length : 0;

      // 根据表达式长度动态调整布局
      if (exprLength > 6) { // 降低阈值，更早开始调整布局
        // 如果表达式较长，增加结果容器宽度
        wx.createSelectorQuery().in(this)
          .select('.header')
          .boundingClientRect(headerRect => {
            if (headerRect) {
              // 计算需要的宽度比例 - 考虑到货币符号的宽度和padding
              // 每个字符大约需要9px宽度，加上货币符号的宽度
              const currencyWidth = 20; // 估计的货币符号宽度
              const textWidth = exprLength * 9; // 估计的文本宽度
              const paddingWidth = 20; // 右侧padding和其他边距
              const totalNeededWidth = currencyWidth + textWidth + paddingWidth;

              // 计算header总宽度
              const headerWidth = headerRect.width;

              // 确保结果容器不超过可用宽度的限制
              // 保留最小宽度给图片按钮
              const photoMinWidth = 40; // 图片按钮最小宽度
              const maxResultWidth = headerWidth - photoMinWidth - 10; // 10px是边距

              // 限制结果容器宽度
              const resultWidth = Math.min(maxResultWidth, totalNeededWidth);

              // 计算剩余宽度 (注释掉未使用的变量)
              // const remainingWidth = headerWidth - resultWidth - photoMinWidth - 10;

              // 根据剩余宽度决定输入框和图片按钮的显示状态
              let inputStyle = '';
              let photoStyle = '';
              let inputVisible = true;
              let photoTextVisible = true;

              // 图片按钮处理
              if (resultWidth > headerWidth * 0.6) {
                // 如果结果容器占用了大部分空间，只显示图标不显示文字
                photoStyle = 'min-width: 40px; max-width: 40px; justify-content: center; flex: 0 0 40px; background-color: #f5f5f5; border-radius: 4px; padding: 0 5px;';
                photoTextVisible = false;
              } else {
                // 正常显示图片按钮
                photoStyle = 'min-width: 40px; flex: 0 0 60px; background-color: #f5f5f5; border-radius: 4px; padding: 0 5px;';
              }

              // 输入框处理
              // if (remainingWidth < 100) {
              //   // 如果剩余宽度很小，隐藏输入框
              //   inputStyle = 'width: 0; opacity: 0; overflow: hidden; flex: 0 0 0;';
              //   inputVisible = false;
              // } else if (remainingWidth < 150) {
              //   // 如果剩余宽度较小，压缩输入框但保持最小宽度
              //   inputStyle = `min-width: 150px; flex: 1 1 auto; opacity: 0.9;`;
              // } else {
              //   // 正常显示输入框
              //   inputStyle = `min-width: 150px; flex: 1 1 auto;`;
              // }

              // 结果容器样式 - 确保不会被截断
              const resultStyle = `flex: 0 0 ${resultWidth}px; min-width: ${resultWidth}px; max-width: ${resultWidth}px; justify-content: flex-end;`;

              // 更新样式
              this.setData({
                resultContainerStyle: resultStyle,
                inputContainerStyle: inputStyle,
                photoStyle: photoStyle,
                inputVisible: inputVisible,
                photoTextVisible: photoTextVisible
              });
            }
          }).exec();
      } else {
        // 重置样式，但保持最小宽度
        this.setData({
          resultContainerStyle: 'flex: 0 0 auto; min-width: 80px; justify-content: flex-end;',
          inputContainerStyle: 'flex: 1 1 auto; min-width: 150px;',
          photoStyle: 'min-width: 40px; flex: 0 0 45px; background-color: #f4f4f4; border-radius: 4px; padding: 0 5px;',
          inputVisible: true,
          photoTextVisible: true
        });
      }
    },
    // 处理输入框输入事件
    handleInput(e) {
      // 获取输入的值
      const value = e.detail.value;

      // 更新输入框的值
      this.setData({
        inputValue: value
      });

      // 如果需要，可以在这里添加其他逻辑，比如字符计数显示等

      // 返回输入的值，确保输入框正常工作
      return value;
    },

    getNum(e) {
      const res = e.currentTarget.dataset.info;

      // 处理删除按钮
      if (res === '删除') {
        // 如果表达式为空，不做任何操作
        if (!this.data.expression) return;

        // 删除表达式的最后一个字符
        let expr = this.data.expression;
        // 如果最后一个字符是运算符，需要同时更新currentOperator
        const lastChar = expr.charAt(expr.length - 1);
        if (lastChar === '+' || lastChar === '-') {
          this.setData({
            currentOperator: ''
          });
        }

        expr = expr.slice(0, -1);

        // 如果表达式为空，重置所有状态
        if (!expr) {
          this.setData({
            expression: '',
            result: '0',
            currentValue: 0,
            startNewNumber: true
          });
          return;
        }

        this.setData({
          expression: expr
        }, () => {
          // 表达式更新后调整布局
          this.adjustLayout();
        });
        return;
      }

      // 处理运算符
      if (res === '+' || res === '-') {
        // 如果表达式为空或以运算符结尾，不添加新运算符
        if (!this.data.expression || /[+\-]$/.test(this.data.expression)) {
          return;
        }

        // 如果刚完成计算，直接使用结果继续计算
        if (this.data.justCalculated) {
          this.setData({
            currentOperator: res,
            expression: this.data.expression + res,
            startNewNumber: true,
            justCalculated: false
          }, () => {
            // 表达式更新后调整布局
            this.adjustLayout();
          });

          // 修改保存按钮为等号
          var newlist = [...this.data.list];
          newlist[15] = '=';
          this.setData({
            list: newlist
          });

          return;
        }

        // 如果已经有运算符，先计算前面的结果
        if (this.data.currentOperator) {
          const currentExpr = this.data.expression;
          const parts = currentExpr.split(/[+\-]/);
          const lastNumber = parseFloat(parts[parts.length - 1]);

          let newValue = this.data.currentValue;
          if (this.data.currentOperator === '+') {
            newValue += lastNumber;
          } else if (this.data.currentOperator === '-') {
            newValue -= lastNumber;
          }

          // 更新当前值
          this.setData({
            currentValue: newValue
          });
        } else {
          // 第一次输入运算符，将当前表达式的值设为currentValue
          this.setData({
            currentValue: parseFloat(this.data.expression)
          });
        }

        // 更新运算符和表达式
        this.setData({
          currentOperator: res,
          expression: this.data.expression + res,
          startNewNumber: true
        }, () => {
          // 表达式更新后调整布局
          this.adjustLayout();
        });

        // 修改保存按钮为等号
        var newlist = [...this.data.list];
        newlist[15] = '=';
        this.setData({
          list: newlist
        });

        return;
      }

      // 处理等号计算
      if (res === '=') {
        // 如果表达式为空或不包含运算符，不做任何操作
        if (!this.data.expression || !/[+\-]/.test(this.data.expression)) {
          return;
        }

        try {
          // 获取最后一个数字
          const parts = this.data.expression.split(/[+\-]/);
          const lastNumber = parseFloat(parts[parts.length - 1] || 0);

          // 计算结果
          let result = this.data.currentValue;
          if (this.data.currentOperator === '+') {
            result += lastNumber;
          } else if (this.data.currentOperator === '-') {
            result -= lastNumber;
          }

          // 格式化结果，最多保留2位小数
          const formattedResult = parseFloat(result.toFixed(2)).toString();

          // 更新状态
          this.setData({
            result: formattedResult,
            expression: formattedResult,
            currentValue: result,
            currentOperator: '',
            justCalculated: true,
            startNewNumber: true
          }, () => {
            // 表达式更新后调整布局
            this.adjustLayout();
          });

          // 修改等号为保存
          var newlist = [...this.data.list];
          newlist[15] = '保存';
          this.setData({
            list: newlist
          });
        } catch (error) {
          console.error('计算错误:', error);
          this.setData({
            result: '0',
            expression: '',
            currentValue: 0,
            currentOperator: '',
            startNewNumber: true
          });
        }
        return;
      }

      // 处理保存按钮
      if (res === '保存') {
        // 这里可以添加保存逻辑
        // wx.navigateBack();
        return;
      }

      // 处理"在记"按钮
      if (res === '在记') {
        this.setData({
          result: '0',
          expression: '',
          currentValue: 0,
          currentOperator: '',
          justCalculated: false,
          startNewNumber: true
        }, () => {
          // 表达式更新后调整布局
          this.adjustLayout();
        });
        return;
      }

      // 处理数字和小数点输入
      if (/^\d$|^\.$/.test(res)) {
        // 如果刚完成计算且输入数字，清空之前的结果
        if (this.data.justCalculated) {
          this.setData({
            expression: res,
            result: res,
            justCalculated: false,
            startNewNumber: false
          }, () => {
            // 表达式更新后调整布局
            this.adjustLayout();
          });
          return;
        }

        // 如果需要开始新数字（通常是在输入运算符后）
        if (this.data.startNewNumber) {
          this.setData({
            expression: this.data.expression + res,
            startNewNumber: false
          }, () => {
            // 表达式更新后调整布局
            this.adjustLayout();
          });
          return;
        }

        // 如果当前表达式为0且输入数字，则替换0
        if (this.data.expression === '0' && res !== '.') {
          this.setData({
            expression: res
          }, () => {
            // 表达式更新后调整布局
            this.adjustLayout();
          });
          return;
        }

        // 如果输入小数点，检查当前数字是否已有小数点
        if (res === '.') {
          // 获取当前正在输入的数字
          const parts = this.data.expression.split(/[+\-]/);
          const currentNumber = parts[parts.length - 1];

          if (currentNumber.includes('.')) {
            return; // 已有小数点，忽略
          }
        }

        // 正常添加数字或小数点
        this.setData({
          expression: this.data.expression + res
        }, () => {
          // 表达式更新后调整布局
          this.adjustLayout();
        });
      }
    }
  }
})