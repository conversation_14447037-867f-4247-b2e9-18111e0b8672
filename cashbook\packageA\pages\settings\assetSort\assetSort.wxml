<!-- 资产排序页面 -->
<view class="asset-sort-container">
  <!-- 提示文本 -->
  <view class="sort-tip">长按拖动排序哦</view>

  <!-- 资产列表 -->
  <view class="asset-list">
    <movable-area>
      <movable-view
        wx:for="{{assetList}}"
        wx:key="id"
        direction="vertical"
        inertia="true"
        data-index="{{index}}"
        bindlongpress="onLongPress"
        bindtouchend="onTouchEnd"
        class="movable-item"
      >
        <view class="asset-item">
          <!-- 左侧图标 -->
          <view class="asset-icon">
            <block wx:if="{{item.type === 'credit'}}">
              <van-icon name="card" size="24px" color="#ff4d4f" />
            </block>
            <block wx:elif="{{item.type === 'debit'}}">
              <van-icon name="card" size="24px" color="#52c41a" />
            </block>
            <block wx:elif="{{item.type === 'online' && item.name === '支付宝'}}">
              <van-icon name="alipay" size="24px" color="#1677ff" />
            </block>
            <block wx:elif="{{item.type === 'online' && item.name === '微信钱包'}}">
              <van-icon name="wechat" size="24px" color="#07c160" />
            </block>
            <block wx:elif="{{item.type === 'person'}}">
              <van-icon name="contact" size="24px" color="#fa8c16" />
            </block>
            <block wx:elif="{{item.name === '花呗'}}">
              <van-icon name="shop" size="24px" color="#1890ff" />
            </block>
            <block wx:elif="{{item.name === '白条'}}">
              <van-icon name="shop" size="24px" color="#eb2f96" />
            </block>
            <block wx:elif="{{item.name === '农业'}}">
              <van-icon name="gold-coin" size="24px" color="#52c41a" />
            </block>
            <block wx:elif="{{item.name === '邮政'}}">
              <van-icon name="newspaper-o" size="24px" color="#52c41a" />
            </block>
            <block wx:elif="{{item.name === '报销'}}">
              <van-icon name="balance-o" size="24px" color="#52c41a" />
            </block>
            <block wx:else>
              <van-icon name="balance-o" size="24px" />
            </block>
          </view>

          <!-- 中间信息 -->
          <view class="asset-info">
            <view class="asset-name">{{item.name}}</view>
            <view class="asset-type" wx:if="{{item.subType}}">
              <text class="asset-tag {{item.type}}">{{item.subType}}</text>
              <text wx:if="{{item.bankName}}"> {{item.bankName}} {{item.cardNumber}}</text>
            </view>
          </view>

          <!-- 右侧金额 -->
          <view class="asset-amount {{item.isNegative ? 'negative' : ''}}">
            <text>{{item.isNegative ? '-' : ''}}¥{{item.amount}}</text>

            <!-- 还款日标签 -->
            <view class="repayment-tag" wx:if="{{item.repaymentDate && item.isNegative}}" style="background-color: {{themeColor}};">
              还款日:{{item.repaymentDate}}
            </view>

            <!-- 可用额度标签 -->
            <view class="available-tag" wx:if="{{item.availableLimit && item.isNegative}}">
              可用:{{item.availableLimit}}
            </view>
          </view>
        </view>
      </movable-view>
    </movable-area>
  </view>

  <!-- 底部保存按钮 -->
  <view class="save-button" bindtap="saveSort" style="background-color: {{themeColor}};">
    保存
  </view>
</view>
