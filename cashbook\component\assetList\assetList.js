Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示账户金额
    showAmount: {
      type: Boolean,
      value: true
    },
    // 资产列表数据
    assetList: {
      type: Array,
      value: []
    },
    // 主题颜色
    themeColor: {
      type: String,
      value: '#8dc63f'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    selectedColor: '#8dc63f', // 默认颜色

    // 删除资产弹层相关
    deletePopupVisible: false,
    currentAssetId: '',
    deleteLoading: false,

    // 两个确认弹窗的显示状态
    deleteWithBillConfirmVisible: false,
    deleteOnlyConfirmVisible: false,

    // 资产设置弹层相关
    settingsPopupVisible: false,
    assetStatisticsSwitch: true, // 资产统计显示开关状态
    assetDisplayMode: '平铺' // 资产展示模式
  },

  lifetimes: {
    attached: function () {
      // 获取全局颜色
      const app = getApp()
      if (app && app.globalData && app.globalData.selectedColor) {
        this.setData({
          selectedColor: app.globalData.selectedColor
        })
      }

      // 获取存储的设置
      const assetStatisticsSwitch = wx.getStorageSync('assetStatisticsSwitch')
      const assetDisplayMode = wx.getStorageSync('assetDisplayMode')

      this.setData({
        assetStatisticsSwitch: assetStatisticsSwitch !== '' ? assetStatisticsSwitch : true,
        assetDisplayMode: assetDisplayMode || '平铺'
      })
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 点击资产项
    onAssetItemTap(e) {
      const assetId = e.currentTarget.dataset.id
      const assetType = e.currentTarget.dataset.type

      // 触发点击事件，传递给父组件
      this.triggerEvent('assetclick', { id: assetId, type: assetType })

      // 直接跳转到资产详情页面
      wx.navigateTo({
        url: `/packageA/pages/settings/assetDetail/assetDetail?id=${assetId}`
      })
    },

    // 点击添加资产
    onAddAssetTap() {
      this.triggerEvent('addasset')
      wx.navigateTo({
        url: '/packageA/pages/settings/createAccount/createAccount'
      })
    },

    // 点击设置按钮
    onSettingTap() {
      // 显示设置弹层
      this.setData({
        settingsPopupVisible: true
      })
    },

    // 关闭设置弹层
    closeSettingsPopup() {
      this.setData({
        settingsPopupVisible: false
      })
    },

    // 跳转到资产管理页面
    navigateToAssetManagement() {
      this.closeSettingsPopup()
      wx.navigateTo({
        url: '/packageA/pages/settings/assetManagement/assetManagement'
      })
    },

    // 跳转到资产排序页面
    navigateToAssetSort() {
      this.closeSettingsPopup()
      wx.navigateTo({
        url: '/packageA/pages/settings/assetSort/assetSort'
      })
    },

    // 跳转到资产变动页面
    navigateToAssetChanges() {
      this.closeSettingsPopup()
      wx.navigateTo({
        url: '/packageA/pages/settings/assetChanges/assetChanges'
      })
    },

    // 切换资产展示模式
    toggleAssetDisplayMode() {
      const newMode = this.data.assetDisplayMode === '平铺' ? '分组' : '平铺'
      this.setData({
        assetDisplayMode: newMode
      })
      wx.setStorageSync('assetDisplayMode', newMode)
      this.triggerEvent('displaymodechange', { mode: newMode })
    },

    // 切换资产统计显示
    toggleAssetStatistics(e) {
      const switchState = e.detail.value
      this.setData({
        assetStatisticsSwitch: switchState
      })
      wx.setStorageSync('assetStatisticsSwitch', switchState)
      this.triggerEvent('statisticschange', { enabled: switchState })
    },

    // 查看隐藏资产
    viewHiddenAssets() {
      // 触发事件通知父组件
      this.triggerEvent('viewhidden')
      // 导航到隐藏资产页面
      wx.navigateTo({
        url: '/packageA/pages/settings/hiddenAssets/hiddenAssets'
      })
    },

    // 滑动单元格点击事件
    onSwipeCellClick(e) {
      const { position, name } = e.detail

      if (position === 'left') {
        // 点击了"隐藏"按钮
        this.hideAsset(name)
      }
    },

    // 隐藏资产
    hideAsset(e) {
      const id = typeof e === 'object' ? e.currentTarget.dataset.id : e

      wx.showModal({
        title: '确认隐藏',
        content: '确定要隐藏该资产吗？',
        success: (res) => {
          if (res.confirm) {
            // 触发隐藏事件通知父组件
            this.triggerEvent('hideasset', { id })
            wx.showToast({
              title: '已隐藏',
              icon: 'success'
            })
          }
        }
      })
    },

    // 编辑资产
    onEditAsset(e) {
      const assetId = e.currentTarget.dataset.id
      this.triggerEvent('editasset', { id: assetId })
      wx.navigateTo({
        url: `/packageA/pages/settings/createAccount/createAccount?id=${assetId}&edit=true`
      })
    },

    // 删除资产
    onDeleteAsset(e) {
      const assetId = e.currentTarget.dataset.id

      // 设置当前操作的资产ID并显示删除弹层
      this.setData({
        currentAssetId: assetId,
        deletePopupVisible: true
      })
    },

    // 关闭删除弹层
    closeDeletePopup() {
      this.setData({
        deletePopupVisible: false
      })
    },

    // 显示删除资产和账单确认弹窗
    showDeleteWithBillConfirm() {
      this.setData({
        deletePopupVisible: false,
        deleteWithBillConfirmVisible: true
      })
    },

    // 关闭删除资产和账单确认弹窗
    closeDeleteWithBillConfirm() {
      this.setData({
        deleteWithBillConfirmVisible: false
      })
    },

    // 显示仅删除资产确认弹窗
    showDeleteOnlyConfirm() {
      this.setData({
        deletePopupVisible: false,
        deleteOnlyConfirmVisible: true
      })
    },

    // 关闭仅删除资产确认弹窗
    closeDeleteOnlyConfirm() {
      this.setData({
        deleteOnlyConfirmVisible: false
      })
    },

    // 删除资产和账单
    deleteAssetAndBill() {
      this.deleteAsset(1)
    },

    // 仅删除资产
    deleteAssetOnly() {
      this.deleteAsset(0)
    },

    // 删除资产
    deleteAsset(delBill) {
      const { currentAssetId } = this.data

      if (!currentAssetId) {
        wx.showToast({
          title: '账户ID不能为空',
          icon: 'none'
        })
        return
      }

      this.setData({ deleteLoading: true })

      // 导入API
      const { deleteAccount } = require('../../api/account/index')

      const params = {
        user_account_id: currentAssetId,
        del_bill: delBill
      }

      deleteAccount(params)
        .then((res) => {
          this.setData({
            deleteLoading: false,
            deleteWithBillConfirmVisible: false,
            deleteOnlyConfirmVisible: false
          })

          if (res.code === 0) {
            wx.showToast({
              title: res.msg || '删除成功',
              icon: 'success'
            })

            // 触发删除成功事件通知父组件
            this.triggerEvent('deleteasset', { id: currentAssetId })
          } else {
            wx.showToast({
              title: res.msg || '删除失败',
              icon: 'none'
            })
          }
        })
        .catch((err) => {
          this.setData({
            deleteLoading: false,
            deleteWithBillConfirmVisible: false,
            deleteOnlyConfirmVisible: false
          })

          wx.showToast({
            title: err.msg || '网络错误',
            icon: 'none'
          })
        })
    }
  }
})
