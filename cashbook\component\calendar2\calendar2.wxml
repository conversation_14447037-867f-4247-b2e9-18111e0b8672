<view class="calendar-container" wx:if="{{isShow}}">
<view class="mycard">

<!-- header -->
   <view class="dialogheader"  style="background-color: #fbfbfb;padding: 10px 0 ;">
      <view class="close" bind:tap="closeDialog">X</view>
      <view style="text-align: center;">未来计划</view>
   </view>
  <!-- 显示当前年月 -->
  <view class="calendar-header" style="text-align: left;margin: 10px 0 10px 10px;">
    <view>{{year}}年{{month}}月</view>
  </view>
  <!-- 显示周一到周日 -->
  <view class="weekdays">
    <view wx:for="{{weekdays}}" wx:key="index">{{item}}</view>
  </view>
  <!-- 显示日历天数 -->
  <view
    class="days"
    bindtouchstart="handleTouchStart"
    bindtouchmove="handleTouchMove"
    bindtouchend="handleTouchEnd"
    animation="{{animationData}}"
  >
    <view
      wx:for="{{calendarData}}"
      wx:key="id"
      class="{{item.isCurrentMonth? 'current-month' : 'other-month'}}"
    >
      {{item.day}}
    </view>
  </view>

</view>
</view>
<view class="mask" wx:if="{{isShow}}" bind:tap="closeDialog"></view>