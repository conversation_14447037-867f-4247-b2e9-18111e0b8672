<view class="month-picker-container {{animationClass}}" wx:if="{{animationVisible}}">
  <view class="month-picker-mask" bindtap="onCancel"></view>
  <view class="month-picker-content">
    <!-- 顶部年份选择区域 -->
    <view class="year-selector" bindtouchstart="touchStart" bindtouchmove="touchMove" bindtouchend="touchEnd">
      <view class="current-year" wx:if="{{mode === 'month'}}">{{currentYear}}年{{currentMonth}}月</view>
      <view class="current-year" wx:else>{{currentYear}}年</view>
      <view class="year-display">
        <view class="year">{{currentYear}}年</view>
        <view class="year-controls">
          <view class="year-control up" bindtap="prevYear">
            <t-icon name="chevron-up"></t-icon>
          </view>
          <view class="year-control down" bindtap="nextYear">
            <t-icon name="chevron-down"></t-icon>
          </view>
        </view>
      </view>
    </view>
    <!-- 月份网格作为垂直滑动的swiper (只在month模式显示) -->
    <swiper wx:if="{{mode === 'month'}}" class="month-swiper" vertical="true" duration="300" current="{{yearSwiperIndex}}" display-multiple-items="1" skip-hidden-item-layout="true" easing-function="easeInOutCubic" bindchange="onSwiperChange">
      <!-- 动态生成年份对应的swiper-item -->
      <swiper-item wx:for="{{yearItems}}" wx:key="year">
        <!-- <view class="year-label">{{item.year}}年</view> -->
        <view class="month-grid">
          <!-- 月份网格 - 使用预先计算的monthItems数组 -->
          <view wx:for="{{item.months}}" wx:for-item="month" wx:key="month" class="month-item {{month.active ? 'active' : ''}} {{month.disabled ? 'disabled' : ''}}" data-year="{{item.year}}" data-month="{{month.month}}" bindtap="selectMonth">
            {{month.month}}月
          </view>
        </view>
      </swiper-item>
    </swiper>
    
    <!-- 年份选择器 (只在year模式显示) -->
    <view wx:if="{{mode === 'year'}}" class="year-only-selector">
      <view class="year-option-list">
        <view 
          wx:for="{{yearItems}}" 
          wx:key="year" 
          class="year-option {{item.year === currentYear ? 'active' : ''}}"
          data-year="{{item.year}}"
          bindtap="selectYear"
        >
          {{item.year}}
        </view>
      </view>
    </view>
    
    <!-- 底部按钮区域 -->
    <view class="picker-actions">
      <view class="action-btn cancel" bindtap="onCancel">取消</view>
      <view class="action-btn confirm" bindtap="onConfirm">确定</view>
    </view>
  </view>
</view>