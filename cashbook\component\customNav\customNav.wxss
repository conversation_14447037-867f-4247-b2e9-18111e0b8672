.box {
  position: fixed;
  top: 0;
  left: 0;
  padding: 10px;
  box-sizing: border-box;
  /* border: 1px solid; */
  width: 100%;
  display: grid;
  grid-template-columns: auto 1fr auto;
  align-items: center;
  gap: 10px;
  background-color: #fbfbfb;
  z-index: 999;
  padding: 60px 10px 10px 10px;
  /* border: 1px solid blue; */

}
.box .list{
   place-self: center;

}
.leftMouds{
  /* border: 1px solid; */
}
.rightMoudls{
  width: 40px;
  height: 40px;
  display: grid;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #fff;
  box-shadow: 2px 2px 1px 1px  rgba(177, 177, 177, .6);
}
.list {
  display: grid;
  grid-template-columns: repeat(3, auto);
  gap: 5px;
  /* width: fit-content; */
  margin: auto;
}

/* .list view:nth-child(1){
  color: red;
}
.list view:nth-child(2){
  color: blue;
} */
.item {
  padding: 3px 8px;
  border-radius: 15px;
  text-align: center;
}

.itemActive {
  background-color: #404655;
  color: white;
}
.icon2{

}
.icon2 image {
  width: 20px;
  height: 20px;
  object-fit: cover;
  transform: translateY(4px);
}