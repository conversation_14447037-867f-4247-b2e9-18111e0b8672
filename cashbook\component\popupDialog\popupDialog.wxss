/* components/popupDialog/popupDialog.wxss */
.popup-dialog-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  pointer-events: none;
}

.popup-dialog-container.visible {
  pointer-events: auto;
}

.popup-dialog-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.popup-dialog-container.visible .popup-dialog-mask {
  opacity: 1;
}

.popup-dialog-content {
  position: absolute;
  background-color: #fff;
  width: calc(100% - 60rpx);
  margin: 0 30rpx;
  z-index: 10000;
  transform: translateY(100%);
  transition: transform 0.35s cubic-bezier(0.33, 0.66, 0.66, 1);
  will-change: transform;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.popup-dialog-container.visible .popup-dialog-content {
  transform: translateY(0);
}

.popup-dialog-round {
  border-radius: 35px;
  overflow: hidden;
}

.popup-dialog-bottom {
  bottom: 15px;
  left: 0;
  right: 0;
}

.popup-dialog-center {
  top: 50%;
  left: 0;
  right: 0;
  transform: translate(0, -50%) scale(0.85);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.popup-dialog-container.visible .popup-dialog-center {
  transform: translate(0, -50%) scale(1);
  opacity: 1;
}

.popup-dialog-top {
  top: 0;
  left: 0;
  right: 0;
  transform: translateY(-100%);
}

.popup-dialog-container.visible .popup-dialog-top {
  transform: translateY(0);
}

.popup-dialog-header {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  padding: 34rpx 20rpx;
}

.reverse-header {
  flex-direction: row-reverse;
}

.popup-dialog-title {
  font-size: 34rpx;
  font-weight: 500;
  text-align: center;
}

.popup-dialog-close {
  position: absolute;
  right: 40rpx;
  top: 30rpx;
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #727272;
  background-color: #e1e1e1;
  border-radius: 50%;
}

.reverse-header .popup-dialog-close {
  position: absolute;
  left: 40rpx;
  right: auto;
}

.popup-dialog-body {
  overflow-y: auto;
  padding: 0 20rpx 30rpx;
  flex: 1;
}

/* 操作按钮区域 */
.popup-dialog-actions {
  padding: 10rpx 30rpx 20rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 底部按钮样式 */
.popup-dialog-footer {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.popup-dialog-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 30rpx;
  margin: 0 15rpx;
}

.popup-dialog-cancel-btn {
  background-color: #f2f2f2;
  color: #333333;
}

.popup-dialog-confirm-btn {
  background-color: #4285f4;
  color: white;
}

/* 分类选项列表样式 */
.more-menu-list {
  max-height: calc(70vh - 100rpx);
  overflow-y: auto;
}

.more-menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1px solid #eee;
}

.more-menu-item:last-child {
  border-bottom: none;
  margin-bottom: 20rpx;
}

.menu-item-icon {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  margin-right: 20rpx;
  font-size: 40rpx;
}

.menu-item-icon image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.menu-item-content {
  flex: 1;
}

.menu-item-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 6rpx;
}

.menu-item-desc {
  font-size: 24rpx;
  color: #999;
}

.menu-item-arrow {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ccc;
} 