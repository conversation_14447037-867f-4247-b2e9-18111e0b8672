<!-- WXS 格式化工具模块 -->
<wxs module="utils">
function formatPercentage(remaining, total) {
  if (!total) return "0";
  var percentage = remaining / total * 100;
  // 保留一位小数
  return percentage.toFixed(1);
}

module.exports = {
  formatPercentage: formatPercentage
};
</wxs>
<van-popup
  show="{{ visible }}"
  position="bottom"
  custom-style="border-radius: 35px; margin: 0 30rpx 30rpx; width: calc(100% - 60rpx);"
  bind:close="onClose"
>
  <view class="share-container">
    <view class="share-header">
      <view class="share-title">分享给朋友</view>
      <van-icon name="cross" size="20px" class="close-btn {{buttonPosition === 'left' ? 'btn-left' : 'btn-right'}}" bind:tap="onClose" />
    </view>
    
    <view class="share-content">
      <view class="share-card">
        <view class="card-header" style="background-color: {{cardStyle.backgroundColor}}">
          <view class="frog-avatar">
            <image src="/static/images/frog.png" mode="aspectFit" />
          </view>
          <view class="account-header">
            <view class="account-name" style="color: {{cardStyle.textColor}};">小青账</view>
            <view class="account-desc" style="color: {{cardStyle.textColor}}; opacity: 0.9;">和小蛙一起管理预算</view>
          </view>
          <view class="frog-badge">
            <text style="color: {{cardStyle.textColor}};">小青账</text>
          </view>
        </view>
        
        <view class="card-content" style="background-color: {{cardStyle.backgroundColor}}">
          <view class="card-divider" style="color: {{cardStyle.textColor}};">···················· {{year}}-{{month}}预算 ····················</view>
          
          <view class="budget-info">
            <view class="budget-left">
              <view class="budget-title" style="color: {{cardStyle.textColor}};">剩余月预算（元）</view>
              <view class="budget-amount" style="color: {{cardStyle.textColor}};">{{remainingAmount}}</view>
            </view>
            <view class="budget-right">
              <view class="budget-circle-chart">
                <!-- Progress circle represents the remaining budget percentage -->
                <view class="circle-progress" style="background: conic-gradient({{cardStyle.textColor}} {{utils.formatPercentage(remainingAmount, budgetAmount)}}%, rgba(255,255,255,0.3) 0%);">
                  <view class="inner-circle" style="background-color: {{cardStyle.backgroundColor}};"></view>
                </view>
              </view>
            </view>
          </view>
          
          <view class="budget-stats">
            <view class="stat-row">
              <view class="stat-item">
                <view class="stat-label" style="color: {{cardStyle.textColor}};">本月消费</view>
                <view class="stat-value" style="color: {{cardStyle.textColor}};">¥ {{spentAmount}}</view>
              </view>
              <view class="stat-item">
                <view class="stat-label" style="color: {{cardStyle.textColor}};">月预算</view>
                <view class="stat-value" style="color: {{cardStyle.textColor}};">¥ {{budgetAmount}}</view>
              </view>
              <view class="stat-item">
                <view class="stat-label" style="color: {{cardStyle.textColor}};">预算剩余</view>
                <view class="stat-value" style="color: {{cardStyle.textColor}};">{{utils.formatPercentage(remainingAmount, budgetAmount)}}%</view>
              </view>
              <view class="stat-item">
                <view class="stat-label" style="color: {{cardStyle.textColor}};">剩余天数</view>
                <view class="stat-value" style="color: {{cardStyle.textColor}};">剩{{remainingDays}}天</view>
              </view>
            </view>
            
            <view class="stat-divider"></view>
            
            <view class="daily-stats">
              <view class="daily-item">
                <view class="daily-label" style="color: {{cardStyle.textColor}};">
                  <view class="dot orange" style="display:inline-block;"></view>
                  本月日均消费
                </view>
                <view class="daily-value" style="color: {{cardStyle.textColor}};">¥ {{dailySpent}}</view>
              </view>
              <view class="daily-item">
                <view class="daily-label" style="color: {{cardStyle.textColor}};">
                  <view class="dot purple" style="display:inline-block;"></view>
                  日均预算
                </view>
                <view class="daily-value" style="color: {{cardStyle.textColor}};">¥ {{dailyBudget}}</view>
              </view>
              <view class="daily-item">
                <view class="daily-label" style="color: {{cardStyle.textColor}};">
                  <view class="dot blue" style="display:inline-block;"></view>
                  剩余每日可消费金额
                </view>
                <view class="daily-value" style="color: {{cardStyle.textColor}};">¥ {{remainingDailyAmount}}</view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- Only show footer if format is not 'none' -->
        <view class="card-footer" style="background-color: {{cardStyle.backgroundColor}}; filter: brightness(0.85);" wx:if="{{cardFormat !== 'none'}}">
          <!-- Default frog style -->
          <view class="frog-brand" wx:if="{{cardFormat === 'frog'}}">
            <view class="frog-avatar-mini">
              <image src="/static/images/frog-mini.png" mode="aspectFit" class="frog-mini" />
            </view>
            <view class="brand-text">
              <view class="brand-name" style="color: {{cardStyle.textColor}};">小青账</view>
              <view class="brand-slogan" style="color: {{cardStyle.textColor}}; opacity: 0.7;">享受记账乐趣，为财务自由做好准备～</view>
            </view>
          </view>
          
          <!-- Personal avatar style -->
          <view class="user-brand" wx:elif="{{cardFormat === 'avatar'}}">
            <view class="user-info">
              <view class="user-avatar">
                <image src="/static/images/user-avatar.png" mode="aspectFit" class="avatar-image" />
              </view>
              <view class="user-details">
                <view class="user-name-wrapper">
                  <view class="user-name" style="color: {{cardStyle.textColor}};">西风.</view>
                  <view class="vip-badge">VIP</view>
                </view>
                <view class="user-record" style="color: {{cardStyle.textColor}}; opacity: 0.7;">今天是你记账的第27天啦 🎯</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <view class="action-buttons">
      <button class="action-btn change-color" bind:tap="onChangeColor">更换颜色</button>
      <button class="action-btn format" bind:tap="onChangeFormat">样式</button>
      <button class="action-btn save-image" bind:tap="onSaveImage">保存图片</button>
    </view>
  </view>
</van-popup> 

<!-- 样式选择弹窗 -->
<van-popup
  show="{{ formatPopupVisible }}"
  position="bottom"
  custom-style="border-radius: 35px; margin: 0 30rpx 30rpx; width: calc(100% - 60rpx);"
  bind:close="onCloseFormatPopup"
>
  <view class="format-popup">
    <view class="format-header">
      <view class="format-title">选择样式</view>
      <van-icon name="cross" size="20px" class="close-btn {{buttonPosition === 'left' ? 'btn-left' : 'btn-right'}}" bind:tap="onCloseFormatPopup" />
    </view>
    
    <view class="format-options">
      <view class="format-option" bindtap="selectFormat" data-format="frog">
        <view class="option-text">小青账</view>
        <van-icon name="arrow" />
      </view>
      <view class="format-option" bindtap="selectFormat" data-format="avatar">
        <view class="option-text">个人头像</view>
        <van-icon name="arrow" />
      </view>
      <view class="format-option" bindtap="selectFormat" data-format="none">
        <view class="option-text">无</view>
        <van-icon name="arrow" />
      </view>
    </view>
  </view>
</van-popup> 