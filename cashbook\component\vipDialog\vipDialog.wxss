
.box{
  position: fixed;
  bottom: 0;
  border-radius: 15px 15px 0 0 ;
  background-color: #fff;
  padding: 20px 30px;
  box-sizing: border-box;
  width: fit-content;
  margin: auto;
  text-align: center;
  z-index: 999;
  width: 100%;
}
.box view:nth-child(1){
  font-size: 38rpx;
  margin-bottom: 10px;
}
.box view:nth-child(2){
  font-size: 30rpx;
  margin-bottom: 30px;
}
.box view:nth-child(3){
 background: linear-gradient(to right,#d8e1ce,#c1d0af);
 text-align: center;
 border-radius: 15px;
 padding: 10px 0 ;
 margin: 0 30px ;
 font-weight: 700;
}

.mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99;
}