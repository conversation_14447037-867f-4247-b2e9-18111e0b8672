Component({
  /**
   * 组件的属性列表
   */
  properties: {
    show: {
      type: Boolean,
      value: false,
      observer: function (newVal) {
        if (newVal) {
          this.showPopup()
        } else {
          this.hidePopup()
        }
      }
    },
    title: {
      type: String,
      value: '提示'
    },
    content: {
      type: String,
      value: ''
    },
    cancelText: {
      type: String,
      value: '取消'
    },
    confirmText: {
      type: String,
      value: '确认'
    },
    maskClosable: {
      type: Boolean,
      value: true
    },
    titleSize: {
      type: String,
      value: '36rpx'
    },
    contentSize: {
      type: String,
      value: '30rpx'
    },
    closePosition: {
      type: String,
      value: 'right' // 'left' 或 'right'
    },
    closeButtonTop: {
      type: String,
      value: '20rpx'
    },
    closeIconSize: {
      type: String,
      value: '24rpx'
    },
    contentColor: {
      type: String,
      value: '#333' // 默认内容颜色
    },
    titleColor:{
      type: String,
      value: '#000' // 默认标题颜色
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    popupVisible: false,
    animating: false,
    closeTimer: null,
    themeColor: '#4fc08d' // 默认主题色
  },

  lifetimes: {
    attached: function () {
      // 从app.globalData获取主题色
      const app = getApp()
      if (app && app.globalData && app.globalData.selectedColor) {
        this.setData({
          themeColor: app.globalData.selectedColor
        })
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 显示弹窗
    showPopup() {
      // 清除可能存在的关闭计时器
      if (this.data.closeTimer) {
        clearTimeout(this.data.closeTimer)
        this.setData({ closeTimer: null })
      }

      // 先设置容器可见
      this.setData({ popupVisible: true })

      // 然后添加动画类触发过渡效果
      setTimeout(() => {
        this.setData({ animating: true })
      }, 30)
    },

    // 隐藏弹窗
    hidePopup() {
      // 先移除动画类
      this.setData({ animating: false })

      // 等待动画完成后再隐藏容器
      const timer = setTimeout(() => {
        if (this) {
          // 确保组件仍然存在
          this.setData({
            popupVisible: false,
            closeTimer: null
          })
        }
      }, 350) // 匹配CSS中最长的过渡时间

      this.setData({ closeTimer: timer })
    },

    // 阻止冒泡
    stopPropagation() {
      return false
    },

    // 点击遮罩层
    onMaskClick() {
      if (this.data.maskClosable) {
        this.hidePopup()
        this.triggerEvent('close')
      }
    },

    // 关闭弹窗
    onClose() {
      this.hidePopup()
      this.triggerEvent('close')
    },

    // 取消按钮点击
    onCancel() {
      this.hidePopup()
      this.triggerEvent('cancel')
    },

    // 确认按钮点击
    onConfirm() {
      this.hidePopup()
      this.triggerEvent('confirm')
    }
  }
})
