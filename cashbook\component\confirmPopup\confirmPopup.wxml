<view class="confirm-popup-mask {{animating ? 'visible' : ''}}" wx:if="{{popupVisible}}" bindtap="onMaskClick" catchtouchmove="stopPropagation">
  <view class="confirm-popup-container {{animating ? 'visible' : ''}}">
    <view class="confirm-popup-header">
      <view class="popup-close" catchtap="onClose">
       <van-icon name="cross" size="28rpx" />
      </view>
      <text class="popup-title" style="font-size: {{titleSize}}; color: {{titleColor}};">{{title}}</text>
    </view>
    <view class="confirm-popup-content" style="font-size: {{contentSize}}; color: {{contentColor}};">
      <text>{{content}}</text>
    </view>
    <view class="confirm-popup-footer">
      <view class="popup-btn cancel-btn" catchtap="onCancel" style="color: {{themeColor}};">{{cancelText}}</view>
      <view class="popup-btn confirm-btn" catchtap="onConfirm" style="background-color: {{themeColor}};">{{confirmText}}</view>
    </view>
  </view>
</view>
