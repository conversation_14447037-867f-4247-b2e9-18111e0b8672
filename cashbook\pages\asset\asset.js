// 导入API
import { getUserAccounts, getUserAccountDetail } from '../../api/account/index'

// 获取应用实例
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 资产列表
    assetList: [],

    // 资产统计数据
    netAssets: '0.00',
    totalAssets: '0.00',
    totalLiabilities: '0.00',

    // 加载状态
    loading: false,

    // 主题颜色
    themeColor: '#3498db',

    // 是否显示金额
    showAccount: true,

    // 状态栏高度
    statusBarHeight: 20,

    // 是否显示资产变动详情
    showAssetChangesDetail: false,

    // 资产变动日期
    assetChangesDate: '2025年5月29日',

    // 资产变动余额
    assetChangesBalance: '0.00',

    // 资产变动时间范围
    assetChangesTimeRange: '近1月'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 获取系统信息设置状态栏高度
    this.getSystemInfo()

    // 获取主题颜色
    this.getThemeColor()

    // 加载资产列表数据
    this.loadAssetList()
  },

  /**
   * 获取系统信息
   */
  getSystemInfo: function () {
    try {
      const systemInfo = wx.getSystemInfoSync()
      this.setData({
        statusBarHeight: systemInfo.statusBarHeight
      })
    } catch (e) {
      console.error('获取系统信息失败:', e)
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 每次显示页面时重新加载资产数据
    this.loadAssetList()
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    // 下拉刷新时重新加载数据
    this.loadAssetList()
      .then(() => {
        // 停止下拉刷新动画
        wx.stopPullDownRefresh()
      })
      .catch(() => {
        wx.stopPullDownRefresh()
      })
  },

  /**
   * 获取主题颜色
   */
  getThemeColor: function () {
    if (app.globalData && app.globalData.selectedColor) {
      this.setData({
        themeColor: app.globalData.selectedColor
      })
    }
  },

  /**
   * 加载资产列表数据
   */
  loadAssetList: function () {
    this.setData({ loading: true })

    // 调用API获取用户账户数据
    return new Promise((resolve, reject) => {
      getUserAccounts({ show_type: 'group', status: 'normal' })
        .then((res) => {
          console.log('获取资产列表成功:', res)
          if (res.code === 1 && res.data) {
            // 处理API返回的数据
            const formattedAssetList = this.formatAssetData(res.data)

            // 更新资产统计数据
            this.setData({
              assetList: formattedAssetList,
              netAssets: res.data.netassets || '0.00',
              totalAssets: res.data.capital || '0.00',
              totalLiabilities: res.data.liabilities || '0.00',
              loading: false
            })
            resolve()
          } else {
            wx.showToast({
              title: res.msg || '获取资产列表失败',
              icon: 'none'
            })
            this.setData({ loading: false })
            reject(new Error(res.msg || '获取资产列表失败'))
          }
        })
        .catch((err) => {
          console.error('获取资产列表失败:', err)
          wx.showToast({
            title: '获取资产列表失败',
            icon: 'none'
          })
          this.setData({ loading: false })
          reject(err)
        })
    })
  },

  /**
   * 格式化API返回的资产数据为组件所需格式
   */
  formatAssetData: function (apiData) {
    const formattedList = []

    if (!apiData || !apiData.data || !Array.isArray(apiData.data)) {
      return formattedList
    }

    // 遍历API返回的分组数据
    apiData.data.forEach((group) => {
      if (group.data && Array.isArray(group.data)) {
        // 遍历每个分组中的账户
        group.data.forEach((account) => {
          // 转换账户数据为组件所需格式
          const assetItem = {
            id: account.id,
            type: this.getAssetType(account.account_name, account.account_type),
            name: account.name,
            subType: account.account_name,
            bankName: account.name,
            cardNumber: account.cardnum || '',
            amount: account.money || '0.00',
            icon: account.image || '/assets/icons/bank/default.png',
            isNegative: account.account_type === 'liabilities',
            repaymentDay: account.repayment_date || '',
            repaymentDate: account.repayment_date ? `${account.repayment_date}号` : ''
          }

          // 如果是信用卡或其他负债类型，添加可用额度
          if (account.account_type === 'liabilities' && account.debt) {
            const debt = parseFloat(account.debt) || 0
            const money = parseFloat(account.money) || 0
            const availableLimit = (debt - Math.abs(money)).toFixed(2)
            assetItem.availableLimit = availableLimit
          }

          formattedList.push(assetItem)
        })
      }
    })

    return formattedList
  },

  /**
   * 获取资产类型
   */
  getAssetType: function (accountName, accountType) {
    if (accountType === 'liabilities') {
      return 'credit'
    }

    // 根据账户名称判断类型
    if (accountName.includes('微信') || accountName.includes('支付宝') || accountName.includes('京东') || accountName.includes('钱包')) {
      return 'online'
    }

    if (accountName.includes('借记卡') || accountName.includes('储蓄卡')) {
      return 'debit'
    }

    if (accountName.includes('信用卡')) {
      return 'credit'
    }

    if (accountName.includes('借出') || accountName.includes('借入')) {
      return 'person'
    }

    return 'debit'
  },

  /**
   * 切换资产变动详情显示状态
   */
  toggleAssetChangesDetail: function () {
    this.setData({
      showAssetChangesDetail: !this.data.showAssetChangesDetail
    })
  },

  /**
   * 启用数据统计
   */
  enableStatsData: function (e) {
    // 阻止事件冒泡
    e.stopPropagation()

    wx.showToast({
      title: '开启数据统计',
      icon: 'success'
    })
  },

  /**
   * 点击资产项
   */
  onAssetItemClick: function (e) {
    const { id, type } = e.detail
    console.log('点击资产项:', id, type)

    // 跳转到资产详情页
    wx.navigateTo({
      url: `/packageA/pages/settings/assetDetail/assetDetail?id=${id}`
    })
  },

  /**
   * 添加资产
   */
  onAddAsset: function () {
    wx.navigateTo({
      url: '/packageA/pages/settings/createAccount/createAccount'
    })
  },

  /**
   * 资产设置
   */
  onAssetSetting: function (e) {
    console.log('资产设置:', e)
    // 跳转到资产管理页面
    wx.navigateTo({
      url: '/packageA/pages/settings/assetManagement/assetManagement'
    })
  },

  /**
   * 隐藏资产
   */
  onHideAsset: function (e) {
    const { id } = e.detail
    console.log('隐藏资产:', id)

    // 这里可以添加隐藏资产的API调用
    wx.showToast({
      title: '已隐藏资产',
      icon: 'success'
    })

    // 重新加载资产列表
    this.loadAssetList()
  },

  /**
   * 编辑资产
   */
  onEditAsset: function (e) {
    const { id } = e.detail
    console.log('编辑资产:', id)

    wx.navigateTo({
      url: `/packageA/pages/settings/createAccount/createAccount?id=${id}&edit=true`
    })
  },

  /**
   * 删除资产
   */
  onDeleteAsset: function (e) {
    const { id } = e.detail
    console.log('删除资产:', id)

    // 这里可以添加删除资产的API调用
    wx.showModal({
      title: '提示',
      content: '确定要删除该资产吗？',
      success: (res) => {
        if (res.confirm) {
          // 用户点击确定，执行删除操作
          // 重新加载资产列表
          this.loadAssetList()
        }
      }
    })
  },

  /**
   * 查看隐藏资产
   */
  viewHiddenAssets: function () {
    // 跳转到隐藏资产页面
    wx.navigateTo({
      url: '/packageA/pages/settings/hiddenAssets/hiddenAssets'
    })
  },

  /**
   * 返回上一页
   */
  navigateBack: function () {
    wx.navigateBack()
  }
})
