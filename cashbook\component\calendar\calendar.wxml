<view class="calendar-container" wx:if="{{isShow}}">

<view  class="bg" style="overflow: hidden;border-radius: 15px 15px 0 0 ; ">
<!-- 1,2 -->
<view class="" style="padding: 10px;" >
 <!-- 第一行：显示当前年月 -->
 <view class="current-date" style="text-align: left;">{{year}}年{{month}}月</view>
  <!-- 第二行：显示年份和切换按钮 -->
  <view class="year-control ">
    <view class="year-text">{{year}}年</view>
    <view class="btn-group">
      <view bindtap="toggleView" class="btn">{{isMonthView && bottomId==0 ? '切换到年份' : '切换到月份'}}</view>
      <view bindtap="{{isMonthView? 'prevYear' : 'prevDecade'}}" class="btn">{{isMonthView? '上一年' : '上'}}</view>
      <view bindtap="{{isMonthView? 'nextYear' : 'nextDecade'}}" class="btn">{{isMonthView? '下一年' : '下'}}</view>
    </view>
  </view>
</view>

<view style="background-color: #fff;">
  <!-- 第三行：显示日历（月份或年份） -->
  <view class="list-container">
    <!-- 使用 wx:if 和 wx:else 控制显示月份或年份列表 -->
    <!-- 年份 -->
    <view class="myaddClass1" wx:if="{{ isMonthView}}">
      <view bindtap="selectMonth" data-month="{{index + 1}}" 
      class="item {{item==activeId? 'active' :''}}" wx:for="{{12}}" wx:key="index">{{index + 1}}月</view>
    </view>
    <!-- 月份 {{item==activeYear ?'active' :''}}  data-index="{{index}}"-->
    <view class="myaddClass1" wx:else>
      <view bindtap="selectYear" data-year="{{startYear + index}}"   data-index="{{index}}"
      class="item {{item==activeYear ?'active' :''}}" wx:for="{{10}}" wx:key="index">{{startYear + index}}年</view>
    </view>
  </view>
  <!-- 第四行：确定和取消按钮 -->
  <view class="btn-group bottom-btn-group">
    <view bindtap="cancel" class="btn">取消</view>
    <view bindtap="confirm" class="btn">确定</view>
  </view>
</view>


</view>

</view>
<view class="mask" wx:if="{{isShow}}"></view>
