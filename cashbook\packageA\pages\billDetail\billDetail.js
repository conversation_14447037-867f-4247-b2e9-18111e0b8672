import { getAccountBookDetail, setDefaultBook } from '../../../api/book/index';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    id: '',
    billDetail: {
      categoryName: '收债',
      categoryIcon: '/static/icon/check-circle.png',
      amount: '100.00',
      time: '5月21日 星期三',
      accountBook: '默认账本',
      assetAccount: '邮政',
      hasRefund: false
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    console.log('账单详情页面加载，参数:', options);
    
    if (options.id) {
      this.setData({
        id: options.id
      });
      
      // 加载账单详情数据
      this.loadBillDetail(options.id);
    }
  },
  
  /**
   * 加载账单详情
   */
  loadBillDetail(id) {
    // 这里应该添加真实的数据加载逻辑
    // 示例仅使用模拟数据
    console.log(`加载账单详情，ID: ${id}`);
    
    // 模拟异步加载
    wx.showLoading({
      title: '加载中...',
    });
    
    setTimeout(() => {
      wx.hideLoading();
    }, 500);
  },

  /**
   * 关闭页面
   */
  onClose() {
    wx.navigateBack();
  },

  /**
   * 删除记录
   */
  onDelete() {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条记录吗？',
      confirmColor: '#ff4242',
      success: (res) => {
        if (res.confirm) {
          // 执行删除操作
          console.log('删除账单:', this.data.id);
          
          // 删除成功后返回上一页
          wx.showToast({
            title: '删除成功',
            icon: 'success',
            duration: 1500,
            success: () => {
              setTimeout(() => {
                wx.navigateBack();
              }, 1500);
            }
          });
        }
      }
    });
  },

  /**
   * 退款操作
   */
  onRefund() {
    wx.navigateTo({
      url: `/pages/refund/refund?id=${this.data.id}`
    });
  },

  /**
   * 修改记录
   */
  onEdit() {
    wx.navigateTo({
      url: `/pages/inputMoney/inputMoney?id=${this.data.id}&mode=edit`
    });
  }
}) 