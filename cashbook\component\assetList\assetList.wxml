<!-- component/assetList/assetList.wxml -->
<view class="asset-list-container">
  <!-- 资产列表标题 -->
  <view class="asset-header">
    <view class="asset-title">资产</view>
    <view class="asset-actions">
      <view class="asset-card-icon" bindtap="onSettingTap">
        <van-icon name="setting-o" size="24px" />
      </view>
      <view class="asset-card-icon" bindtap="onAddAssetTap">
        <van-icon name="plus" size="24px" />
      </view>
    </view>
  </view>

  <!-- 资产列表内容 -->
  <view class="asset-content">
    <block wx:if="{{assetList.length > 0}}">
      <block wx:for="{{assetList}}" wx:key="id">
        <!-- 资产项 -->
        <van-swipe-cell right-width="{{ 180 }}" left-width="{{ 65 }}" name="{{item.id}}" bind:click="onSwipeCellClick">
          <view class="asset-item" bindtap="onAssetItemTap" data-id="{{item.id}}" data-type="{{item.type}}">
            <!-- 左侧图标 -->
            <view class="asset-icon">
              <image src="{{item.icon}}" mode="aspectFit"></image>
            </view>

            <!-- 中间信息 -->
            <view class="asset-info">
              <view class="asset-name-row">
                <view class="asset-name">
                  {{item.name}}
                  <view wx:if="{{item.availableLimit && item.isNegative}}" class="asset-available-tag">
                    可用:{{item.availableLimit}}
                  </view>
                </view>
              </view>
              <view class="asset-detail">
                <text class="card-type-tag card-type-debit" wx:if="{{item.type === 'debit'}}" style="background-color: {{selectedColor}}; color: #000;">借记卡</text>
                <text class="card-type-tag card-type-credit" wx:elif="{{item.type === 'credit'}}">信用卡</text>
                <text class="card-type-tag card-type-online" wx:elif="{{item.type === 'online'}}">{{item.subType}}</text>
                <text class="card-type-tag card-type-person" wx:elif="{{item.type === 'person'}}">{{item.subType}}</text>
                <text wx:if="{{item.bankName}}">{{item.bankName}} {{item.cardNumber}}</text>
                <text wx:else>{{item.subType}}</text>
              </view>
            </view>

            <!-- 右侧金额 -->
            <view class="asset-amount {{item.isNegative ? 'negative' : ''}}">
              <view class="amount-text">{{showAmount ? (item.isNegative ? '-' : '') + '¥' + item.amount : '¥***'}}</view>
              <!-- 显示还款日期标签 -->
              <view class="repayment-tag" wx:if="{{item.repaymentDay && item.isNegative}}" style="background-color: {{selectedColor}};">
                还款日:{{item.repaymentDate}}
              </view>
            </view>
          </view>
          <view slot="right" class="right-buttons">
            <view class="hide-button" catchtap="hideAsset" data-id="{{item.id}}">
              隐藏
            </view>
            <view class="edit-button" catchtap="onEditAsset" data-id="{{item.id}}">
              编辑
            </view>
            <view class="delete-button" catchtap="onDeleteAsset" data-id="{{item.id}}">
              删除
            </view>
          </view>
        </van-swipe-cell>
      </block>
    </block>
    <block wx:else>
      <van-empty description="没有发现资产账户，试着添加一个吧~" />
    </block>
  </view>

  <!-- 查看隐藏资产按钮 -->
  <view class="view-hidden-assets" bindtap="viewHiddenAssets">
    <view class="hidden-assets-text" style="color: {{selectedColor}};">查看隐藏资产 ></view>
  </view>
</view>

<!-- 删除资产弹层 -->
<customPopup title="" visible="{{deletePopupVisible}}" position="bottom" round="{{true}}" bind:close="closeDeletePopup" closeButtonPosition="left" maxHeight="auto">
  <view class="delete-popup-container">
    <!-- 删除资产和账单选项 -->
    <view class="delete-popup-option" bindtap="showDeleteWithBillConfirm">
      <view class="delete-popup-option-content">
        <view class="delete-popup-title">删除资产和账单</view>
        <view class="delete-popup-subtitle">删除资产并删除资产内的账单</view>
      </view>
      <view class="delete-popup-arrow">
        <van-icon name="arrow" size="16px" />
      </view>
    </view>

    <!-- 仅删除资产选项 -->
    <view class="delete-popup-option" bindtap="showDeleteOnlyConfirm">
      <view class="delete-popup-option-content">
        <view class="delete-popup-title">仅删除资产</view>
        <view class="delete-popup-subtitle">只删除资产，账单保留</view>
      </view>
      <view class="delete-popup-arrow">
        <van-icon name="arrow" size="16px" />
      </view>
    </view>
  </view>
</customPopup>

<!-- 删除资产和账单确认弹窗 -->
<confirmPopup
  show="{{deleteWithBillConfirmVisible}}"
  title="删除资产账户"
  content="删除会将这个记录下的所有账单都会清除，谨慎操作哦~"
  cancelText="取消"
  confirmText="确认删除"
  bind:cancel="closeDeleteWithBillConfirm"
  bind:confirm="deleteAssetAndBill"
>
</confirmPopup>

<!-- 仅删除资产确认弹窗 -->
<confirmPopup
  show="{{deleteOnlyConfirmVisible}}"
  title="删除资产账户"
  content="是否只删除这个资产，账单保留"
  cancelText="取消"
  confirmText="确认删除"
  bind:cancel="closeDeleteOnlyConfirm"
  bind:confirm="deleteAssetOnly"
>
</confirmPopup>

<!-- 资产设置弹层 -->
<customPopup visible="{{settingsPopupVisible}}" title="首页资产" position="bottom" round="{{true}}" bind:close="closeSettingsPopup" closeButtonPosition="left" maxHeight="70%">
  <view class="settings-popup-container">
    <!-- 资产管理选项 -->
    <view class="settings-option" bindtap="navigateToAssetManagement">
      <view class="settings-option-left">资产管理</view>
      <view class="settings-option-right">
        <van-icon name="arrow" size="16px" color="#999" />
      </view>
    </view>

    <!-- 资产排序选项 -->
    <view class="settings-option" bindtap="navigateToAssetSort">
      <view class="settings-option-left">
        <view class="settings-option-title">资产排序</view>
        <view class="settings-option-subtitle">将资产整理顺序，常用可至前。</view>
      </view>
      <view class="settings-option-right">
        <van-icon name="arrow" size="16px" color="#999" />
      </view>
    </view>

    <!-- 资产变动选项 -->
    <view class="settings-option" bindtap="navigateToAssetChanges">
      <view class="settings-option-left">资产变动</view>
      <view class="settings-option-right">
        <van-icon name="arrow" size="16px" color="#999" />
      </view>
    </view>

    <!-- 资产展示选项 -->
    <view class="settings-option">
      <view class="settings-option-left">
        <view class="settings-option-title">资产展示</view>
        <view class="settings-option-subtitle">分组模式可查看资产变动</view>
      </view>
      <view class="settings-option-right" bindtap="toggleAssetDisplayMode">
        {{assetDisplayMode}}
        <van-icon name="arrow" size="16px" color="#999" style="margin-left: 5px;" />
      </view>
    </view>

    <!-- 资产统计显示开关 -->
    <view class="settings-option">
      <view class="settings-option-left">
        <view class="settings-option-title">资产统计显示</view>
        <view class="settings-option-subtitle">分组模式下，资产显示相关统计信息。</view>
      </view>
      <view class="settings-option-right">
        <switch checked="{{assetStatisticsSwitch}}" bindchange="toggleAssetStatistics" color="#20cc52" />
      </view>
    </view>
  </view>
</customPopup>
