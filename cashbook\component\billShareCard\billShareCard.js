Component({
    /**
     * 组件的属性列表
     */
    properties: {
        visible: {
            type: Boolean,
            value: false
        },
        billData: {
            type: Object,
            value: {
                expenseCount: 1,
                expenseTotal: 20.00,
                incomeCount: 1,
                incomeTotal: 0.00,
                date: '5月24日',
                items: [
                    {
                        icon: '/static/icon/food.png',
                        category: '餐饮11',
                        subcategory: '大碗饭111',
                        amount: 20.00,
                        tag: '午餐'
                    },
                ]
            }
        },
        buttonPosition: {
            type: String,
            value: 'right' // 'left'或'right'
        }
    },

    /**
     * 组件的初始数据
     */
    data: {
        cardStyle: {
            backgroundColor: '#ffb6c1', // 粉色背景
            textColor: '#333333',
            selectedColor: '#ff5252' // 默认选中颜色
        },
        formatPopupVisible: false,
        cardFormat: 'frog', // 默认样式：小青账
        storedColors: {}, // 存储颜色配置
        selectedColor: '#ff5252' // 直接存储的选中颜色
    },

    lifetimes: {
        attached() {
            // 加载颜色配置
            let storedColors = wx.getStorageSync('cardColors');
            if (!storedColors) {
                storedColors = {
                    theme1: {
                        backgroundColor: '#ffb6c1', // 粉色背景
                        textColor: '#333333',
                    },
                    theme2: {
                        backgroundColor: '#183241', // 深蓝色背景
                        textColor: '#ffffff',
                    }
                };
                wx.setStorageSync('cardColors', storedColors);
            }
            // 确保storedColors中有selectedColor
            if (!storedColors.theme1.selectedColor) {
                storedColors.theme1.selectedColor = '#ff5252';
            }
            if (!storedColors.theme2.selectedColor) {
                storedColors.theme2.selectedColor = '#2196f3';
            }

            // 获取直接存储的selectedColor
            let directSelectedColor = wx.getStorageSync('selectedColor');
            if (!directSelectedColor) {
                directSelectedColor = '#ff5252'; // 默认值
                wx.setStorageSync('selectedColor', directSelectedColor);
            }

            // 初始化样式
            this.setData({
                cardStyle: storedColors.theme1,
                storedColors: storedColors,
                selectedColor: directSelectedColor
            });
        }
    },

    /**
     * 组件的方法列表
     */
    methods: {
        /**
         * 关闭弹窗
         */
        onClose() {
            this.setData({
                visible: false
            });
            this.triggerEvent('close');
        },

        /**
         * 更换颜色
         */
        onChangeColor() {
            const storedColors = wx.getStorageSync('cardColors') || {
                theme1: {
                    backgroundColor: '#ffb6c1',
                    textColor: '#333333',
                    selectedColor: '#ff5252'
                },
                theme2: {
                    backgroundColor: '#183241',
                    textColor: '#ffffff',
                    selectedColor: '#2196f3'
                }
            };

            // 切换颜色主题
            const currentBgColor = this.data.cardStyle.backgroundColor;
            let newTheme;

            if (currentBgColor === storedColors.theme1.backgroundColor) {
                newTheme = storedColors.theme2;
                // 保持使用本地存储的selectedColor
                const directSelectedColor = wx.getStorageSync('selectedColor') || '#ff5252';
                this.setData({
                    cardStyle: newTheme,
                    storedColors: storedColors,
                    selectedColor: directSelectedColor
                });
            } else {
                newTheme = storedColors.theme1;
                // 保持使用本地存储的selectedColor
                const directSelectedColor = wx.getStorageSync('selectedColor') || '#ff5252';
                this.setData({
                    cardStyle: newTheme,
                    storedColors: storedColors,
                    selectedColor: directSelectedColor
                });
            }
        },

        /**
         * 改变样式
         */
        onChangeFormat() {
            this.setData({
                formatPopupVisible: true
            });
        },

        /**
         * 关闭样式弹窗
         */
        onCloseFormatPopup() {
            this.setData({
                formatPopupVisible: false
            });
        },

        /**
         * 选择样式
         */
        selectFormat(e) {
            const format = e.currentTarget.dataset.format;
            this.setData({
                cardFormat: format,
                formatPopupVisible: false
            });

            wx.showToast({
                title: '样式已更新',
                icon: 'success',
                duration: 1500
            });
        },

        /**
         * 保存图片
         */
        onSaveImage() {
            wx.showLoading({
                title: '生成图片中...',
            });

            // 在实际应用中，这里需要使用canvas或者后端服务创建图片
            setTimeout(() => {
                wx.hideLoading();
                wx.showToast({
                    title: '图片保存成功',
                    icon: 'success'
                });
            }, 1500);
        }
    }
}) 