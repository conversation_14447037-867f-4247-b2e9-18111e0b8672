/* 记账偏好设置页面样式 */
.container {
  padding: 0 0 40rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
  box-sizing: border-box;
  padding-top: 160rpx; /* 为customNav预留空间 */
}

/* 页面说明 */
.page-desc {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  background-color: #fff;
  margin-bottom: 20rpx;

  .desc-icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background-color: #f5f5f5;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 20rpx;
  }

  .desc-text {
    font-size: 28rpx;
    color: #666;
    flex: 1;
  }
}

/* 设置区块 */
.section {
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 0 30rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    padding: 30rpx 0 20rpx;
    border-bottom: 1rpx solid #f5f5f5;
  }
}

/* 设置项 */
.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;

  .item-label {
    font-size: 30rpx;
    color: #333;
  }

  .item-value {
    display: flex;
    align-items: center;

    text {
      font-size: 28rpx;
      color: #999;
      margin-right: 10rpx;
    }
  }
}

/* 设置项描述 */
.item-desc {
  font-size: 24rpx;
  color: #999;
  padding: 0 0 20rpx;
}

/* 日期选择弹窗 */
.date-picker-popup {
  width: 600rpx;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;

  .popup-header {
    padding: 30rpx;
    position: relative;

    .close-btn {
      position: absolute;
      top: 30rpx;
      right: 30rpx;
    }

    .popup-title {
      font-size: 34rpx;
      font-weight: 500;
      text-align: center;
      margin-bottom: 20rpx;
    }

    .popup-subtitle {
      font-size: 28rpx;
      color: #666;
      text-align: center;
      margin-bottom: 10rpx;
    }

    .popup-desc {
      font-size: 24rpx;
      color: #999;
      text-align: center;
    }
  }

  .date-grid {
    padding: 20rpx 30rpx;

    .date-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20rpx;

      .date-cell {
        width: 70rpx;
        height: 70rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        font-size: 28rpx;

        &.selected {
          background-color: var(--theme-color, #3cc51f);
          color: #fff;
        }
      }
    }
  }

  .popup-footer {
    display: flex;
    border-top: 1rpx solid #f5f5f5;

    .cancel-btn,
    .confirm-btn {
      flex: 1;
      height: 90rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 32rpx;
    }

    .cancel-btn {
      color: #666;
      border-right: 1rpx solid #f5f5f5;
    }

    .confirm-btn {
      color: var(--theme-color, #3cc51f);
      font-weight: 500;
    }
  }
}

/* 记账页面设置弹窗 */
.booking-page-popup {
  width: 650rpx;
  max-height: 900rpx;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;

  .popup-header {
    padding: 30rpx;
    position: relative;
    border-bottom: 1rpx solid #f5f5f5;

    .close-btn {
      position: absolute;
      top: 30rpx;
      right: 30rpx;
    }

    .popup-title {
      font-size: 34rpx;
      font-weight: 500;
      text-align: center;
    }
  }

  .popup-content {
    max-height: 700rpx;

    .setting-section {
      padding: 0 30rpx;

      .section-title {
        font-size: 30rpx;
        font-weight: 500;
        color: #333;
        padding: 30rpx 0 20rpx;
      }

      .setting-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20rpx 0;
        border-bottom: 1rpx solid #f5f5f5;

        .item-left {
          flex: 1;

          .item-label {
            font-size: 28rpx;
            color: #333;
            margin-bottom: 6rpx;
          }

          .item-desc {
            font-size: 24rpx;
            color: #999;
          }
        }

        .item-value {
          display: flex;
          align-items: center;

          text {
            font-size: 28rpx;
            color: #999;
            margin-right: 10rpx;
          }
        }
      }
    }
  }
}

/* 账本选择器样式 */
.book-selector {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;

  .selector-header {
    padding: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1rpx solid #f5f5f5;

    .title {
      font-size: 34rpx;
      font-weight: 500;
    }

    .actions {
      display: flex;
      align-items: center;

      .action-btn {
        margin-left: 30rpx;
        font-size: 28rpx;
        color: #666;
      }
    }
  }

  .selector-content {
    max-height: 600rpx;
    padding: 20rpx 30rpx;

    .book-item {
      display: flex;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #f5f5f5;

      .book-icon {
        width: 80rpx;
        height: 80rpx;
        border-radius: 16rpx;
        background-color: #f5f5f5;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20rpx;
      }

      .book-info {
        flex: 1;

        .book-name {
          font-size: 30rpx;
          color: #333;
          margin-bottom: 6rpx;
        }

        .book-desc {
          font-size: 24rpx;
          color: #999;
        }
      }

      .book-select {
        width: 40rpx;
        height: 40rpx;
        border-radius: 50%;
        border: 1rpx solid #ddd;
        display: flex;
        justify-content: center;
        align-items: center;

        &.selected {
          background-color: var(--theme-color, #3cc51f);
          border-color: var(--theme-color, #3cc51f);

          .check-icon {
            color: #fff;
          }
        }
      }
    }
  }

  .selector-footer {
    padding: 20rpx 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1rpx solid #f5f5f5;

    .select-all {
      display: flex;
      align-items: center;
      font-size: 28rpx;
      color: #333;

      .select-box {
        width: 36rpx;
        height: 36rpx;
        border-radius: 6rpx;
        border: 1rpx solid #ddd;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 10rpx;

        &.selected {
          background-color: var(--theme-color, #3cc51f);
          border-color: var(--theme-color, #3cc51f);

          .check-icon {
            color: #fff;
          }
        }
      }
    }

    .confirm-btn {
      width: 200rpx;
      height: 70rpx;
      background-color: var(--theme-color, #3cc51f);
      color: #fff;
      font-size: 30rpx;
      border-radius: 35rpx;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

/* 主题色变量 */
page {
  --theme-color: #3cc51f;
}
