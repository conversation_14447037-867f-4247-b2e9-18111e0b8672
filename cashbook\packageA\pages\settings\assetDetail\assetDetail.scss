/* 资产详情页样式 */

/* 页面容器 */
.page-container {
  height: 100vh;
  background-color: #f8f8f8;
}

/* 状态栏 */
.status-bar {
  width: 100%;
  background-color: #ffffff;
}

/* 导航栏 */
.navigation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  background-color: #ffffff;
  position: relative;

  .nav-left {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 40rpx;
  }

  .nav-title {
    position: absolute;
    left: 0;
    right: 0;
    text-align: center;
    font-size: 36rpx;
    font-weight: 500;
    color: #333333;
    pointer-events: none;
  }

  .nav-right {
    font-size: 32rpx;
    color: #333333;
  }
}

/* 卡片信息头部 */
.asset-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f5f5f5;

  .asset-title {
    display: flex;
    align-items: center;

    .asset-icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 10rpx;
      overflow: hidden;
      margin-right: 20rpx;
      background-color: #f5f5f5;
      display: flex;
      justify-content: center;
      align-items: center;

      image {
        width: 80%;
        height: 80%;
        object-fit: contain;
      }
    }

    .asset-name {
      font-size: 32rpx;
      font-weight: 500;
      color: #333333;
    }
  }

  .edit-btn {
    font-size: 28rpx;
    color: #999999;
    padding: 8rpx 30rpx;
  }
}

/* 信用卡详情 */
.asset-details {
  background-color: #ffffff;
  border-radius: 20rpx;
  margin: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  /* 欠款和额度区域 - 左右布局 */
  .balance-limit-section {
    display: flex;
    justify-content: space-between;
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid #f5f5f5;
  }

  /* 当前欠款 */
  .balance-section {
    flex: 1;
    padding-bottom: 20rpx;

    .balance-label {
      font-size: 28rpx;
      color: #666666;
      margin-bottom: 10rpx;
    }

    .balance-amount {
      text {
        font-size: 60rpx;
        font-weight: 600;
        color: #333333;
        line-height: 1;
      }
    }
  }

  /* 可用额度 */
  .limit-section {
    flex: 1;
    padding: 0 0 20rpx;

    .limit-label {
      font-size: 28rpx;
      color: #666666;
      margin-bottom: 10rpx;
    }

    .limit-amount {
      font-size: 32rpx;
      font-weight: 500;
      color: #333333;
      margin-bottom: 15rpx;
    }

    .limit-progress {
      height: 8rpx;
      background-color: #e9e9e9;
      border-radius: 4rpx;
      overflow: hidden;

      .progress-bar {
        height: 100%;
        border-radius: 4rpx;
        transition: width 0.3s ease;
      }
    }
  }

  /* 账单信息区域 */
  .bill-info-section {
    padding: 20rpx 0;

    .bill-info-row {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin-bottom: 10rpx;

      .bill-info-item {
        width: 33.33%;
        padding: 10rpx 0;

        &.center-align {
          text-align: center;
        }

        &.right-align {
          text-align: right;
        }

        .bill-info-label {
          font-size: 24rpx;
          color: #999999;
          margin-bottom: 6rpx;
        }

        .bill-info-value {
          font-size: 24rpx;
          color: #333333;
          font-weight: 500;

          .date-status {
            display: inline-block;
            margin-left: 6rpx;
            font-size: 20rpx;
            color: #999999;
            background-color: #f5f5f5;
            padding: 2rpx 8rpx;
            border-radius: 4rpx;
          }
        }
      }
    }
  }

  /* 分隔线 */
  .divider {
    height: 1px;
    background-color: #f0f0f0;
    margin: 10rpx 0;
  }
}

/* 提示信息 */
.tip-section {
  margin: 20rpx;
  padding: 20rpx;
  font-size: 24rpx;
  color: #999999;
  line-height: 1.6;
  background-color: #ffffff;
  border-radius: 16rpx;
}

/* 年份选择器 */
.year-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 30rpx 20rpx 10rpx;

  .year-display {
    display: flex;
    align-items: center;
    font-size: 32rpx;
    font-weight: 500;
    color: #333333;

    van-icon {
      margin-left: 10rpx;
      transition: transform 0.3s ease;

      &.rotated {
        transform: rotate(180deg);
      }
    }
  }

  .setting-icon {
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666666;
  }
}

/* 月份账单列表 */
.month-bill-list {
  margin: 10rpx 20rpx;

  .month-bill-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx;
    background-color: #ffffff;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);

    .month-info {
      .month-name {
        font-size: 28rpx;
        font-weight: 500;
        color: #333333;
        margin-bottom: 8rpx;
        display: flex;
        align-items: center;

        .month-icon {
          margin-left: 10rpx;
          color: #999999;
          display: flex;
          align-items: center;
        }
      }

      .month-period {
        font-size: 22rpx;
        color: #999999;
      }
    }

    .month-amount {
      text-align: right;

      .expense {
        font-size: 24rpx;
        color: #333333;
        margin-bottom: 4rpx;
        font-weight: 500;
      }

      .income {
        font-size: 24rpx;
        color: #999999;
      }
    }
  }
}

/* 底部占位 */
.bottom-placeholder {
  height: 120rpx;
  width: 100%;
}

/* 底部按钮区域 */
.bottom-buttons {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

  .record-btn,
  .repay-btn {
    flex: 1;
    height: 80rpx;
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30rpx;
    font-weight: 500;
  }

  .record-btn {
    background-color: #f5f5f5;
    color: #333333;
    margin-right: 20rpx;
  }

  .repay-btn {
    color: #ffffff;
  }
}
