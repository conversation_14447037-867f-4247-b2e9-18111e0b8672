/* 资产详情页样式 */

/* 页面容器 */
.page-container {
  height: 100vh;
  background-color: #f8f8f8;
}

/* 状态栏 */
.status-bar {
  width: 100%;
  background-color: #ffffff;
}

/* 导航栏 */
.navigation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  background-color: #ffffff;
  position: relative;

  .nav-left {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 40rpx;
  }

  .nav-title {
    position: absolute;
    left: 0;
    right: 0;
    text-align: center;
    font-size: 36rpx;
    font-weight: 500;
    color: #333333;
    pointer-events: none;
  }

  .nav-right {
    font-size: 32rpx;
    color: #333333;
  }
}

/* 资产卡片 - 将头部和详情合并在同一个卡片中 */
.asset-card {
  background-color: #ffffff;
  margin: 0 0 20rpx 0;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 卡片信息头部 */
.asset-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: #ffffff;

  .asset-title {
    display: flex;
    align-items: center;

    .asset-icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 10rpx;
      overflow: hidden;
      margin-right: 20rpx;
      background-color: #f5f5f5;
      display: flex;
      justify-content: center;
      align-items: center;

      image {
        width: 80%;
        height: 80%;
        object-fit: contain;
      }
    }

    .asset-name {
      font-size: 32rpx;
      font-weight: 500;
      color: #333333;
    }
  }

  .edit-btn {
    font-size: 28rpx;
    color: #999999;
    padding: 8rpx 30rpx;
  }
}

/* 信用卡详情 */
.asset-details {
  background-color: #ffffff;
  padding: 20rpx 30rpx 30rpx;
}

/* 欠款和额度区域 */
.balance-limit-section {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;

  /* 当前欠款 */
  .balance-section {
    flex: 1;

    .balance-label {
      font-size: 28rpx;
      color: #666666;
      margin-bottom: 10rpx;
    }

    .balance-amount {
      font-size: 72rpx;
      font-weight: 500;
      color: #333333;

      text {
        font-family: Arial, sans-serif;
      }
    }
  }

  /* 可用额度 */
  .limit-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: flex-end;

    .limit-label {
      font-size: 28rpx;
      color: #666666;
      margin-bottom: 10rpx;
    }

    .limit-amount {
      font-size: 36rpx;
      color: #333333;
      margin-bottom: 8rpx;
    }

    .limit-progress {
      width: 100%;
      height: 10rpx;
      background-color: #f0f0f0;
      border-radius: 5rpx;
      overflow: hidden;

      .progress-bar {
        height: 100%;
        border-radius: 5rpx;
      }
    }
  }
}

/* 账单信息区域 */
.bill-info-section {
  .bill-info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10rpx;

    .bill-info-item {
      flex: 1;

      &.center-align {
        text-align: center;
      }

      &.right-align {
        text-align: right;
      }

      .bill-info-label {
        font-size: 28rpx;
        color: #666666;
        margin-bottom: 8rpx;
      }

      .bill-info-value {
        font-size: 30rpx;
        color: #333333;

        .date-status {
          display: inline-block;
          font-size: 22rpx;
          color: #666666;
          background-color: #f2f2f2;
          padding: 0 6rpx;
          border-radius: 4rpx;
          margin-left: 6rpx;
        }
      }
    }
  }

  .divider {
    height: 1rpx;
    background-color: #f0f0f0;
    margin: 20rpx 0;
  }
}

/* 提示信息 */
.tip-section {
  background-color: #ffffff;
  padding: 24rpx 30rpx;
  margin-bottom: 20rpx;
  font-size: 26rpx;
  color: #999999;
  line-height: 1.5;
}

/* 年份选择器 */
.year-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  background-color: #ffffff;
  margin-bottom: 2rpx;

  .year-display {
    display: flex;
    align-items: center;
    font-size: 32rpx;
    font-weight: 500;
    color: #333333;

    .van-icon {
      margin-left: 10rpx;
      transition: transform 0.3s;

      &.rotated {
        transform: rotate(180deg);
      }
    }
  }

  .setting-icon {
    font-size: 40rpx;
    color: #999999;
  }
}

/* 月份账单列表 */
.month-bill-list {
  background-color: #ffffff;

  .month-bill-item {
    display: flex;
    justify-content: space-between;
    padding: 30rpx;

    .month-info {
      .month-name {
        display: flex;
        align-items: center;
        font-size: 32rpx;
        color: #333333;
        font-weight: 500;
        margin-bottom: 8rpx;

        .month-icon {
          display: flex;
          align-items: center;
          margin-left: 10rpx;
          color: #999999;
        }
      }

      .month-period {
        font-size: 24rpx;
        color: #999999;
      }
    }

    .month-amount {
      text-align: right;

      .expense {
        font-size: 28rpx;
        color: #333333;
        margin-bottom: 6rpx;
      }

      .income {
        font-size: 28rpx;
        color: #333333;
      }
    }
  }
}

/* 底部占位 */
.bottom-placeholder {
  height: 120rpx;
}

/* 底部按钮 */
.bottom-buttons {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  height: 100rpx;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

  .record-btn {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 32rpx;
    color: #333333;
    border-right: 1rpx solid #f0f0f0;
  }

  .repay-btn {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 32rpx;
    color: #ffffff;
  }
}
