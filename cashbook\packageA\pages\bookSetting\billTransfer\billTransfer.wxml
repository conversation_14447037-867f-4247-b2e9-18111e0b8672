<!--packageA/pages/bookSetting/billTransfer/billTransfer.wxml-->
<!-- 账单迁移页面 -->
<view class="transfer-container">
  <!-- 顶部导航栏 -->
  <!-- <view class="nav-bar">
    <view class="back-icon" bindtap="goBack">
      <van-icon name="arrow-left" size="20px" color="#333333" />
    </view>
    <view class="page-title">账本迁移</view>
  </view> -->

  <!-- 主要内容区域 -->
  <view class="content-area">
    <!-- 迁移账本名称 -->
    <view class="section-title">迁移账本名称</view>
    <view class="book-card">
      <view class="book-info">
        <view class="book-name">{{accountBookDetail.name || '默认账本'}}</view>
      </view>
      <view class="book-avatar">
        <image src="{{accountBookDetail.cover_img || '/static/images/book-avatar.png'}}" mode="aspectFill" />
      </view>
    </view>

    <!-- 迁移到新账本 -->
    <view class="section-title">迁移到新账本</view>
    <view class="book-card" bindtap="selectTargetBook">
      <view class="book-info">
        <view class="book-name {{!targetBook ? 'target-book' : ''}}">{{targetBook || '请选择'}}</view>
      </view>
      <view class="select-icon" wx:if="{{!targetBook}}">
        <van-icon name="arrow" size="20px" color="#999999" />
      </view>
      <view class="book-avatar" wx:if="{{targetBook}}">
        <image src="{{targetBookImage || '/static/images/book-avatar.png'}}" mode="aspectFill" />
      </view>
    </view>

    <!-- 提示信息 -->
    <view class="hint-text">
      迁移后原有账本使用到的分类会和新账本分类进行合并
    </view>
  </view>

  <!-- 底部确认按钮 -->
  <view class="confirm-button" bindtap="confirmTransfer">
    确认迁移
  </view>
  <!-- 使用封装的账本选择器组件 -->
  <book-selector
    show="{{showBookSelector}}"
    bookList="{{bookList}}"
    showSelectionIndicator="{{false}}"
    showSelectModeToggle="{{false}}"
    showAllBooksOption="{{false}}"
    showAddButton="{{false}}"
    showViewModeToggle="{{true}}"
    showReloadOption="{{true}}"
    title="选择账本"
    bind:select="onBookSelect"
    bind:close="closeBookSelector"
  />

</view>