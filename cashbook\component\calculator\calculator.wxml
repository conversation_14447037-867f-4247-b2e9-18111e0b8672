<view class="box">
  <!-- 滚动选择器 -->
  <scrollTap2 isIcon="{{true}}" isSet="{{true}}" list="{{sclist}}"></scrollTap2>
  <!-- 备注图片和金额的部分 -->
  <view class="header">
    <!-- 输入框 -->
    <view class="input-container" style="{{inputContainerStyle}}">
      <input wx:if="{{inputVisible}}" type="text" placeholder="请输入备注信息(最多150字)" placeholder-style="font-size:26rpx;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;" maxlength="150" bindinput="handleInput" value="{{inputValue}}"   />
      <!-- <view wx:if="{{inputValue.length > 0 && inputVisible}}" class="char-count">{{inputValue.length}}/150</view> -->
    </view>
    <!-- 图片和文字 -->
    <view class="photo" style="{{photoStyle}}">
      <span>
        <image src="/static/icon/pic.png" mode="" />
      </span>
      <text wx:if="{{photoTextVisible}}">图片</text>
    </view>
    <!-- 显示结果和表达式在同一行，自动扩展宽度 -->
    <view class="result-container" style="{{resultContainerStyle}}">
      <!-- 显示结果和表达式在同一行，自动扩展宽度 -->
      <view class="result-wrapper">
        <text class="currency-symbol" style="color: {{resultTextColor}};">￥</text>
        <text class="result" style="color: {{resultTextColor}};">{{expression || result}}</text>
      </view>
    </view>

  </view>
  <!-- 计算器组件 -->
  <view class="body" style="background-color: {{themeColor}};">
    <view bind:tap="getNum" data-info="{{item}}" class="bitem" style="background-color: {{themeColor === '#4BA3E1' ? '#A7D8F7' : (themeColor === '#FF6B6B' ? '#FFADAD' : '#E8E8E8')}}" wx:for="{{list}}" wx:key="index">
      {{item}}
    </view>
  </view>
  <!-- 底部横线 -->
  <!-- <view class="bottom-line"></view> -->
</view>