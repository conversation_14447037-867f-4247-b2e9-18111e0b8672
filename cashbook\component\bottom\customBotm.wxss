.box{
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #ffffff;
  padding: 10rpx 0 40rpx;
  z-index: 1000;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 新的Tab Bar样式 */
.tab-bar {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 0 20rpx;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 25%;
  padding: 10rpx 0;
}

.tab-icon {
  width: 56rpx;
  height: 56rpx;
  margin-bottom: 6rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.tab-icon image {
  width: 44rpx;
  height: 44rpx;
}

.tab-text {
  font-size: 24rpx;
  color: #999999;
}

.active .tab-icon image {
  width: 48rpx;
  height: 48rpx;
}

.activeInfo{
  position: absolute;
  top: -60rpx;
  right: 20rpx;
  width: 80rpx;
  height: 80rpx;
}

.activeInfo image{
  width: 100%;
  height: 100%;
}

/* 卡片 */
.card{
  border-radius: 20rpx 20rpx 0 0;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 9999;
  background-color: #fff;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.card-header{
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 30rpx 20rpx;
  position: relative;
}

.close {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background-color: #e4e4e4;
  color: #7c7c7c;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
}

.card-header view:nth-child(2){
  font-size: 32rpx;
  font-weight: 500;
}

.card-header view:nth-child(3){
  font-size: 24rpx;
  color: #b2b2b2;
  background-color: #f4f4f4;
  padding: 6rpx 12rpx;
  border-radius: 10rpx;
}

.card-content{
  margin: 40rpx 0;
  text-align: center;
}

.card-content view:nth-child(1){
  font-size: 36rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.card-content view:nth-child(2){
  font-size: 28rpx;
  color: #666;
}

.join-now{
  margin: 0 40rpx 40rpx;
  padding: 24rpx 0;
  text-align: center;
  border-radius: 100rpx;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
}

.mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99;
}