// component/accountSelector/accountSelector.js
const app = getApp();

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 账户数据
    accounts: {
      type: Array,
      value: []
    },
    // 显示模式：list（列表）或 card（卡片）
    mode: {
      type: String,
      value: 'list'
    },
    // 是否显示
    show: {
      type: Boolean,
      value: false
    },
    // 主题颜色
    themeColor: {
      type: String,
      value: '#4BA3E1'
    },
    // 是否显示设置按钮
    showSettings: {
      type: Boolean,
      value: false
    },
    // 设置按钮图标
    settingsIcon: {
      type: String,
      value: '/static/icon/card.png'
    },
    // 当前选中的账户ID
    selectedAccountId: {
      type: Number,
      value: 0
    },
    // 自定义动画
    animation: {
      type: Object,
      value: {}
    },
    // 借入借出类型：1-借入，2-借出
    borrowType: {
      type: Number,
      value: 0
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 默认账户数据，如果没有传入则使用这个
    defaultAccounts: [
      { id: 1, name: '12', subName: '微信钱包', amount: '¥ 800.00', icon: '/static/icon/wx.png', selected: true },
      { id: 2, name: '支付宝', subName: '支付宝', amount: '¥ 980.00', icon: '/static/icon/zfb.png', selected: false },
    ],
    // 所有账户数据
    allAccounts: []
  },

  lifetimes: {
    attached: function () {
      // 在组件实例进入页面节点树时执行
      // 获取全局主题颜色
      if (app && app.globalData && app.globalData.selectedColor) {
        this.setData({
          themeColor: app.globalData.selectedColor
        });
      }

      // 初始化处理账户数据
      this.processAccountData();
    }
  },

  observers: {
    'accounts, selectedAccountId': function(accounts, selectedAccountId) {
      // 当accounts属性或selectedAccountId变化时，重新处理账户数据
      this.processAccountData();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 切换显示模式（列表/卡片）
    toggleMode: function () {
      const newMode = this.data.mode === 'list' ? 'card' : 'list';
      this.setData({
        mode: newMode
      });
      // 触发模式变化事件
      this.triggerEvent('modeChange', { mode: newMode });
    },

    // 关闭选择器
    closeSelector: function () {
      this.setData({
        show: false
      });
      // 触发关闭事件
      this.triggerEvent('close');
    },

    // 选择账户
    selectAccount: function (e) {
      const index = e.currentTarget.dataset.index;
      const account = this.data.allAccounts[index];

      // 更新选中状态
      let accounts = [...this.data.allAccounts];
      accounts.forEach((item, idx) => {
        item.selected = idx === index;
      });

      this.setData({
        allAccounts: accounts
      });

      // 获取当前页面上下文，用于判断是从哪里打开的账户选择器
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      
      // 判断当前账户选择器是从哪个场景打开的
      let accountSource = 'normal';
      
      // 检查是否存在 currentAccountType 来判断来源
      if (currentPage && currentPage.data) {
        if (currentPage.data.currentAccountType === 'borrowing') {
          accountSource = 'borrowing';
        } else if (currentPage.data.currentAccountType === 'asset') {
          accountSource = 'asset';
        } else if (currentPage.data.currentAccountType === 'from' || currentPage.data.currentAccountType === 'to') {
          accountSource = 'transfer';
        }
      }
      
      console.log('账户选择来源:', accountSource);
      
      // 根据不同场景处理选中的账户
      const selectedAccount = {
        ...account,
        isLastAccount: false
      };

      // 触发选择事件
      this.triggerEvent('select', { account: selectedAccount });
    },

    // 添加新账户
    addAccount: function () {
      // 触发添加事件
      this.triggerEvent('add');
    },

    // 刷新数据
    refreshData: function () {
      // 触发刷新事件
      this.triggerEvent('refresh');
    },

    // 阻止冒泡
    preventBubble: function () {
      // 阻止点击内容区域时关闭弹窗
      return;
    },

    // 设置按钮点击事件
    onSettingsClick: function () {
      // 触发设置事件
      this.triggerEvent('settings');
    },

    // 处理账户数据
    processAccountData: function () {
      const sourceAccounts = this.data.accounts.length > 0 ? this.data.accounts : this.data.defaultAccounts;
      const selectedId = this.data.selectedAccountId;
      
      console.log('处理账户数据，选中ID:', selectedId);

      // 提取所有账户
      const allAccounts = [...sourceAccounts];

      // 根据selectedAccountId更新选中状态
        allAccounts.forEach(account => {
          account.selected = account.id === selectedId;
        });

      console.log('处理后的账户数据:', allAccounts);

      this.setData({
        allAccounts: allAccounts
      });
    }
  }
})
